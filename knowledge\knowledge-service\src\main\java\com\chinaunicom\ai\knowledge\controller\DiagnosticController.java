package com.chinaunicom.ai.knowledge.controller;

import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.util.ElasticsearchDiagnosticUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 诊断控制器
 * 用于验证混合检索配置和分词器效果
 * 仅在开发环境启用
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-11
 */
@Slf4j
@RestController
@RequestMapping("/api/diagnostic")
@AllArgsConstructor
@Tag(name = "诊断接口", description = "用于验证混合检索配置和分词器效果的诊断接口")
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class DiagnosticController {

    private final ElasticsearchDiagnosticUtil diagnosticUtil;
    private final HybridSearchConfig hybridSearchConfig;
    private final KnowledgeBaseMapper knowledgeBaseMapper;

    @GetMapping("/check-config")
    @Operation(summary = "检查混合检索配置", description = "验证当前的混合检索配置是否正确加载")
    public Map<String, Object> checkHybridSearchConfig() {
        log.info("🔍 检查混合检索配置");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("enabled", hybridSearchConfig.getEnabled());
            result.put("keywordWeight", hybridSearchConfig.getKeywordWeight());
            result.put("vectorWeight", hybridSearchConfig.getVectorWeight());
            result.put("analyzer", hybridSearchConfig.getAnalyzer());
            result.put("minimumShouldMatch", hybridSearchConfig.getMinimumShouldMatch());
            result.put("isValid", hybridSearchConfig.isValid());
            
            log.info("📋 当前混合检索配置:");
            log.info("  - enabled: {}", hybridSearchConfig.getEnabled());
            log.info("  - keywordWeight: {}", hybridSearchConfig.getKeywordWeight());
            log.info("  - vectorWeight: {}", hybridSearchConfig.getVectorWeight());
            log.info("  - analyzer: {}", hybridSearchConfig.getAnalyzer());
            log.info("  - minimumShouldMatch: {}", hybridSearchConfig.getMinimumShouldMatch());
            log.info("  - isValid: {}", hybridSearchConfig.isValid());
            
            result.put("status", "success");
            result.put("message", "配置检查完成");
            
        } catch (Exception e) {
            log.error("检查混合检索配置失败: {}", e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "配置检查失败: " + e.getMessage());
        }
        
        return result;
    }

    @GetMapping("/check-index-mapping/{kbId}")
    @Operation(summary = "检查索引mapping配置", description = "验证指定知识库的Elasticsearch索引mapping配置")
    public Map<String, Object> checkIndexMapping(
            @Parameter(description = "知识库ID") @PathVariable String kbId) {
        
        log.info("🔍 检查索引mapping配置 - 知识库ID: {}", kbId);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(kbId);
            if (knowledgeBase == null) {
                result.put("status", "error");
                result.put("message", "知识库不存在: " + kbId);
                return result;
            }
            
            String indexName = knowledgeBase.getIndexName();
            log.info("📋 知识库信息: 名称={}, 索引={}", knowledgeBase.getName(), indexName);
            
            // 检查索引mapping
            diagnosticUtil.checkIndexMapping(indexName);
            
            result.put("status", "success");
            result.put("message", "索引mapping检查完成，详细信息请查看日志");
            result.put("knowledgeBaseName", knowledgeBase.getName());
            result.put("indexName", indexName);
            
        } catch (Exception e) {
            log.error("检查索引mapping配置失败 - 知识库ID: {}, 错误: {}", kbId, e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "索引mapping检查失败: " + e.getMessage());
        }
        
        return result;
    }

    @PostMapping("/test-tokenization/{kbId}")
    @Operation(summary = "测试分词效果", description = "测试指定文本在指定知识库索引中的分词效果")
    public Map<String, Object> testTokenization(
            @Parameter(description = "知识库ID") @PathVariable String kbId,
            @Parameter(description = "测试文本") @RequestBody String text) {

        log.info("🔍 测试分词效果 - 知识库ID: {}, 文本: '{}'", kbId, text);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(kbId);
            if (knowledgeBase == null) {
                result.put("status", "error");
                result.put("message", "知识库不存在: " + kbId);
                return result;
            }

            String indexName = knowledgeBase.getIndexName();

            // 测试分词效果
            Map<String, Object> tokenizationResult = diagnosticUtil.testTokenization(indexName, text);

            result.put("status", tokenizationResult.get("status"));
            result.put("message", "分词测试完成");
            result.put("knowledgeBaseName", knowledgeBase.getName());
            result.put("indexName", indexName);
            result.put("testText", text);
            result.put("tokenizationResult", tokenizationResult);

        } catch (Exception e) {
            log.error("测试分词效果失败 - 知识库ID: {}, 文本: '{}', 错误: {}",
                    kbId, text, e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "分词测试失败: " + e.getMessage());
        }

        return result;
    }

    @PostMapping("/compare-analyzers")
    @Operation(summary = "对比分词器效果", description = "对比standard和ik_max_word分词器的分词效果")
    public Map<String, Object> compareAnalyzers(
            @Parameter(description = "测试文本") @RequestBody String text) {

        log.info("🔍 对比分词器效果 - 文本: '{}'", text);

        try {
            // 对比分词器效果
            Map<String, Object> comparisonResult = diagnosticUtil.compareAnalyzers(text);
            return comparisonResult;

        } catch (Exception e) {
            log.error("对比分词器效果失败 - 文本: '{}', 错误: {}", text, e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "error");
            result.put("message", "分词器对比失败: " + e.getMessage());
            return result;
        }
    }

    @GetMapping("/check-ik-plugin")
    @Operation(summary = "检查IK分词器插件", description = "验证Elasticsearch是否正确安装了IK分词器插件")
    public Map<String, Object> checkIkPlugin() {
        log.info("🔍 检查IK分词器插件");

        try {
            Map<String, Object> pluginCheckResult = diagnosticUtil.checkIkPlugin();
            return pluginCheckResult;

        } catch (Exception e) {
            log.error("检查IK分词器插件失败: {}", e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "error");
            result.put("message", "IK分词器插件检查失败: " + e.getMessage());
            return result;
        }
    }

    @PostMapping("/rebuild-index-with-analyzer/{kbId}")
    @Operation(summary = "重建索引应用新分词器", description = "删除并重新创建索引以应用新的ik_max_word分词器配置")
    public Map<String, Object> rebuildIndexWithAnalyzer(
            @Parameter(description = "知识库ID") @PathVariable String kbId) {

        log.info("🔧 开始重建索引应用新分词器 - 知识库ID: {}", kbId);

        Map<String, Object> result = new HashMap<>();

        try {
            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(kbId);
            if (knowledgeBase == null) {
                result.put("status", "error");
                result.put("message", "知识库不存在: " + kbId);
                return result;
            }

            String indexName = knowledgeBase.getIndexName();
            log.info("📋 准备重建索引: {}", indexName);

            // 警告：这将删除所有数据
            log.warn("⚠️ 警告：重建索引将删除所有现有数据，需要重新导入文档");

            result.put("status", "warning");
            result.put("message", "重建索引功能已准备就绪，但需要手动确认。请使用知识库管理界面删除并重新创建知识库，或联系管理员执行索引重建操作。");
            result.put("knowledgeBaseName", knowledgeBase.getName());
            result.put("indexName", indexName);
            result.put("warning", "此操作将删除所有现有数据，请确保已备份重要数据");

        } catch (Exception e) {
            log.error("重建索引失败 - 知识库ID: {}, 错误: {}", kbId, e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "重建索引失败: " + e.getMessage());
        }

        return result;
    }
}
