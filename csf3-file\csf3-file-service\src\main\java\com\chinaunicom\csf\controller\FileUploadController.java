package com.chinaunicom.csf.controller;

import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.dto.CompleteMultipartUploadRequest;
import com.chinaunicom.csf.dto.FileMetadataDTO;
import com.chinaunicom.csf.dto.FileUploadRequest;
import com.chinaunicom.csf.dto.InitMultipartUploadRequest;
import com.chinaunicom.csf.dto.InitMultipartUploadResponse;
import com.chinaunicom.csf.dto.UploadPartRequest;
import com.chinaunicom.csf.service.IFileUploadService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/file")
@Tag(name = "文件服务")
public class FileUploadController {

    private final IFileUploadService fileUploadService;

    /**
     * 上传文件
     *
     * @param file    文件
     * @param bizType 业务类型
     * @return 文件元数据
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件")
    public R<FileMetadataDTO> uploadFile(@RequestParam("file") MultipartFile file,
                                         @RequestParam(value = "bizType", required = false) String bizType) {
        try {
            FileUploadRequest request = new FileUploadRequest();
            request.setFile(file);
            request.setBizType(bizType);
            FileMetadataDTO fileMetadata = fileUploadService.uploadFile(request);
            return R.data(fileMetadata);
        } catch (Exception e) {
            log.error("文件上传失败: ", e);
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 初始化分片上传
     *
     * @param request 初始化分片上传请求
     * @return 初始化分片上传响应
     */
    @PostMapping("/multipart/init")
    @Operation(summary = "初始化分片上传")
    public R<InitMultipartUploadResponse> initMultipartUpload(@RequestBody InitMultipartUploadRequest request) {
        try {
            InitMultipartUploadResponse response = fileUploadService.initMultipartUpload(request);
            return R.data(response);
        } catch (Exception e) {
            log.error("初始化分片上传失败: ", e);
            return R.fail("初始化分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件分片
     *
     * @param file       分片文件
     * @param fileId     文件ID
     * @param objectName MinIO 中的对象名
     * @param bucketName 存储的桶名
     * @param uploadId   Upload ID
     * @param partNumber 分片编号
     * @return etag
     */
    @PostMapping("/multipart/uploadPart")
    @Operation(summary = "上传文件分片")
    public R<String> uploadPart(@RequestParam("file") MultipartFile file,
                                @RequestParam("fileId") String fileId,
                                @RequestParam("objectName") String objectName,
                                @RequestParam("bucketName") String bucketName,
                                @RequestParam("uploadId") String uploadId,
                                @RequestParam("partNumber") Integer partNumber) {
        try {
            UploadPartRequest request = new UploadPartRequest();
            request.setFileId(fileId);
            request.setObjectName(objectName);
            request.setBucketName(bucketName);
            request.setUploadId(uploadId);
            request.setPartNumber(partNumber);
            request.setPartFile(file);
            String etag = fileUploadService.uploadPart(request);
            return R.data(etag);
        } catch (Exception e) {
            log.error("上传文件分片失败: ", e);
            return R.fail("上传文件分片失败: " + e.getMessage());
        }
    }

    /**
     * 完成分片上传
     *
     * @param request 完成分片上传请求
     * @return 文件元数据
     */
    @PostMapping("/multipart/complete")
    @Operation(summary = "完成分片上传")
    public R<FileMetadataDTO> completeMultipartUpload(@RequestBody CompleteMultipartUploadRequest request) {
        try {
            FileMetadataDTO fileMetadata = fileUploadService.completeMultipartUpload(request);
            return R.data(fileMetadata);
        } catch (Exception e) {
            log.error("完成分片上传失败: ", e);
            return R.fail("完成分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     * 支持中文文件名，确保在各种浏览器环境下正确显示
     *
     * @param fileId   文件ID
     * @param response HTTP响应
     */
    @GetMapping("/download/{fileId}")
    @Operation(summary = "下载文件")
    public void downloadFile(@PathVariable String fileId, HttpServletResponse response) {
        try {
            // 设置响应编码，确保中文文件名正确处理
            response.setCharacterEncoding("UTF-8");

            fileUploadService.downloadFile(fileId, response);

            log.info("文件下载请求处理完成: fileId={}", fileId);
        } catch (Exception e) {
            log.error("文件下载失败: fileId={}, error={}", fileId, e.getMessage(), e);

            // 设置错误响应
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");

            try {
                response.getWriter().write("{\"error\":\"文件下载失败: " + e.getMessage() + "\"}");
            } catch (Exception writeException) {
                log.error("写入错误响应失败: {}", writeException.getMessage());
            }
        }
    }

    /**
     * 获取文件元数据
     *
     * @param fileId 文件ID
     * @return 文件元数据
     */
    @GetMapping("/metadata/{fileId}")
    @Operation(summary = "获取文件元数据")
    public R<FileMetadataDTO> getFileMetadata(@PathVariable String fileId) {
        try {
            FileMetadataDTO fileMetadata = fileUploadService.getFileMetadata(fileId);
            if (fileMetadata == null) {
                return R.fail("文件元数据未找到");
            }
            return R.data(fileMetadata);
        } catch (Exception e) {
            log.error("获取文件元数据失败: ", e);
            return R.fail("获取文件元数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件直接访问URL
     *
     * @param fileId 文件ID
     * @return 文件直接访问URL
     */
    @GetMapping("/accessUrl/{fileId}")
    @Operation(summary = "获取文件直接访问URL")
    public R<String> getFileAccessUrl(@PathVariable String fileId) {
        try {
            String url = fileUploadService.getFileAccessUrl(fileId);
            return R.data(url);
        } catch (Exception e) {
            log.error("获取文件直接访问URL失败: ", e);
            return R.fail("获取文件直接访问URL失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileIds 文件ID列表
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除文件")
    public R<Boolean> deleteFiles(@RequestBody List<String> fileIds) {
        try {
            fileUploadService.deleteFiles(fileIds);
            return R.status(true);
        } catch (Exception e) {
            log.error("删除文件失败: ", e);
            return R.fail("删除文件失败: " + e.getMessage());
        }
    }
} 