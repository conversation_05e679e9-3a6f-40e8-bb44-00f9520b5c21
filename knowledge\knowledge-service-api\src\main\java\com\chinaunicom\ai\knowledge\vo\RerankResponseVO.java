package com.chinaunicom.ai.knowledge.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Rerank接口响应VO
 * 对应算法端rerank接口的返回结果
 */
@Data
@Schema(description = "Rerank接口响应VO")
public class RerankResponseVO {
    
    @Schema(description = "状态码", example = "200")
    private String code;
    
    @Schema(description = "描述", example = "success")
    private String msg;
    
    @Schema(description = "返回重排分数")
    @JsonProperty("data")
    private QueryResult data;
    
    @Data
    @Schema(description = "查询结果")
    public static class QueryResult {
        @Schema(description = "分数列表，与传入的texts一一对应")
        private List<Float> rank;

        @Schema(description = "消耗的token数，无实际意义，可忽略")
        @JsonProperty("token_count")
        private Integer tokenCount;
    }
}
