package com.chinaunicom.ai.knowledge.service.impl;

import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.service.AiModelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.ai.knowledge.vo.AiModelDropdownVO;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:模型表 服务实现类
 * @date 2025/6/3 17:14
 */
@Service
public class AiModelServiceImpl extends ServiceImpl<AiModelMapper, AiModel> implements AiModelService {
    /**
     * 根据模型类型查询AI模型用于下拉框展示
     * @param modelType 模型类型 (1.大语言模型 2.向量嵌入模型)
     * @return 模型名称列表
     */
    @Override
    public List<AiModelDropdownVO> getModelsForDropdown(Integer modelType) {
        String tenantId = SecureUtil.getTenantId();
        List<AiModel> models = lambdaQuery()
                .eq(AiModel::getModelType, modelType)
                .eq(AiModel::getTenantId, tenantId)
                .eq(AiModel::getIsDeleted, 0) // 只查询未删除的模型
                .list();
        return models.stream()
                .map(model -> {
                    AiModelDropdownVO vo = new AiModelDropdownVO();
                    vo.setId(model.getId());
                    vo.setDisplayName(model.getDisplayName());
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
