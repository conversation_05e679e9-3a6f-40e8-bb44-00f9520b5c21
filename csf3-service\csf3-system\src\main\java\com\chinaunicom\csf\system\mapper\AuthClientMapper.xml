<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.csf.system.mapper.AuthClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="clientResultMap" type="com.chinaunicom.csf.system.entity.AuthClient">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="client_id" property="clientId"/>
        <result column="client_secret" property="clientSecret"/>
        <result column="resources_ids" property="resourceIds"/>
        <result column="scope" property="scope"/>
        <result column="authorized_grant_types" property="authorizedGrantTypes"/>
        <result column="web_server_redirect_uri" property="webServerRedirectUri"/>
        <result column="authorities" property="authorities"/>
        <result column="access_token_validity" property="accessTokenValidity"/>
        <result column="refresh_token_validity" property="refreshTokenValidity"/>
        <result column="additional_information" property="additionalInformation"/>
        <result column="autoapprove" property="autoapprove"/>
    </resultMap>

</mapper>
