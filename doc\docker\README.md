# 快速体验CSF微服版

## 环境准备
### 基本组件
- MySQL: 5.7.23
- Nacos: 2.0.4
- Redis: 5.0.3
- Sentinel: 1.8.0

### 日志组件(非必选)
- elasticsearch: 7.10.0
- logstash: 7.10.0
- kibana: 7.10.0

## csf微服务运行
### 微服务说明
基础服务主要有以下微服务，建议结合[CSF Admin（vue3）](https://gitlab.uniin.cn/csf/csf-admin)PC管理端页面使用，完整功能分支：feature/完善基础功能)
```
csf-user 
csf-admin 
csf-auth 
csf-desk
csf-system 
csf-report 
csf-log 
csf-develop 
csf-gateway 
csf-swagger
```

### 运行命令参考
```bash
# 1.启动基本组件
# 启动成功之后，通过navicat或其他方式导入csf数据库
./deploy.sh base

# 2.打包编译项目
./deploy.sh mvnJars

# 3.运行csf微服务
./deploy.sh modules
```
后续。开发测试可以使用`./deploy.sh all`快速启动

### 日志功能、前端测试
在nacos上配置[开启ELK](https://itbase.uniin.cn:31746/docs/docs/csf-backend-train/csf.html#%E6%8E%A5%E5%85%A5elk)
```bash
./deploy.sh elk
```
将[CSF Admin（vue3）](https://gitlab.uniin.cn/csf/csf-admin)PC管理端页面打包之后，拷贝dist文件夹所有内容到nginx/html目录下
```bash
./deploy.sh front
```
之后，可以访问http://localhost:8077登录使用，初始化账号admin / Csf@123456
