version : '3.8'
services:
  csf-nacos:
    container_name: csf3-nacos
    image: nacos/nacos-server:2.0.4
    build:
      context: nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - csf3-mysql

  csf-mysql:
    container_name: csf3-mysql
    image: mysql:5.7.23
    build:
      context: mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/mysql.conf.d:/etc/mysql/mysql.conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
          'mysqld',
          '--innodb-buffer-pool-size=80M',
          '--character-set-server=utf8mb4',
          '--collation-server=utf8mb4_unicode_ci',
          '--default-time-zone=+8:00',
          '--lower-case-table-names=1'
        ]
    environment:
      MYSQL_DATABASE: 'csf'
      MYSQL_ROOT_PASSWORD: Csf@123456

  csf-redis:
    container_name: csf3-redis
    image: redis:5.0.3
    build:
      context: redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf

  csf-sentinel:
    image: csf-sentinel-dashboard:1.8.7
    container_name: csf3-sentinel
    environment:
      - "SENTINEL_NACOS_SERVERADDR=csf-nacos:8848"
      - "SENTINEL_NACOS_NAMESPACE=032d1419-b3c4-48e9-bc0e-f83997dae13c"
      - "SENTINEL_NACOS_USERNAME=nacos"
      - "SENTINEL_NACOS_PASSWORD=nacos"
      - "AUTH_USERNAME=sentinel"
      - "AUTH_PASSWORD=sentinel"
    ports:
      - 8858:8858

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.10.0
    container_name: elasticsearch
    environment:
      - "discovery.type=single-node"
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.10.0
    container_name: logstash
    environment:
      - "LS_JAVA_OPTS=-Xms512m -Xmx512m"
      - "LOGSTASH_SETTINGS={\"pipeline.workers\": 1,\"pipeline.batch.size\": 125}"
    depends_on:
      - elasticsearch
    ports:
      - "5066:5066"
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./logstash/pipeline/tcp.conf:/usr/share/logstash/pipeline/tcp.conf
      - ./logstash/logs:/var/log/logstash

  kibana:
    image: docker.elastic.co/kibana/kibana:7.10.0
    container_name: kibana
    depends_on:
      - elasticsearch
    ports:
      - "5601:5601"

  minio:
    image: minio/minio:RELEASE.2022-05-04T07-45-27Z
    container_name: minio
    privileged: true
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - /usr/local/minion/data:/data
      - /usr/local/minio/config:/root/.minio
    command: server --console-address ":9000" --address ":9001" /data

  csf-nginx:
    container_name: csf-nginx
    image: nginx:1.23.4
    build:
      context: nginx
    ports:
      - "8077:80"
    volumes:
      - ./nginx/html/dist:/usr/share/nginx/html/csf-admin-vue3
      - ./nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/logs:/var/log/nginx

  csf-gateway:
    container_name: csf-gateway
    build:
      context: csf/csf-gateway
      dockerfile: csf/csf-gateway/dockerfile
    ports:
      - "80:80"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-auth:
    container_name: csf-auth
    build:
      context: csf/csf-auth
      dockerfile: csf/csf-auth/dockerfile
    ports:
      - "8100:8100"
    environment:
      - CSF_DEV_REDIS_HOST=${CSF_DEV_REDIS_HOST}
      - CSF_DEV_REDIS_PORT=${CSF_DEV_REDIS_PORT}
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-admin:
    container_name: csf-admin
    build:
      context: csf/csf-ops/csf-admin
      dockerfile: csf/csf-ops/csf-admin/dockerfile
    ports:
      - "7002:7002"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-develop:
    container_name: csf-develop
    build:
      context: csf/csf-ops/csf-develop
      dockerfile: csf/csf-ops/csf-develop/dockerfile
    ports:
      - "7007:7007"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-mq-consumer:
    container_name: csf-mq-consumer
    build:
      context: csf/csf-ops/csf-mq-consumer
      dockerfile: csf/csf-ops/csf-mq-consumer/dockerfile
    ports:
      - "8899:8899"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-mq-producer:
    container_name: csf-mq-producer
    build:
      context: csf/csf-ops/csf-mq-producer
      dockerfile: csf/csf-ops/csf-mq-producer/dockerfile
    ports:
      - "8999:8999"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-report:
    container_name: csf-report
    build:
      context: csf/csf-ops/csf-report
      dockerfile: csf/csf-ops/csf-report/dockerfile
    ports:
      - "8108:8108"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-resource:
    container_name: csf-resource
    build:
      context: csf/csf-ops/csf-resource
      dockerfile: csf/csf-ops/csf-resource/dockerfile
    ports:
      - "8010:8010"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-seata-order:
    container_name: csf-seata-order
    build:
      context: csf/csf-ops/csf-seata-order
      dockerfile: csf/csf-ops/csf-seata-order/dockerfile
    ports:
      - "8501:8501"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-seata-storage:
    container_name: csf-seata-storage
    build:
      context: csf/csf-ops/csf-seata-storage
      dockerfile: csf/csf-ops/csf-seata-storage/dockerfile
    ports:
      - "8502:8502"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-swagger:
    container_name: csf-swagger
    build:
      context: csf/csf-ops/csf-swagger
      dockerfile: csf/csf-ops/csf-swagger/dockerfile
    ports:
      - "18000:18000"
  csf-demo:
    container_name: csf-demo
    build:
      context: csf/csf-service/csf-demo
      dockerfile: csf/csf-service/csf-demo/dockerfile
    ports:
      - "8200:8200"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-desk:
    container_name: csf-desk
    build:
      context: csf/csf-service/csf-desk
      dockerfile: csf/csf-service/csf-desk/dockerfile
    ports:
      - "8105:8105"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-log:
    container_name: csf-log
    build:
      context: csf/csf-service/csf-log
      dockerfile: csf/csf-service/csf-log/dockerfile
    ports:
      - "8103:8103"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-system:
    container_name: csf-system
    build:
      context: csf/csf-service/csf-system
      dockerfile: csf/csf-service/csf-system/dockerfile
    ports:
      - "8106:8106"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}

  csf-user:
    container_name: csf-user
    build:
      context: csf/csf-service/csf-user
      dockerfile: csf/csf-service/csf-user/dockerfile
    ports:
      - "8102:8102"
    environment:
      - DISCOVERY_SERVER_ENDPOINT=${DISCOVERY_SERVER_ENDPOINT}
      - CONFIG_SERVER_ENDPOINT=${CONFIG_SERVER_ENDPOINT}
