package com.chinaunicom.ai.knowledge.evaluation.controller;

import com.chinaunicom.ai.knowledge.evaluation.dto.CleanupResult;
import com.chinaunicom.ai.knowledge.evaluation.dto.EvaluationResult;
import com.chinaunicom.ai.knowledge.evaluation.dto.ImportResult;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationRequest;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationResult;
import com.chinaunicom.ai.knowledge.evaluation.service.EvaluationCleanupService;
import com.chinaunicom.ai.knowledge.evaluation.service.EvaluationDataImporter;
import com.chinaunicom.ai.knowledge.evaluation.service.EvaluationTestService;
import com.chinaunicom.ai.knowledge.evaluation.service.FullEvaluationService;
import com.chinaunicom.csf.core.tool.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

/**
 * 评测控制器
 * 提供数据导入和评测功能的API接口
 */
@Slf4j
@RestController
@RequestMapping("/evaluation")
@RequiredArgsConstructor
@Tag(name = "知识库评测", description = "基于1doc_QA数据集的知识库检索质量评测")
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationController {

    private final EvaluationDataImporter dataImporter;
    private final EvaluationTestService testService;
    private final EvaluationCleanupService cleanupService;
    private final FullEvaluationService fullEvaluationService;
    
    @Operation(summary = "导入评测数据集", description = "从1doc_QA.json导入数据到指定知识库")
    @PostMapping("/import-dataset")
    public R<ImportResult> importDataset(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId,
            @Parameter(description = "最大导入数量，不指定则导入全部")
            @RequestParam(required = false) Integer maxCount) {
        
        log.info("开始导入评测数据集，知识库ID: {}, 最大数量: {}", knowledgeBaseId, maxCount);
        
        try {
            ImportResult result = dataImporter.importDataset(knowledgeBaseId, maxCount);
            
            if ("SUCCESS".equals(result.getStatus())) {
                log.info("数据导入成功，知识库ID: {}, 成功数量: {}", knowledgeBaseId, result.getSuccessCount());
                return R.data(result, "数据导入成功");
            } else if ("PARTIAL".equals(result.getStatus())) {
                log.warn("数据部分导入成功，知识库ID: {}, 成功数量: {}, 失败数量: {}", 
                        knowledgeBaseId, result.getSuccessCount(), result.getFailedCount());
                return R.data(result, "数据部分导入成功");
            } else {
                log.error("数据导入失败，知识库ID: {}, 错误: {}", knowledgeBaseId, result.getErrorMessage());
                return R.fail(result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("数据导入异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("数据导入异常: " + e.getMessage());
        }
    }
    
    @Operation(summary = "执行评测测试", description = "对指定知识库执行完整的检索质量评测")
    @PostMapping("/run-evaluation")
    public R<EvaluationResult> runEvaluation(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId) {
        
        log.info("开始执行评测测试，知识库ID: {}", knowledgeBaseId);
        
        try {
            EvaluationResult result = testService.runEvaluation(knowledgeBaseId);
            
            log.info("评测测试完成，知识库ID: {}, 召回率: {:.2f}%, 平均准确率: {:.2f}%",
                    knowledgeBaseId, result.getRecallRate() * 100, result.getAverageAccuracy() * 100);
            
            return R.data(result, "评测测试完成");
            
        } catch (Exception e) {
            log.error("评测测试异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("评测测试异常: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取评测统计", description = "获取指定知识库的评测统计信息")
    @GetMapping("/statistics")
    public R<EvaluationTestService.EvaluationStatistics> getStatistics(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId) {
        
        try {
            EvaluationTestService.EvaluationStatistics stats = testService.getEvaluationStatistics(knowledgeBaseId);
            return R.data(stats, "获取统计信息成功");
            
        } catch (Exception e) {
            log.error("获取评测统计异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("获取统计信息异常: " + e.getMessage());
        }
    }
    
    @Operation(summary = "获取数据集信息", description = "获取1doc_QA数据集的基本信息")
    @GetMapping("/dataset-info")
    public R<EvaluationDataImporter.DatasetInfo> getDatasetInfo() {
        try {
            EvaluationDataImporter.DatasetInfo info = dataImporter.getDatasetInfo();
            if (info != null) {
                return R.data(info, "获取数据集信息成功");
            } else {
                return R.fail("无法读取数据集信息");
            }
        } catch (Exception e) {
            log.error("获取数据集信息异常", e);
            return R.fail("获取数据集信息异常: " + e.getMessage());
        }
    }

    @Operation(summary = "清理评测数据", description = "清理指定知识库的所有评测相关数据")
    @DeleteMapping("/cleanup")
    public R<CleanupResult> cleanupEvaluationData(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId,
            @Parameter(description = "是否清理MinIO文件", required = false)
            @RequestParam(defaultValue = "true") Boolean cleanupFiles,
            @Parameter(description = "是否清理Elasticsearch数据", required = false)
            @RequestParam(defaultValue = "true") Boolean cleanupIndex) {

        log.info("开始清理评测数据，知识库ID: {}, 清理文件: {}, 清理索引: {}",
                knowledgeBaseId, cleanupFiles, cleanupIndex);

        try {
            CleanupResult result = cleanupService.cleanupEvaluationData(
                    knowledgeBaseId, cleanupFiles, cleanupIndex);

            log.info("评测数据清理完成，知识库ID: {}, 清理结果: {}", knowledgeBaseId, result);
            return R.data(result, "评测数据清理完成");

        } catch (Exception e) {
            log.error("评测数据清理异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("评测数据清理异常: " + e.getMessage());
        }
    }

    @Operation(summary = "清理评测历史", description = "仅清理评测历史记录，保留文档数据")
    @DeleteMapping("/cleanup-history")
    public R<Integer> cleanupTestHistory(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId) {

        log.info("开始清理评测历史，知识库ID: {}", knowledgeBaseId);

        try {
            int deletedCount = cleanupService.cleanupTestHistory(knowledgeBaseId);

            log.info("评测历史清理完成，知识库ID: {}, 删除记录数: {}", knowledgeBaseId, deletedCount);
            return R.data(deletedCount, "评测历史清理完成");

        } catch (Exception e) {
            log.error("评测历史清理异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("评测历史清理异常: " + e.getMessage());
        }
    }

    @Operation(summary = "清理预览", description = "获取清理操作的预览信息")
    @GetMapping("/cleanup-preview")
    public R<EvaluationCleanupService.CleanupPreview> getCleanupPreview(
            @Parameter(description = "知识库ID", required = true)
            @RequestParam Long knowledgeBaseId) {

        try {
            EvaluationCleanupService.CleanupPreview preview = cleanupService.getCleanupPreview(knowledgeBaseId);
            return R.data(preview, "获取清理预览成功");

        } catch (Exception e) {
            log.error("获取清理预览异常，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("获取清理预览异常: " + e.getMessage());
        }
    }

    @Operation(summary = "端到端评测", description = "创建临时知识库，导入数据，向量化，评测，清理资源的完整流程")
    @PostMapping("/run-full-evaluation")
    public R<FullEvaluationResult> runFullEvaluation(@RequestBody FullEvaluationRequest request) {
        try {
            log.info("接收到端到端评测请求: {}", request);

            FullEvaluationResult result = fullEvaluationService.runFullEvaluation(request);

            if (result.getSuccess()) {
                log.info("端到端评测成功完成，性能摘要: {}", result.getPerformanceSummary());
                return R.data(result);
            } else {
                // 检查是否有部分成功的数据可以展示
                boolean hasUsefulData = hasUsefulEvaluationData(result);

                if (hasUsefulData) {
                    log.warn("端到端评测部分失败但有可用数据，性能摘要: {}", result.getPerformanceSummary());
                    // 返回成功状态但在消息中说明是部分成功
                    R<FullEvaluationResult> partialResult = R.data(result);
                    partialResult.setMsg("端到端评测部分成功: " + result.getErrorMessage());
                    return partialResult;
                } else {
                    log.error("端到端评测失败: {}", result.getErrorMessage());
                    R<FullEvaluationResult> failResult = R.fail("端到端评测失败: " + result.getErrorMessage());
                    failResult.setData(result);
                    return failResult;
                }
            }

        } catch (Exception e) {
            log.error("端到端评测异常", e);

            // 创建失败结果
            FullEvaluationResult failureResult = new FullEvaluationResult();
            failureResult.setSuccess(false);
            failureResult.setErrorMessage(e.getMessage());
            failureResult.setStartTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            failureResult.setEndTime(java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ISO_LOCAL_DATE_TIME));

            R<FullEvaluationResult> exceptionResult = R.fail("端到端评测异常: " + e.getMessage());
            exceptionResult.setData(failureResult);
            return exceptionResult;
        }
    }

    @Operation(summary = "检查知识库向量化状态", description = "检查指定知识库的向量化进度")
    @GetMapping("/vectorization-status")
    public R<FullEvaluationResult.VectorizationStats> getVectorizationStatus(
            @Parameter(description = "知识库ID") @RequestParam Long knowledgeBaseId) {
        try {
            log.info("检查知识库 {} 的向量化状态", knowledgeBaseId);

            FullEvaluationResult.VectorizationStats stats = fullEvaluationService.getVectorizationStats(knowledgeBaseId);

            return R.data(stats);

        } catch (Exception e) {
            log.error("检查向量化状态失败，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("检查向量化状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查知识库是否准备就绪", description = "检查知识库是否完成向量化并可以进行评测")
    @GetMapping("/knowledge-base-ready")
    public R<Boolean> isKnowledgeBaseReady(
            @Parameter(description = "知识库ID") @RequestParam Long knowledgeBaseId) {
        try {
            log.info("检查知识库 {} 是否准备就绪", knowledgeBaseId);

            boolean ready = fullEvaluationService.isKnowledgeBaseReady(knowledgeBaseId);

            return R.data(ready);

        } catch (Exception e) {
            log.error("检查知识库准备状态失败，知识库ID: {}", knowledgeBaseId, e);
            return R.fail("检查知识库准备状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查评测结果是否包含有用的数据
     * 即使整体失败，如果有部分成功的数据也值得展示
     */
    private boolean hasUsefulEvaluationData(FullEvaluationResult result) {
        // 检查是否有成功导入的文档
        if (result.getDocumentImportStats() != null &&
            result.getDocumentImportStats().getSuccessCount() != null &&
            result.getDocumentImportStats().getSuccessCount() > 0) {

            // 计算导入成功率
            double importSuccessRate = result.getDocumentImportStats().getTotalDocuments() > 0 ?
                (double) result.getDocumentImportStats().getSuccessCount() / result.getDocumentImportStats().getTotalDocuments() : 0.0;

            // 如果导入成功率超过50%，认为有有用的数据
            if (importSuccessRate >= 0.5) {
                return true;
            }
        }

        // 检查是否有评测结果
        if (result.getEvaluationResult() != null &&
            result.getEvaluationResult().getTotalQuestions() != null &&
            result.getEvaluationResult().getTotalQuestions() > 0) {
            return true;
        }

        // 检查是否有向量化统计
        if (result.getVectorizationStats() != null &&
            result.getVectorizationStats().getVectorizedCount() != null &&
            result.getVectorizationStats().getVectorizedCount() > 0) {
            return true;
        }

        return false;
    }
}
