package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 知识库文档召回请求体
 */
@Data
@Schema(description = "知识库文档召回请求体")
public class RecallDocumentsRequest {
    @Schema(description = "知识库ID列表", required = true, example = "[1,2,3]")
    private List<Long> kbIds;

    // 不再接收问题向量，改为接收问题文本
    @Schema(description = "问题文本", requiredMode = Schema.RequiredMode.REQUIRED, example = "什么是RAG模型？")
    private String queryText;

}
