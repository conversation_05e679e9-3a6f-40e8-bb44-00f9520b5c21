
package com.chinaunicom.csf.system.user.wrapper;

import com.chinaunicom.csf.core.mp.support.BaseEntityWrapper;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.BeanUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.core.tool.utils.SpringUtil;
import com.chinaunicom.csf.system.feign.IDictClient;
import com.chinaunicom.csf.system.feign.ISysClient;
import com.chinaunicom.csf.system.user.entity.User;
import com.chinaunicom.csf.system.user.service.IUserService;
import com.chinaunicom.csf.system.user.vo.UserVO;

import java.util.List;

/**
 * 包装类,返回视图层所需的字段
 *
 */
public class UserWrapper extends BaseEntityWrapper<User, UserVO> {

	private static IUserService userService;
	private static IDictClient dictClient;
	private static ISysClient sysClient;

	static {
		sysClient = SpringUtil.getBean(ISysClient.class);
		userService = SpringUtil.getBean(IUserService.class);
		dictClient = SpringUtil.getBean(IDictClient.class);
	}

	public static UserWrapper build() {
		return new UserWrapper();
	}

	@Override
	public UserVO entityVO(User user) {
		UserVO userVO = BeanUtil.copy(user, UserVO.class);
		if(Func.isNotEmpty(user.getRoleId())) {
			List<String> roleName = userService.getRoleName(user.getRoleId());
			if(roleName.size() > 0) {
				userVO.setRoleName(Func.join(roleName));
			}
		}
		if(Func.isNotEmpty(user.getDeptId())) {
			List<String> deptName = userService.getDeptName(user.getDeptId());
			if(deptName.size() > 0) {
				userVO.setDeptName(Func.join(deptName));
			}
		}
		if(Func.isNotEmpty(user.getPostId())) {
			List<String> postName = sysClient.getPostNames(user.getPostId());
			if(postName.size() > 0) {
				userVO.setPostName(Func.join(postName));
			}
		}
		R<String> dict = dictClient.getValue("sex", Func.toInt(user.getSex()));
		if (dict.isSuccess()) {
			userVO.setSexName(dict.getData());
		}
		return userVO;
	}
}
