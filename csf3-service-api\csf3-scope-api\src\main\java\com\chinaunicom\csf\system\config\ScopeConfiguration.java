
package com.chinaunicom.csf.system.config;


import lombok.AllArgsConstructor;
import com.chinaunicom.csf.core.datascope.handler.ScopeModelHandler;
import com.chinaunicom.csf.core.secure.config.RegistryConfiguration;
import com.chinaunicom.csf.system.handler.DataScopeModelHandler;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 公共封装包配置类
 *
 */
@Configuration
@AllArgsConstructor
@AutoConfigureBefore(RegistryConfiguration.class)
public class ScopeConfiguration {

	@Bean
	public ScopeModelHandler scopeModelHandler() {
		return new DataScopeModelHandler();
	}

}
