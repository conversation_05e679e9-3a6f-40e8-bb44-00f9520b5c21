package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.evaluation.dto.AccuracyDistribution;
import com.chinaunicom.ai.knowledge.evaluation.dto.AccuracyStatistics;
import com.chinaunicom.ai.knowledge.evaluation.dto.EvaluationResult;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationTestHistory;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationTestHistoryMapper;
import com.chinaunicom.ai.knowledge.service.KnowledgeBaseService;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import org.springframework.beans.factory.DisposableBean;

/**
 * 评测测试服务
 * 负责执行知识库检索质量评测
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationTestService implements DisposableBean {

    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final EvaluationTestHistoryMapper evaluationTestHistoryMapper;
    private final KnowledgeBaseService knowledgeBaseService;
    private final ObjectMapper objectMapper;

    // 并行处理配置
    private static final int THREAD_POOL_SIZE = 10; // 线程池大小
    private static final int BATCH_SAVE_SIZE = 10; // 批量保存大小，与线程池大小匹配以提高性能

    // 线程池，用于并行处理评测任务
    private final ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

    // 批量保存的测试历史记录缓存
    private final List<EvaluationTestHistory> batchHistoryCache = Collections.synchronizedList(new ArrayList<>());
    
    /**
     * 执行完整的评测测试（优化版本：并行处理 + 批量保存）
     */
    public EvaluationResult runEvaluation(Long knowledgeBaseId) {
        // 使用默认topK=5保持向后兼容性
        return runEvaluation(knowledgeBaseId, 5);
    }

    /**
     * 执行完整的评测测试（优化版本：并行处理 + 批量保存）
     * @param knowledgeBaseId 知识库ID
     * @param topK 召回文档数量
     */
    public EvaluationResult runEvaluation(Long knowledgeBaseId, Integer topK) {
        log.info("开始执行知识库评测（并行优化版本），知识库ID: {}, topK: {}", knowledgeBaseId, topK);

        LocalDateTime startTime = LocalDateTime.now();

        // 1. 获取所有评测文档
        List<EvaluationDocument> documents = evaluationDocumentMapper.selectByKnowledgeBaseId(knowledgeBaseId);
        if (documents.isEmpty()) {
            log.warn("知识库 {} 没有评测文档", knowledgeBaseId);
            return createEmptyResult(knowledgeBaseId, startTime);
        }

        // 过滤有效的文档
        List<EvaluationDocument> validDocuments = documents.stream()
                .filter(doc -> doc.getQuestions() != null && !doc.getQuestions().trim().isEmpty())
                .filter(doc -> doc.getOriginalDocId() != null && !doc.getOriginalDocId().trim().isEmpty())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        log.info("有效评测文档数量: {} / {}", validDocuments.size(), documents.size());

        // 2. 并行执行评测测试
        List<EvaluationResult.TestResult> testResults = executeTestsInParallel(validDocuments, knowledgeBaseId, topK);

        // 3. 统计结果
        AtomicInteger correctRecalls = new AtomicInteger(0);
        AtomicLong totalExecutionTime = new AtomicLong(0);
        AtomicLong totalAccuracyLong = new AtomicLong(0); // 使用long存储，避免double并发问题
        AtomicInteger validAccuracyCount = new AtomicInteger(0);

        testResults.parallelStream().forEach(testResult -> {
            if (testResult.getIsCorrect()) {
                correctRecalls.incrementAndGet();
            }
            totalExecutionTime.addAndGet(testResult.getExecutionTime());

            if (testResult.getAccuracy() != null) {
                // 将double转换为long进行原子操作，精度保持4位小数
                long accuracyLong = Math.round(testResult.getAccuracy() * 10000);
                totalAccuracyLong.addAndGet(accuracyLong);
                validAccuracyCount.incrementAndGet();
            }
        });

        // 4. 批量保存剩余的测试历史记录
        flushBatchHistoryCache();

        LocalDateTime endTime = LocalDateTime.now();

        // 5. 计算平均准确率
        double totalAccuracy = totalAccuracyLong.get() / 10000.0; // 恢复精度
        int validCount = validAccuracyCount.get();
        double averageAccuracy = validCount > 0 ? totalAccuracy / validCount : 0.0;

        // 6. 构建评测结果
        EvaluationResult result = new EvaluationResult();
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setTotalQuestions(testResults.size());
        result.setCorrectRecalls(correctRecalls.get());
        result.setRecallRate(testResults.size() > 0 ? (double) correctRecalls.get() / testResults.size() : 0.0);
        result.setAverageAccuracy(averageAccuracy);
        result.setAvgExecutionTime(testResults.size() > 0 ? (double) totalExecutionTime.get() / testResults.size() : 0.0);
        result.setTestResults(testResults);
        result.setStartTime(startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.setEndTime(endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.setTotalDuration(java.time.Duration.between(startTime, endTime).toMillis());

        // 7. 设置计算公式说明
        result.setRecallRateFormula("传统召回率 = 正确召回的问题数 / 总问题数 = " + correctRecalls.get() + " / " + testResults.size() + " = " + String.format("%.4f", result.getRecallRate()));
        result.setAverageAccuracyFormula(buildDetailedAverageAccuracyFormula(testResults, totalAccuracy, validCount, averageAccuracy));

        // 8. 计算详细的准确率统计信息
        AccuracyStatistics accuracyStats = calculateDetailedAccuracyStatistics(testResults, totalAccuracy, validCount, averageAccuracy);
        result.setAccuracyStatistics(accuracyStats);

        log.info("评测完成（并行优化），知识库ID: {}, 总问题数: {}, 正确召回数: {}, 召回率: {:.2f}%, 平均准确率: {:.2f}%",
                knowledgeBaseId, testResults.size(), correctRecalls.get(), result.getRecallRate() * 100, averageAccuracy * 100);

        return result;
    }

    /**
     * 并行执行测试
     *
     * @param documents 评测文档列表
     * @param knowledgeBaseId 知识库ID
     * @param topK 召回文档数量
     * @return 测试结果列表
     */
    private List<EvaluationResult.TestResult> executeTestsInParallel(List<EvaluationDocument> documents, Long knowledgeBaseId, Integer topK) {
        log.info("开始并行执行测试，文档数量: {}, topK: {}", documents.size(), topK);

        // 创建计数器，用于显示进度
        AtomicInteger completedCount = new AtomicInteger(0);
        int totalCount = documents.size();

        // 提交所有任务到线程池
        List<Future<EvaluationResult.TestResult>> futures = new ArrayList<>();
        for (EvaluationDocument document : documents) {
            futures.add(executorService.submit(() -> executeTestParallel(document, knowledgeBaseId, topK, completedCount, totalCount)));
        }

        // 收集结果，添加超时控制避免无限等待
        List<EvaluationResult.TestResult> results = new ArrayList<>(documents.size());
        for (Future<EvaluationResult.TestResult> future : futures) {
            try {
                // 添加5分钟超时控制，避免无限等待
                results.add(future.get(5, TimeUnit.MINUTES));
            } catch (TimeoutException e) {
                log.error("测试执行超时，取消任务", e);
                future.cancel(true); // 取消超时的任务
                results.add(createFailedTestResult("执行超时"));
            } catch (InterruptedException e) {
                log.error("测试执行被中断", e);
                Thread.currentThread().interrupt(); // 恢复中断状态
                future.cancel(true);
                results.add(createFailedTestResult("执行被中断"));
            } catch (ExecutionException e) {
                log.error("测试执行异常", e);
                results.add(createFailedTestResult("执行异常: " + e.getCause().getMessage()));
            }
        }

        log.info("并行测试完成，总数: {}, 成功: {}", totalCount, results.size());
        return results;
    }

    /**
     * 创建失败的测试结果
     */
    private EvaluationResult.TestResult createFailedTestResult(String reason) {
        EvaluationResult.TestResult failedResult = new EvaluationResult.TestResult();
        failedResult.setIsCorrect(false);
        failedResult.setAccuracy(0.0);
        failedResult.setRecallCount(0);
        failedResult.setRecalledDocIds(new ArrayList<>());
        failedResult.setSimilarityScores(new ArrayList<>());
        failedResult.setExecutionTime(0L);
        failedResult.setQuestion("测试失败: " + reason);
        failedResult.setExpectedDocId("N/A");
        return failedResult;
    }

    /**
     * 并行执行单个测试（线程安全版本）
     */
    private EvaluationResult.TestResult executeTestParallel(
            EvaluationDocument document,
            Long knowledgeBaseId,
            Integer topK,
            AtomicInteger completedCount,
            int totalCount) {

        long startTime = System.currentTimeMillis();

        EvaluationResult.TestResult testResult = new EvaluationResult.TestResult();
        testResult.setQuestion(document.getQuestions());
        testResult.setExpectedDocId(extractRealOriginalDocId(document.getOriginalDocId()));

        try {
            // 调用知识库检索服务，使用传递的topK参数
            List<RecallDocSegment> segments = knowledgeBaseService.recallDocuments(
                    Arrays.asList(knowledgeBaseId), document.getQuestions(), topK);

            // 提取召回的文档ID和分数
            List<String> recalledDocIds = new ArrayList<>();
            List<Double> similarityScores = new ArrayList<>();
            boolean isCorrect = false;
            int correctDocCount = 0; // 召回结果中正确文档的数量

            for (RecallDocSegment segment : segments) {
                // 通过知识库文档ID查找对应的原始文档ID
                EvaluationDocument evalDoc = evaluationDocumentMapper.selectByKnowledgeDocId(segment.getDocId());
                if (evalDoc != null) {
                    String originalDocId = evalDoc.getOriginalDocId();
                    // 处理重用文档的情况：去掉后缀获取真实的originalDocId
                    String realOriginalDocId = extractRealOriginalDocId(originalDocId);
                    recalledDocIds.add(realOriginalDocId);
                    similarityScores.add(segment.getScore().doubleValue());

                    // 检查是否召回了正确的文档
                    // 需要将document的originalDocId也去掉后缀进行比较
                    String realExpectedDocId = extractRealOriginalDocId(document.getOriginalDocId());
                    if (realExpectedDocId.equals(realOriginalDocId)) {
                        isCorrect = true;
                        correctDocCount++; // 统计正确文档数量
                    }
                }
            }

            // 计算单个问题的准确率
            double accuracy = 0.0;
            if (recalledDocIds.size() > 0) {
                accuracy = (double) correctDocCount / recalledDocIds.size();
            }

            testResult.setIsCorrect(isCorrect);
            testResult.setAccuracy(accuracy);
            testResult.setRecallCount(recalledDocIds.size());
            testResult.setRecalledDocIds(recalledDocIds);
            testResult.setSimilarityScores(similarityScores);

            long executionTime = System.currentTimeMillis() - startTime;
            testResult.setExecutionTime(executionTime);

            // 批量保存测试历史（线程安全）
            saveTestHistoryBatch(document, knowledgeBaseId, isCorrect, recalledDocIds, similarityScores, executionTime);

            // 更新进度并记录日志
            int completed = completedCount.incrementAndGet();
            if (completed % 10 == 0 || completed == totalCount) {
                log.info("测试进度: {}/{} ({}%)", completed, totalCount, Math.round((double) completed / totalCount * 100));
            }

        } catch (Exception e) {
            log.error("执行测试失败，文档ID: {}", document.getOriginalDocId(), e);

            testResult.setIsCorrect(false);
            testResult.setAccuracy(0.0); // 异常情况下准确率为0
            testResult.setRecallCount(0);
            testResult.setRecalledDocIds(new ArrayList<>());
            testResult.setSimilarityScores(new ArrayList<>());
            testResult.setExecutionTime(System.currentTimeMillis() - startTime);

            // 保存失败的测试历史
            saveFailedTestHistoryBatch(document, knowledgeBaseId, e.getMessage());

            // 更新进度
            completedCount.incrementAndGet();
        }

        return testResult;
    }

    /**
     * 批量保存测试历史记录（线程安全）
     */
    private void saveTestHistoryBatch(EvaluationDocument document, Long knowledgeBaseId, boolean isCorrect,
                                     List<String> recalledDocIds, List<Double> similarityScores, long executionTime) {
        try {
            EvaluationTestHistory history = new EvaluationTestHistory();
            history.setKnowledgeBaseId(knowledgeBaseId);
            history.setQuestion(document.getQuestions());
            history.setExpectedDocId(document.getOriginalDocId());
            history.setRecallCount(recalledDocIds.size());
            history.setIsCorrectRecall(isCorrect);
            history.setRecalledDocIds(objectMapper.writeValueAsString(recalledDocIds));
            history.setSimilarityScores(objectMapper.writeValueAsString(similarityScores));
            history.setExecutionTime(executionTime);
            history.setStatus("SUCCESS");
            history.setCreateTime(LocalDateTime.now());

            // 添加到批量缓存，避免死锁风险
            boolean needFlush = false;
            synchronized (batchHistoryCache) {
                batchHistoryCache.add(history);

                // 当缓存达到批量大小时，标记需要刷新
                if (batchHistoryCache.size() >= BATCH_SAVE_SIZE) {
                    needFlush = true;
                }
            }

            // 在同步块外执行刷新操作，避免死锁
            if (needFlush) {
                flushBatchHistoryCache();
            }

        } catch (JsonProcessingException e) {
            log.error("保存测试历史失败，JSON序列化错误", e);
        } catch (Exception e) {
            log.error("保存测试历史失败", e);
        }
    }

    /**
     * 批量保存失败的测试历史记录（线程安全）
     */
    private void saveFailedTestHistoryBatch(EvaluationDocument document, Long knowledgeBaseId, String errorMessage) {
        try {
            EvaluationTestHistory history = new EvaluationTestHistory();
            history.setKnowledgeBaseId(knowledgeBaseId);
            history.setQuestion(document.getQuestions());
            history.setExpectedDocId(document.getOriginalDocId());
            history.setRecallCount(0);
            history.setIsCorrectRecall(false);
            history.setRecalledDocIds("[]");
            history.setSimilarityScores("[]");
            history.setExecutionTime(0L);
            history.setStatus("FAILED");
            history.setErrorMessage(errorMessage);
            history.setCreateTime(LocalDateTime.now());

            // 添加到批量缓存，避免死锁风险
            boolean needFlush = false;
            synchronized (batchHistoryCache) {
                batchHistoryCache.add(history);

                // 当缓存达到批量大小时，标记需要刷新
                if (batchHistoryCache.size() >= BATCH_SAVE_SIZE) {
                    needFlush = true;
                }
            }

            // 在同步块外执行刷新操作，避免死锁
            if (needFlush) {
                flushBatchHistoryCache();
            }

        } catch (Exception e) {
            log.error("保存失败测试历史失败", e);
        }
    }

    /**
     * 刷新批量历史缓存，执行批量保存
     * 优化版本：先获取数据，释放锁，再执行数据库操作，避免长时间持有锁
     */
    private void flushBatchHistoryCache() {
        // 先获取数据，避免长时间持有锁
        List<EvaluationTestHistory> toSave;
        synchronized (batchHistoryCache) {
            if (batchHistoryCache.isEmpty()) {
                return;
            }

            toSave = new ArrayList<>(batchHistoryCache);
            batchHistoryCache.clear();
        }

        // 在锁外执行数据库操作，避免阻塞其他线程
        if (!toSave.isEmpty()) {
            try {
                log.debug("批量保存测试历史记录，数量: {}", toSave.size());

                // 使用自定义的批量插入方法
                int insertedCount = evaluationTestHistoryMapper.insertBatch(toSave);

                log.debug("批量保存测试历史记录完成，数量: {}, 实际插入: {}", toSave.size(), insertedCount);

            } catch (Exception e) {
                log.error("批量保存测试历史记录失败", e);
                // 由于是开发环境，允许丢失少量测试历史数据，不再重试
                // 如果需要保证数据完整性，可以在这里添加重试逻辑
            }
        }
    }

    /**
     * 服务销毁时的清理工作
     * 实现DisposableBean接口，确保Spring容器关闭时自动调用
     */
    @Override
    public void destroy() throws Exception {
        log.info("EvaluationTestService 正在关闭，执行清理工作...");

        try {
            // 1. 刷新剩余的批量缓存
            flushBatchHistoryCache();

            // 2. 关闭线程池
            executorService.shutdown();

            // 等待线程池关闭，最多等待30秒
            if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("线程池未能在30秒内正常关闭，强制关闭");
                executorService.shutdownNow();

                // 再次等待10秒，确保任务能够响应中断
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.error("线程池无法正常关闭，可能存在资源泄漏");
                }
            }

            log.info("EvaluationTestService 清理工作完成");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("清理工作被中断", e);
            executorService.shutdownNow();
        } catch (Exception e) {
            log.error("清理工作异常", e);
            // 确保线程池被关闭，即使发生异常
            executorService.shutdownNow();
            throw e; // 重新抛出异常，让Spring容器知道销毁过程中出现问题
        }
    }

    /**
     * 创建空的评测结果
     */
    private EvaluationResult createEmptyResult(Long knowledgeBaseId, LocalDateTime startTime) {
        LocalDateTime endTime = LocalDateTime.now();
        
        EvaluationResult result = new EvaluationResult();
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setTotalQuestions(0);
        result.setCorrectRecalls(0);
        result.setRecallRate(0.0);
        result.setAverageAccuracy(0.0); // 空结果时平均准确率为0
        result.setAvgExecutionTime(0.0);
        result.setTestResults(new ArrayList<>());
        result.setStartTime(startTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.setEndTime(endTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.setTotalDuration(java.time.Duration.between(startTime, endTime).toMillis());
        
        return result;
    }
    
    /**
     * 获取评测统计信息
     */
    public EvaluationStatistics getEvaluationStatistics(Long knowledgeBaseId) {
        Integer totalTests = evaluationTestHistoryMapper.countByKnowledgeBaseId(knowledgeBaseId);
        Integer correctRecalls = evaluationTestHistoryMapper.countCorrectRecallByKnowledgeBaseId(knowledgeBaseId);
        Integer totalDocuments = evaluationDocumentMapper.countByKnowledgeBaseId(knowledgeBaseId);
        
        EvaluationStatistics stats = new EvaluationStatistics();
        stats.setKnowledgeBaseId(knowledgeBaseId);
        stats.setTotalDocuments(totalDocuments);
        stats.setTotalTests(totalTests);
        stats.setCorrectRecalls(correctRecalls);
        stats.setRecallRate(totalTests > 0 ? (double) correctRecalls / totalTests : 0.0);
        
        return stats;
    }
    
    /**
     * 提取真实的原始文档ID
     * 处理重用文档的情况，去掉后缀获取真实的originalDocId
     */
    private String extractRealOriginalDocId(String originalDocId) {
        if (originalDocId == null) {
            return null;
        }

        // 检查是否是重用文档的ID（包含_reuse_kb后缀）
        if (originalDocId.contains("_reuse_kb")) {
            // 去掉后缀，返回真实的originalDocId
            return originalDocId.substring(0, originalDocId.indexOf("_reuse_kb"));
        }

        // 如果不是重用文档，直接返回原始ID
        return originalDocId;
    }

    /**
     * 计算详细的准确率统计信息
     */

    private AccuracyStatistics calculateDetailedAccuracyStatistics(
            List<EvaluationResult.TestResult> testResults,
            double totalAccuracy,
            int validAccuracyCount,
            double averageAccuracy) {

        AccuracyStatistics stats = new AccuracyStatistics();

        // 1. 基础统计
        int totalRecalledSegments = 0;
        int totalCorrectSegments = 0;
        int noRecallQuestions = 0;

        for (EvaluationResult.TestResult testResult : testResults) {
            if (testResult.getRecallCount() != null && testResult.getRecallCount() > 0) {
                totalRecalledSegments += testResult.getRecallCount();

                // 计算正确片段数：准确率 × 召回片段数
                if (testResult.getAccuracy() != null) {
                    int correctSegments = (int) Math.round(testResult.getAccuracy() * testResult.getRecallCount());
                    totalCorrectSegments += correctSegments;
                }
            } else {
                noRecallQuestions++;
            }
        }

        int totalIncorrectSegments = totalRecalledSegments - totalCorrectSegments;

        stats.setTotalRecalledSegments(totalRecalledSegments);
        stats.setTotalCorrectSegments(totalCorrectSegments);
        stats.setTotalIncorrectSegments(totalIncorrectSegments);
        stats.setValidQuestions(validAccuracyCount);
        stats.setNoRecallQuestions(noRecallQuestions);

        // 2. 准确率分布统计
        Map<Double, Integer> accuracyCountMap = new HashMap<>();
        for (EvaluationResult.TestResult testResult : testResults) {
            if (testResult.getAccuracy() != null) {
                // 四舍五入到4位小数
                double roundedAccuracy = Math.round(testResult.getAccuracy() * 10000.0) / 10000.0;
                accuracyCountMap.put(roundedAccuracy, accuracyCountMap.getOrDefault(roundedAccuracy, 0) + 1);
            }
        }

        List<AccuracyDistribution> distributions = new ArrayList<>();
        for (Map.Entry<Double, Integer> entry : accuracyCountMap.entrySet()) {
            AccuracyDistribution distribution = new AccuracyDistribution();
            distribution.setAccuracyValue(entry.getKey());
            distribution.setQuestionCount(entry.getValue());
            distribution.setContributionSum(entry.getKey() * entry.getValue());
            distributions.add(distribution);
        }

        // 按准确率值排序
        distributions.sort((a, b) -> Double.compare(b.getAccuracyValue(), a.getAccuracyValue()));
        stats.setAccuracyDistribution(distributions);

        // 3. 详细计算过程
        StringBuilder calculation = new StringBuilder();
        calculation.append("准确率计算详情:\n");
        calculation.append(String.format("总召回片段数: %d\n", totalRecalledSegments));
        calculation.append(String.format("总正确片段数: %d\n", totalCorrectSegments));
        calculation.append(String.format("总错误片段数: %d\n", totalIncorrectSegments));
        calculation.append(String.format("有效问题数: %d\n", validAccuracyCount));
        calculation.append(String.format("无召回问题数: %d\n", noRecallQuestions));
        calculation.append("\n准确率分布:\n");

        for (AccuracyDistribution dist : distributions) {
            calculation.append(String.format("准确率 %.4f: %d个问题, 贡献值 %.4f\n",
                    dist.getAccuracyValue(), dist.getQuestionCount(), dist.getContributionSum()));
        }

        calculation.append(String.format("\n平均准确率计算: %.4f ÷ %d = %.4f",
                totalAccuracy, validAccuracyCount, averageAccuracy));

        stats.setDetailedCalculation(calculation.toString());

        return stats;
    }


    /**
     * 构建详细的平均准确率计算公式字符串
     *
     * @param testResults 测试结果列表
     * @param totalAccuracy 总准确率
     * @param validAccuracyCount 有效问题数
     * @param averageAccuracy 平均准确率
     * @return 详细的计算公式字符串
     */
    private String buildDetailedAverageAccuracyFormula(List<EvaluationResult.TestResult> testResults,
                                                      double totalAccuracy,
                                                      int validAccuracyCount,
                                                      double averageAccuracy) {
        StringBuilder formula = new StringBuilder();
        formula.append("平均准确率 = 所有问题准确率的平均值 = (");

        // 收集所有有效问题的准确率
        List<String> accuracyValues = new ArrayList<>();
        for (EvaluationResult.TestResult testResult : testResults) {
            if (testResult.getAccuracy() != null &&
                testResult.getExpectedDocId() != null &&
                !testResult.getExpectedDocId().trim().isEmpty()) {
                accuracyValues.add(String.format("%.4f", testResult.getAccuracy()));
            }
        }

        // 拼接所有准确率值
        formula.append(String.join(" + ", accuracyValues));
        formula.append(") / ").append(validAccuracyCount);
        formula.append(" = ").append(String.format("%.4f", totalAccuracy));
        formula.append(" / ").append(validAccuracyCount);
        formula.append(" = ").append(String.format("%.4f", averageAccuracy));

        return formula.toString();
    }

    @lombok.Data
    public static class EvaluationStatistics {
        private Long knowledgeBaseId;
        private Integer totalDocuments;
        private Integer totalTests;
        private Integer correctRecalls;
        private Double recallRate;
    }
}
