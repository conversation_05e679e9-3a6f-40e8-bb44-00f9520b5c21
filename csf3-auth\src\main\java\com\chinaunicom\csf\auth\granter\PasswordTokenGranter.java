
package com.chinaunicom.csf.auth.granter;

import com.chinaunicom.csf.core.tool.utils.ObjectUtil;
import lombok.AllArgsConstructor;
import com.chinaunicom.csf.auth.enums.CsfUserEnum;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.DigestUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.chinaunicom.csf.system.user.feign.IUserClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * PasswordTokenGranter
 *
 */
@Component
@AllArgsConstructor
@Slf4j
public class PasswordTokenGranter implements ITokenGranter {

	public static final String GRANT_TYPE = "password";

	private IUserClient userClient;

	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		String tenantId = tokenParameter.getArgs().getStr("tenantId");
		String account = tokenParameter.getArgs().getStr("account");
		String password = tokenParameter.getArgs().getStr("password");
		UserInfo userInfo = null;

		if (Func.isNoneBlank(account, password)) {
			// 获取用户类型
			String userType = tokenParameter.getArgs().getStr("userType");
			String salt =  userClient.getUserSalt(tenantId, account);
			if(ObjectUtil.isEmpty(salt)) {
				salt = "";
			}
			R<UserInfo> result;
			// 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
			if (userType.equals(CsfUserEnum.WEB.getName())) {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			} else if (userType.equals(CsfUserEnum.APP.getName())) {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			} else {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			}
			userInfo = result.isSuccess() ? result.getData() : null;
		}
		return userInfo;
	}

}
