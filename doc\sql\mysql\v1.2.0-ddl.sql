DROP TABLE if EXISTS `csf_audit_log`;
CREATE TABLE `csf_audit_log`
(
    `id`                 bigint(20)   NOT NULL,
    `success`            boolean      NOT NULL COMMENT '操作结果',
    `operator`           varchar(100) NULL DEFAULT NULL COMMENT '操作人',
    `operate_type`       varchar(10)  NOT NULL COMMENT '操作类型',
    `operate_time`       datetime     NOT NULL COMMENT '操作时间',
    `operate_table_name` varchar(50)  NOT NULL COMMENT '操作表名称',
    `operate_columns`    varchar(500)      DEFAULT NULL COMMENT '修改字段',
    `original_data`      text              DEFAULT NULL COMMENT '原始数据',
    `modified_data`      text              DEFAULT NULL COMMENT '更改后数据',
    `diff`               text              DEFAULT NULL COMMENT '变更内容',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '审计日志表';
