package com.chinaunicom.ai.knowledge.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class S3Config {
    @Autowired
    private S3Properties s3Properties;

    @Bean
    public AmazonS3 amazonS3() {
        return AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(
                new BasicAWSCredentials(s3Properties.getAccessKey(), s3Properties.getSecretKey())))
            .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(
                s3Properties.getEndpoint(), s3Properties.getRegion()))
            .withPathStyleAccessEnabled(true) // MinIO 通常启用路径风格
            .build();
    }
}
