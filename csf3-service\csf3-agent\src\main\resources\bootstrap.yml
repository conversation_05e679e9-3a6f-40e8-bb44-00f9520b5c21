spring:
  application:
    name: csf-agent
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 10.81.9.154:8848
        username: nacos
        password: <PERSON><PERSON><PERSON>@nacos2023
      config:
        server-addr: 10.81.9.154:8848
        username: nacos
        password: <PERSON><PERSON><PERSON>@nacos2023
        namespace: csf-agent
        file-extension: yaml
        refresh-enabled: true
        shared-configs:
          - data-id: csf.yaml
            group: DEFAULT_GROUP
            refresh: true
          - data-id: csf-dev.yaml
            group: DEFAULT_GROUP
            refresh: true