package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationRequest;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationResult;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.KnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

/**
 * 端到端评测服务
 * 
 * 提供完整的评测流程：创建知识库 → 导入文档 → 向量化 → 评测 → 清理
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class FullEvaluationService {

    private final FullEvaluationTransactionService transactionService;
    private final VectorizationMonitorService vectorizationMonitor;
    private final IElasticSearchService elasticSearchService;
    private final KnowledgeBaseService knowledgeBaseService;

    /**
     * 执行端到端评测
     *
     * 重要：移除@Transactional注解，将长事务拆分为多个独立的短事务
     * 避免Python向量化服务异步回调时的锁等待问题
     *
     * @param request 评测请求
     * @return 评测结果
     */
    public FullEvaluationResult runFullEvaluation(FullEvaluationRequest request) {
        log.info("开始执行端到端评测，请求参数: {}", request);

        // 验证请求参数
        request.validate();

        FullEvaluationResult result = new FullEvaluationResult();
        result.setStartTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.setExecutionSteps(new ArrayList<>());

        long startTime = System.currentTimeMillis();
        Long tempKnowledgeBaseId = null;

        try {
            // 步骤1: 创建临时知识库（独立事务）
            result.addExecutionStep("CREATE_KNOWLEDGE_BASE", "IN_PROGRESS", "创建临时知识库");
            tempKnowledgeBaseId = transactionService.createTemporaryKnowledgeBase(request, result);
            result.updateLastStepStatus("SUCCESS", null);

            // 步骤2: 导入评测数据（独立事务）
            result.addExecutionStep("IMPORT_DOCUMENTS", "IN_PROGRESS", "导入评测文档");
            transactionService.importEvaluationDocuments(tempKnowledgeBaseId, request, result);
            result.updateLastStepStatus("SUCCESS", null);

            // 步骤3: 等待向量化完成（无事务，允许Python服务异步回调）
            boolean vectorizationSuccess = false;
            result.addExecutionStep("WAIT_VECTORIZATION", "IN_PROGRESS", "等待文档向量化完成");
            try {
                waitForVectorization(tempKnowledgeBaseId, request, result);
                result.updateLastStepStatus("SUCCESS", null);
                vectorizationSuccess = true;
            } catch (Exception vectorizationError) {
                log.warn("向量化步骤失败，但继续尝试评测: {}", vectorizationError.getMessage());
                result.updateLastStepStatus("FAILURE", vectorizationError.getMessage());
                vectorizationSuccess = false;
            }

            // 步骤4: 执行评测（独立事务）- 即使向量化部分失败也尝试评测
            boolean evaluationSuccess = false;
            if (canProceedWithEvaluation(tempKnowledgeBaseId, result)) {
                result.addExecutionStep("RUN_EVALUATION", "IN_PROGRESS", "执行检索质量评测");
                try {
                    transactionService.runEvaluation(tempKnowledgeBaseId, request, result);
                    result.updateLastStepStatus("SUCCESS", null);
                    evaluationSuccess = true;
                } catch (Exception evaluationError) {
                    log.warn("评测步骤失败: {}", evaluationError.getMessage());
                    result.updateLastStepStatus("FAILURE", evaluationError.getMessage());
                    evaluationSuccess = false;
                }
            } else {
                result.addExecutionStep("RUN_EVALUATION", "SKIPPED", "跳过评测：没有足够的向量化数据");
            }

            // 步骤5: 清理资源（独立事务，如果需要）
            if (request.shouldCleanup()) {
                result.addExecutionStep("CLEANUP_RESOURCES", "IN_PROGRESS", "清理临时资源");
                try {
                    transactionService.cleanupResources(tempKnowledgeBaseId, result);
                    result.updateLastStepStatus("SUCCESS", null);
                } catch (Exception cleanupError) {
                    log.warn("清理资源失败: {}", cleanupError.getMessage());
                    result.updateLastStepStatus("FAILURE", cleanupError.getMessage());
                }
            }

            // 判断整体成功状态：基于关键步骤的成功情况
            boolean overallSuccess = determineOverallSuccess(result, vectorizationSuccess, evaluationSuccess);
            result.setSuccess(overallSuccess);
            result.setEndTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            result.setTotalDurationMs(System.currentTimeMillis() - startTime);

            if (overallSuccess) {
                log.info("端到端评测完成，知识库ID: {}, 总耗时: {}ms, 性能摘要: {}",
                        tempKnowledgeBaseId, result.getTotalDurationMs(), result.getPerformanceSummary());
            } else {
                log.warn("端到端评测部分失败，知识库ID: {}, 总耗时: {}ms, 性能摘要: {}",
                        tempKnowledgeBaseId, result.getTotalDurationMs(), result.getPerformanceSummary());
            }

        } catch (Exception e) {
            log.error("端到端评测失败", e);

            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            result.setTotalDurationMs(System.currentTimeMillis() - startTime);
            result.updateLastStepStatus("FAILURE", e.getMessage());

            // 失败时强制清理资源（独立事务）
            if (tempKnowledgeBaseId != null) {
                try {
                    result.addExecutionStep("FORCE_CLEANUP", "IN_PROGRESS", "失败后强制清理资源");
                    transactionService.cleanupResources(tempKnowledgeBaseId, result);
                    result.updateLastStepStatus("SUCCESS", null);
                } catch (Exception cleanupError) {
                    log.error("强制清理资源失败", cleanupError);
                    result.updateLastStepStatus("FAILURE", cleanupError.getMessage());
                }
            }
        }

        return result;
    }




    /**
     * 等待向量化完成
     */
    private void waitForVectorization(Long knowledgeBaseId, FullEvaluationRequest request, FullEvaluationResult result) {
        log.info("等待知识库 {} 向量化完成", knowledgeBaseId);

        VectorizationMonitorService.VectorizationMonitorResult monitorResult =
            vectorizationMonitor.waitForVectorizationComplete(
                knowledgeBaseId,
                request.getVectorizationTimeoutSeconds(),
                request.getStatusCheckIntervalSeconds()
            );

        if (!monitorResult.getSuccess()) {
            throw new RuntimeException("向量化监控失败: " + monitorResult.getErrorMessage());
        }

        // 额外检查并自动同步评测文档的状态
        log.info("向量化完成，开始检查并同步评测文档状态");
        VectorizationMonitorService.VectorizationStatus finalStatus =
            vectorizationMonitor.checkEvaluationVectorizationStatus(knowledgeBaseId, true); // 启用自动同步

        if (finalStatus.getError() != null) {
            log.warn("评测文档状态检查发现问题: {}", finalStatus.getError());
            // 不抛出异常，但记录警告，评测可以继续进行
        } else {
            log.info("评测文档状态一致性检查通过，可以开始评测");
        }

        // 获取ES中的片段总数
        KnowledgeBase knowledgeBase = knowledgeBaseService.getById(knowledgeBaseId);
        long totalSegments = 0;
        if (knowledgeBase != null && knowledgeBase.getIndexName() != null) {
            totalSegments = elasticSearchService.countDocuments(knowledgeBase.getIndexName());
        }

        // 设置向量化统计信息
        FullEvaluationResult.VectorizationStats vectorStats = new FullEvaluationResult.VectorizationStats();
        vectorStats.setTotalSegments(totalSegments);
        vectorStats.setVectorizedCount(monitorResult.getLastCheckStatus().getVectorizedCount());
        vectorStats.setVectorizationFailureCount(monitorResult.getLastCheckStatus().getFailedCount());
        vectorStats.setVectorizationDurationMs(monitorResult.getDurationMs());
        vectorStats.setStatusCheckCount(monitorResult.getCheckCount());

        if (vectorStats.getVectorizedCount() != null && vectorStats.getVectorizedCount() > 0) {
            vectorStats.setAvgVectorizationTimePerDoc(
                (double) vectorStats.getVectorizationDurationMs() / vectorStats.getVectorizedCount()
            );
        }

        result.setVectorizationStats(vectorStats);

        log.info("向量化完成，总片段数: {}, 成功文档数: {}, 失败文档数: {}, 耗时: {}ms",
                totalSegments, vectorStats.getVectorizedCount(),
                vectorStats.getVectorizationFailureCount(), vectorStats.getVectorizationDurationMs());
    }





    /**
     * 获取评测统计信息（用于监控和调试）
     */
    public FullEvaluationResult.VectorizationStats getVectorizationStats(Long knowledgeBaseId) {
        VectorizationMonitorService.VectorizationStatus status =
            vectorizationMonitor.checkVectorizationStatus(knowledgeBaseId);

        FullEvaluationResult.VectorizationStats stats = new FullEvaluationResult.VectorizationStats();
        stats.setVectorizedCount(status.getVectorizedCount());
        stats.setVectorizationFailureCount(status.getFailedCount());

        // 获取ES片段数量
        KnowledgeBase knowledgeBase = knowledgeBaseService.getById(knowledgeBaseId);
        if (knowledgeBase != null && knowledgeBase.getIndexName() != null) {
            stats.setTotalSegments(elasticSearchService.countDocuments(knowledgeBase.getIndexName()));
        }

        return stats;
    }

    /**
     * 检查知识库是否准备就绪
     */
    public boolean isKnowledgeBaseReady(Long knowledgeBaseId) {
        VectorizationMonitorService.VectorizationStatus status =
            vectorizationMonitor.checkVectorizationStatus(knowledgeBaseId);

        return status.isAllCompleted() &&
               (status.getVectorizedCount() != null && status.getVectorizedCount() > 0);
    }

    /**
     * 检查是否可以继续进行评测
     * 即使向量化部分失败，只要有部分数据成功向量化就可以进行评测
     */
    private boolean canProceedWithEvaluation(Long knowledgeBaseId, FullEvaluationResult result) {
        try {
            // 检查是否有成功导入的文档
            if (result.getDocumentImportStats() == null ||
                result.getDocumentImportStats().getSuccessCount() == null ||
                result.getDocumentImportStats().getSuccessCount() <= 0) {
                log.warn("没有成功导入的文档，无法进行评测");
                return false;
            }

            // 检查是否有向量化的数据
            VectorizationMonitorService.VectorizationStatus status =
                vectorizationMonitor.checkVectorizationStatus(knowledgeBaseId);

            if (status.getVectorizedCount() != null && status.getVectorizedCount() > 0) {
                log.info("检测到 {} 个文档已向量化，可以进行评测", status.getVectorizedCount());
                return true;
            }

            // 检查ES中是否有数据
            KnowledgeBase knowledgeBase = knowledgeBaseService.getById(knowledgeBaseId);
            if (knowledgeBase != null && knowledgeBase.getIndexName() != null) {
                long segmentCount = elasticSearchService.countDocuments(knowledgeBase.getIndexName());
                if (segmentCount > 0) {
                    log.info("检测到ES中有 {} 个文档片段，可以进行评测", segmentCount);
                    return true;
                }
            }

            log.warn("没有找到可用于评测的向量化数据");
            return false;
        } catch (Exception e) {
            log.error("检查评测条件时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断整体评测是否成功
     * 成功标准：
     * 1. 有成功导入的文档（允许部分失败）
     * 2. 有成功的评测结果（即使向量化部分失败）
     * 或者：
     * 1. 有成功导入的文档
     * 2. 向量化成功（即使评测失败）
     */
    private boolean determineOverallSuccess(FullEvaluationResult result, boolean vectorizationSuccess, boolean evaluationSuccess) {
        // 检查文档导入是否有成功的数据
        boolean hasSuccessfulImport = result.getDocumentImportStats() != null &&
                result.getDocumentImportStats().getSuccessCount() != null &&
                result.getDocumentImportStats().getSuccessCount() > 0;

        if (!hasSuccessfulImport) {
            log.warn("没有成功导入的文档，判定为整体失败");
            return false;
        }

        // 计算导入成功率
        double importSuccessRate = result.getDocumentImportStats().getTotalDocuments() > 0 ?
                (double) result.getDocumentImportStats().getSuccessCount() / result.getDocumentImportStats().getTotalDocuments() : 0.0;

        // 如果评测成功，则整体成功（允许向量化部分失败）
        if (evaluationSuccess) {
            log.info("评测成功，导入成功率: {:.1f}%，判定为整体成功", importSuccessRate * 100);
            return true;
        }

        // 如果评测失败但向量化成功，且导入成功率较高，也可以判定为部分成功
        if (vectorizationSuccess && importSuccessRate >= 0.8) { // 80%以上导入成功率
            log.info("向量化成功且导入成功率较高({:.1f}%)，判定为部分成功", importSuccessRate * 100);
            return true;
        }

        // 如果导入成功率很高（95%以上），即使其他步骤失败也可以考虑部分成功
        if (importSuccessRate >= 0.95) {
            log.info("导入成功率很高({:.1f}%)，判定为部分成功", importSuccessRate * 100);
            return true;
        }

        log.warn("整体评测失败 - 向量化成功: {}, 评测成功: {}, 导入成功率: {:.1f}%",
                vectorizationSuccess, evaluationSuccess, importSuccessRate * 100);
        return false;
    }
}
