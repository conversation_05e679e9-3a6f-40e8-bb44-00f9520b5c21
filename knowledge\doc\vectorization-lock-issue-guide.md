# 向量化数据库锁等待问题解决指南

## 🚨 问题描述

在端到端评测过程中，多个文档同时进行向量化处理时，出现MySQL数据库锁等待超时错误：

```
Lock wait timeout exceeded; try restarting transaction
```

## 🔍 问题根因分析

### 1. 并发更新冲突
- 多个文档同时调用`PythonVectorizationService.updateDocumentStatus()`
- 对`knowledge_document`表的同一行或相关行进行并发更新
- MySQL InnoDB引擎的行锁机制导致锁等待

### 2. 事务持续时间过长
- Python向量化服务响应时间不确定
- 数据库连接池可能存在连接泄漏
- 长时间持有锁导致其他事务等待超时

### 3. 数据库配置问题
- `innodb_lock_wait_timeout`设置过短
- 并发连接数配置不当

## ✅ 解决方案

### 1. 立即解决方案：重试机制（已实现）

在`PythonVectorizationService`中实现了智能重试机制：

```java
// 配置参数
vectorization:
  retry:
    max-attempts: 3          # 最大重试次数
    initial-delay: 100       # 初始重试延迟（毫秒）
    backoff-multiplier: 2.0  # 重试延迟倍数
    max-delay: 5000          # 最大重试延迟（毫秒）
```

**特性**：
- 指数退避重试策略
- 针对锁等待异常的专门处理
- 避免重试风暴的延迟机制

### 2. 数据库优化建议

#### 2.1 调整MySQL配置

```sql
-- 增加锁等待超时时间
SET GLOBAL innodb_lock_wait_timeout = 120;

-- 查看当前配置
SHOW VARIABLES LIKE 'innodb_lock_wait_timeout';

-- 增加并发连接数
SET GLOBAL max_connections = 500;
```

#### 2.2 优化表结构

```sql
-- 确保knowledge_document表有合适的索引
ALTER TABLE knowledge_document ADD INDEX idx_status_update_time (status, update_time);
ALTER TABLE knowledge_document ADD INDEX idx_base_id_status (base_id, status);
```

### 3. 应用层优化

#### 3.1 批量处理策略

```yaml
vectorization:
  concurrency:
    max-concurrent-tasks: 10 # 限制并发任务数
    batch-size: 5           # 批处理大小
    batch-delay: 1000       # 批次间延迟
```

#### 3.2 连接池优化

```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 4. 监控和预警

#### 4.1 添加监控指标

```java
// 在PythonVectorizationService中添加
@Autowired
private MeterRegistry meterRegistry;

// 记录重试次数
Counter.builder("vectorization.retry.count")
    .tag("reason", "lock_timeout")
    .register(meterRegistry)
    .increment();
```

#### 4.2 日志监控

关键日志关键词：
- `Lock wait timeout exceeded`
- `文档状态更新遇到锁等待`
- `文档状态更新最终失败`

## 🛠️ 故障排查步骤

### 1. 检查数据库状态

```sql
-- 查看当前锁等待情况
SELECT * FROM information_schema.INNODB_LOCKS;
SELECT * FROM information_schema.INNODB_LOCK_WAITS;

-- 查看事务状态
SELECT * FROM information_schema.INNODB_TRX;

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';
```

### 2. 检查应用日志

```bash
# 查看锁等待相关日志
grep -i "lock wait timeout" /path/to/application.log

# 查看向量化处理日志
grep -i "向量化" /path/to/application.log | tail -100

# 查看重试日志
grep -i "重试" /path/to/application.log | tail -50
```

### 3. 检查系统资源

```bash
# 检查数据库连接数
netstat -an | grep :3306 | wc -l

# 检查内存使用
free -h

# 检查CPU使用
top -p $(pgrep java)
```

## 🎯 预防措施

### 1. 分批处理策略

```java
// 在端到端评测中实现分批处理
public void processBatchVectorization(List<Long> documentIds) {
    int batchSize = vectorizationConfig.getConcurrency().getBatchSize();
    int batchDelay = vectorizationConfig.getConcurrency().getBatchDelay();
    
    for (int i = 0; i < documentIds.size(); i += batchSize) {
        List<Long> batch = documentIds.subList(i, 
            Math.min(i + batchSize, documentIds.size()));
        
        // 处理当前批次
        processBatch(batch);
        
        // 批次间延迟
        if (i + batchSize < documentIds.size()) {
            Thread.sleep(batchDelay);
        }
    }
}
```

### 2. 异步处理优化

```java
// 使用信号量控制并发数
private final Semaphore vectorizationSemaphore = 
    new Semaphore(vectorizationConfig.getConcurrency().getMaxConcurrentTasks());

public void triggerVectorization(Long documentId, ...) {
    try {
        vectorizationSemaphore.acquire();
        // 执行向量化
        doVectorization(documentId, ...);
    } finally {
        vectorizationSemaphore.release();
    }
}
```

### 3. 健康检查

```java
@Component
public class VectorizationHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        // 检查数据库连接
        // 检查Python服务状态
        // 检查待处理队列长度
        return Health.up()
            .withDetail("pendingTasks", getPendingTaskCount())
            .withDetail("failedTasks", getFailedTaskCount())
            .build();
    }
}
```

## 📊 性能调优建议

### 1. 数据库层面

- **连接池大小**: 根据并发需求调整
- **锁等待时间**: 适当增加但不宜过长
- **索引优化**: 确保查询和更新有合适索引

### 2. 应用层面

- **批处理大小**: 平衡吞吐量和资源使用
- **重试策略**: 指数退避避免重试风暴
- **并发控制**: 使用信号量限制并发数

### 3. 系统层面

- **内存配置**: 确保足够的JVM堆内存
- **网络优化**: 优化与Python服务的网络连接
- **监控告警**: 及时发现和处理异常

## 🔄 持续改进

1. **监控指标收集**: 收集重试率、成功率、响应时间等指标
2. **性能基准测试**: 定期进行压力测试
3. **配置动态调整**: 根据实际负载调整配置参数
4. **故障复盘**: 分析每次故障的根因和改进措施

通过以上措施，可以有效解决向量化过程中的数据库锁等待问题，提高系统的稳定性和性能。
