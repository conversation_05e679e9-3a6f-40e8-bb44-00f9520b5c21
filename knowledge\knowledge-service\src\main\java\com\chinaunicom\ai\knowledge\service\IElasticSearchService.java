package com.chinaunicom.ai.knowledge.service;


import com.chinaunicom.ai.knowledge.config.DualRouteConfig;
import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;

import java.util.List;

/**
 * Elasticsearch操作服务接口
 * 定义了与Elasticsearch交互的核心方法
 */
public interface IElasticSearchService {

    /**
     * 创建ElasticSearch索引
     * @param indexName 索引名称
     * @param dims 向量维度
     * @return 是否创建成功
     */
    boolean createIndex(String indexName, Integer dims);

    /**
     * 删除ElasticSearch索引
     * @param indexName 索引名称
     * @return 是否删除成功
     */
    boolean deleteIndex(String indexName);

    /**
     * 根据向量在ElasticSearch中进行相似度搜索
     * @param indexName 索引名称
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @param knowledgeBaseId 知识库ID (用于过滤，可选)
     * @param tenantId 租户ID (用于过滤)
     * @return 匹配的文档片段列表
     */
    List<AgentSearchResultVO> searchVector(String indexName, float[] queryVector, int topK, Long knowledgeBaseId, String tenantId);

    /**
     * 根据文档ID（mysqlDocId）删除Elasticsearch索引中对应文档的所有片段。
     * @param indexName Elasticsearch索引名称
     * @param mysqlDocId MySQL中的文档ID (对应ES中的 metadata.mysqlDocId)
     * @return 是否删除成功
     */
    boolean deleteDocumentByMysqlDocId(String indexName, Long mysqlDocId);

    /**
     * 统计Elasticsearch索引中的文档片段总数
     * @param indexName 索引名称
     * @return 片段总数，如果索引不存在或查询失败返回0
     */
    long countDocuments(String indexName);

    /**
     * 混合检索：结合关键词匹配和向量相似度搜索
     * 使用bool查询组合关键词查询和向量查询，通过权重控制两种检索方式的重要性
     *
     * @param indexName 索引名称
     * @param queryText 查询文本（用于关键词匹配）
     * @param queryVector 查询向量（用于向量相似度计算）
     * @param topK 返回结果数量
     * @param knowledgeBaseId 知识库ID (用于过滤，可选)
     * @param tenantId 租户ID (用于过滤)
     * @param config 混合检索配置参数
     * @return 匹配的文档片段列表，按综合分数排序
     */
    List<AgentSearchResultVO> searchHybrid(String indexName, String queryText, float[] queryVector,
                                          int topK, Long knowledgeBaseId, String tenantId,
                                          HybridSearchConfig config);

    /**
     * 双路检索：结合精确匹配和混合检索的融合检索策略
     * 并行执行精确匹配和混合检索，通过分数标准化和权重融合提升检索效果
     *
     * @param indexName 索引名称
     * @param queryText 查询文本
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @param knowledgeBaseId 知识库ID (用于过滤，可选)
     * @param tenantId 租户ID (用于过滤)
     * @param dualRouteConfig 双路检索配置参数
     * @param hybridConfig 混合检索配置参数
     * @return 融合并重排序后的文档片段列表
     */
    List<AgentSearchResultVO> searchDualRoute(String indexName, String queryText, float[] queryVector,
                                             int topK, Long knowledgeBaseId, String tenantId,
                                             DualRouteConfig dualRouteConfig, HybridSearchConfig hybridConfig);
}
