package com.chinaunicom.ai.knowledge.evaluation.dto;

import lombok.Data;

import java.util.List;

/**
 * 评测结果DTO
 */
@Data
public class EvaluationResult {
    
    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;
    
    /**
     * 总测试问题数
     */
    private Integer totalQuestions;
    
    /**
     * 正确召回数
     */
    private Integer correctRecalls;
    
    /**
     * 召回率（正确召回数/总问题数）
     */
    private Double recallRate;

    /**
     * 平均准确率（所有问题准确率的平均值）
     * 准确率 = 召回结果中正确文档数量 / 召回结果总数量
     * 范围：0.0-1.0
     */
    private Double averageAccuracy;

    /**
     * 平均执行时间（毫秒）
     */
    private Double avgExecutionTime;
    
    /**
     * 详细测试结果
     */
    private List<TestResult> testResults;
    
    /**
     * 评测开始时间
     */
    private String startTime;
    
    /**
     * 评测结束时间
     */
    private String endTime;
    
    /**
     * 总耗时（毫秒）
     */
    private Long totalDuration;

    private String recallRateFormula;

    private String averageAccuracyFormula;

    private AccuracyStatistics accuracyStatistics;
    
    @Data
    public static class TestResult {
        /**
         * 问题文本
         */
        private String question;
        
        /**
         * 期望的文档ID
         */
        private String expectedDocId;
        
        /**
         * 是否召回正确
         */
        private Boolean isCorrect;

        /**
         * 单个问题的准确率
         * 计算公式：召回结果中正确文档数量 / 召回结果总数量
         * 范围：0.0-1.0
         */
        private Double accuracy;

        /**
         * 召回的文档数量
         */
        private Integer recallCount;
        
        /**
         * 召回的文档ID列表
         */
        private List<String> recalledDocIds;
        
        /**
         * 相似度分数列表
         */
        private List<Double> similarityScores;
        
        /**
         * 执行时间（毫秒）
         */
        private Long executionTime;
    }
}
