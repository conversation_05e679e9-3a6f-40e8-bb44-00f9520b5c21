package com.chinaunicom.csf.service.impl;

import com.amazonaws.HttpMethod;
import com.amazonaws.SdkClientException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.csf.dto.*;
import com.chinaunicom.csf.entity.FileMetadata;
import com.chinaunicom.csf.mapper.FileMetadataMapper;
import com.chinaunicom.csf.props.MinioProperties;
import com.chinaunicom.csf.service.IFileUploadService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 文件上传服务实现类
 */
@Slf4j
@Service
@AllArgsConstructor
public class FileUploadServiceImpl extends ServiceImpl<FileMetadataMapper, FileMetadata> implements IFileUploadService {

    private final AmazonS3 amazonS3; // 替换为 AmazonS3
    private final MinioProperties minioProperties;

    /**
     * 上传文件
     *
     * @param request 文件上传请求
     * @return 文件元数据DTO
     */
    @Override
    public FileMetadataDTO uploadFile(FileUploadRequest request) {
        MultipartFile file = request.getFile();
        String bizType = request.getBizType();
        String originalFileName = file.getOriginalFilename();
        String fileExtension = StringUtils.getFilenameExtension(originalFileName);

        String fileId = UUID.randomUUID().toString().replace("-", ""); // 32位不含横线的UUID
        String objectName = fileId + (StringUtils.hasText(fileExtension) ? "." + fileExtension : "");
        String bucketName = minioProperties.getBucketName();

        try {
            // 确保桶存在，如果不存在则创建
            if (!amazonS3.doesBucketExistV2(bucketName)) {
                amazonS3.createBucket(bucketName);
            }

            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(file.getSize());
            metadata.setContentType(file.getContentType());

            // 上传文件
            amazonS3.putObject(new PutObjectRequest(bucketName, objectName, file.getInputStream(), metadata));

            // 保存文件元数据到数据库
            FileMetadata fileMetadata = new FileMetadata();
            fileMetadata.setId(fileId);
            fileMetadata.setFileName(objectName);
            fileMetadata.setObjectName(objectName);
            fileMetadata.setBucketName(bucketName);
            fileMetadata.setFileSize(file.getSize());
            fileMetadata.setMimeType(file.getContentType());
            fileMetadata.setUploadTime(LocalDateTime.now());
            fileMetadata.setBizType(bizType);
            fileMetadata.setOriginalFileName(originalFileName);

            // 直接构建访问路径，避免循环依赖
            String accessPath;
            LocalDateTime urlExpireTime = null;

            if (StringUtils.hasText(minioProperties.getPublicBucketName()) && minioProperties.getPublicBucketName().equals(bucketName)) {
                // 如果是公共读桶，直接返回拼接的URL（永久有效）
                accessPath = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, objectName);
                // 公共桶URL不设置过期时间
            } else {
                // 私有桶，生成预签名URL
                try {
                    accessPath = generatePresignedUrl(bucketName, objectName);
                    urlExpireTime = LocalDateTime.now().plusDays(7); // 7天有效期
                } catch (Exception e) {
                    log.warn("生成预签名URL失败，使用默认路径: {}", e.getMessage());
                    accessPath = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, objectName);
                }
            }

            fileMetadata.setAccessPath(accessPath);
            fileMetadata.setUrlExpireTime(urlExpireTime);

            this.save(fileMetadata);
            log.info("文件上传成功: fileId={}, fileName={}", fileId, originalFileName);

            FileMetadataDTO fileMetadataDTO = new FileMetadataDTO();
            BeanUtils.copyProperties(fileMetadata, fileMetadataDTO);
            return fileMetadataDTO;

        } catch (SdkClientException e) { // Catch client-side exceptions including AmazonServiceException
            log.error("S3操作异常: ", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("文件上传过程中发生IO异常: ", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 初始化分片上传
     *
     * @param request 初始化分片上传请求
     * @return 初始化分片上传响应
     */
    @Override
    public InitMultipartUploadResponse initMultipartUpload(InitMultipartUploadRequest request) {
        String fileName = request.getFileName();
        String mimeType = request.getMimeType();
        String bizType = request.getBizType();

        String fileId = UUID.randomUUID().toString().replace("-", ""); // 32位不含横线的UUID
        String fileExtension = StringUtils.getFilenameExtension(fileName);
        String objectName = fileId + (StringUtils.hasText(fileExtension) ? "." + fileExtension : "");
        String bucketName = minioProperties.getBucketName();

        try {
            // 确保桶存在，如果不存在则创建
            if (!amazonS3.doesBucketExistV2(bucketName)) {
                amazonS3.createBucket(bucketName);
            }

            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(mimeType);

            // 发起分片上传请求，获取uploadId
            InitiateMultipartUploadResult initiateResult = amazonS3.initiateMultipartUpload(
                    new com.amazonaws.services.s3.model.InitiateMultipartUploadRequest(bucketName, objectName, objectMetadata));
            String uploadId = initiateResult.getUploadId();

            // 保存文件元数据到数据库，记录 uploadId
            FileMetadata fileMetadata = new FileMetadata();
            fileMetadata.setId(fileId);
            fileMetadata.setFileName(objectName);
            fileMetadata.setObjectName(objectName);
            fileMetadata.setBucketName(bucketName);
            fileMetadata.setMimeType(mimeType);
            fileMetadata.setUploadTime(LocalDateTime.now());
            fileMetadata.setBizType(bizType);
            fileMetadata.setOriginalFileName(fileName);
            fileMetadata.setUploadId(uploadId); // 设置分片上传ID
            fileMetadata.setAccessPath(""); // 初始化为空，待 completeMultipartUpload 时更新
            fileMetadata.setFileSize(0L); // 初始化文件大小为0，待 completeMultipartUpload 时更新
            this.save(fileMetadata);
            log.info("初始化分片上传成功: fileId={}, objectName={}, uploadId={}", fileId, objectName, uploadId);

            InitMultipartUploadResponse response = new InitMultipartUploadResponse();
            response.setFileId(fileId);
            response.setObjectName(objectName);
            response.setBucketName(bucketName);
            response.setUploadId(uploadId);
            return response;

        } catch (SdkClientException e) { // Catch client-side exceptions including AmazonServiceException
            log.error("S3操作异常: ", e);
            throw new RuntimeException("初始化分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 上传文件分片
     *
     * @param request 上传分片请求
     * @return etag
     */
    @Override
    public String uploadPart(com.chinaunicom.csf.dto.UploadPartRequest request) {
        String fileId = request.getFileId();
        String bucketName = request.getBucketName();
        String objectName = request.getObjectName();
        String uploadId = request.getUploadId();
        Integer partNumber = request.getPartNumber();
        MultipartFile partFile = request.getPartFile();

        try (InputStream inputStream = partFile.getInputStream()) {
            // 上传文件分片
            com.amazonaws.services.s3.model.UploadPartRequest s3UploadPartRequest = new com.amazonaws.services.s3.model.UploadPartRequest()
                    .withBucketName(bucketName)
                    .withKey(objectName)
                    .withUploadId(uploadId)
                    .withPartNumber(partNumber)
                    .withInputStream(inputStream)
                    .withPartSize(partFile.getSize());

            UploadPartResult uploadPartResult = amazonS3.uploadPart(s3UploadPartRequest);
            String etag = uploadPartResult.getETag();

            log.info("文件分片上传成功: fileId={}, partNumber={}, etag={}", fileId, partNumber, etag);
            return etag;
        } catch (SdkClientException e) { // Catch client-side exceptions including AmazonServiceException
            log.error("S3操作异常: ", e);
            throw new RuntimeException("上传文件分片失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("上传文件分片过程中发生IO异常: ", e);
            throw new RuntimeException("上传文件分片失败: " + e.getMessage());
        }
    }

    /**
     * 完成分片上传
     *
     * @param request 完成分片上传请求
     * @return 文件元数据DTO
     */
    @Override
    public FileMetadataDTO completeMultipartUpload(com.chinaunicom.csf.dto.CompleteMultipartUploadRequest request) {
        String fileId = request.getFileId();
        String bucketName = request.getBucketName();
        String objectName = request.getObjectName();
        String uploadId = request.getUploadId();
        List<com.chinaunicom.csf.dto.CompleteMultipartUploadRequest.PartETag> partETags = request.getPartETags();

        // 确保分片按partNumber排序，并转换为AWS SDK的PartETag
        List<PartETag> s3PartETags = partETags.stream()
                .map(p -> new PartETag(p.getPartNumber(), p.getEtag()))
                .sorted(Comparator.comparingInt(PartETag::getPartNumber))
                .collect(Collectors.toList());

        try {
            // 完成分片上传
            com.amazonaws.services.s3.model.CompleteMultipartUploadRequest s3CompleteRequest = new com.amazonaws.services.s3.model.CompleteMultipartUploadRequest(
                    bucketName, objectName, uploadId, s3PartETags);
            amazonS3.completeMultipartUpload(s3CompleteRequest);

            // 更新文件元数据
            FileMetadataDTO retrievedFileMetadataDTO = getFileMetadata(fileId);
            if (retrievedFileMetadataDTO == null) {
                log.error("完成分片上传失败：文件元数据未找到，fileId={}", fileId);
                throw new RuntimeException("文件元数据丢失");
            }
            // 获取文件大小
            ObjectMetadata s3ObjectMetadata = amazonS3.getObjectMetadata(bucketName, objectName);
            FileMetadata fileMetadata = new FileMetadata();
            BeanUtils.copyProperties(retrievedFileMetadataDTO, fileMetadata);
            fileMetadata.setFileSize(s3ObjectMetadata.getContentLength());
            fileMetadata.setPartCount(partETags.size()); // 设置分片数量
            fileMetadata.setAccessPath(getFileAccessUrl(fileId)); // 更新访问路径
            this.updateById(fileMetadata); // 更新数据库记录

            log.info("完成分片上传成功: fileId={}, objectName={}", fileId, objectName);

            BeanUtils.copyProperties(fileMetadata, retrievedFileMetadataDTO);
            return retrievedFileMetadataDTO;

        } catch (SdkClientException e) { // Catch client-side exceptions including AmazonServiceException
            log.error("S3操作异常: ", e);
            throw new RuntimeException("完成分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @param response HTTP响应
     */
    @Override
    public void downloadFile(String fileId, HttpServletResponse response) {
        FileMetadataDTO fileMetadataDTO = getFileMetadata(fileId);
        if (fileMetadataDTO == null) {
            log.error("下载失败：文件元数据未找到，fileId={}", fileId);
            throw new RuntimeException("文件不存在");
        }

        String bucketName = fileMetadataDTO.getBucketName();
        String objectName = fileMetadataDTO.getObjectName();
        String originalFileName = fileMetadataDTO.getOriginalFileName();

        try (InputStream stream = amazonS3.getObject(new GetObjectRequest(bucketName, objectName)).getObjectContent()) {
            // 详细日志记录
            log.info("开始处理文件下载: fileId={}, originalFileName={}, bucketName={}, objectName={}",
                    fileId, originalFileName, bucketName, objectName);

            // 设置响应字符编码为UTF-8
            response.setCharacterEncoding("UTF-8");

            // 强制设置为下载类型，避免浏览器预览
            // 使用通用的二进制流类型，而不是具体的MIME类型（如application/pdf）
            response.setContentType("application/octet-stream");

            // 设置强制下载的响应头，支持中文文件名
            String contentDisposition = buildContentDisposition(originalFileName);
            response.setHeader("Content-Disposition", contentDisposition);

            // 添加额外的响应头来确保强制下载
            response.setHeader("Content-Transfer-Encoding", "binary");
            response.setHeader("Accept-Ranges", "bytes");

            log.info("设置强制文件下载: fileId={}, originalFileName={}, contentDisposition={}, originalMimeType={}",
                    fileId, originalFileName, contentDisposition, fileMetadataDTO.getMimeType());

            // 设置文件大小（如果可用）
            if (fileMetadataDTO.getFileSize() != null && fileMetadataDTO.getFileSize() > 0) {
                response.setHeader("Content-Length", String.valueOf(fileMetadataDTO.getFileSize()));
            }

            // 解决跨域问题，允许所有源访问（生产环境应限制）
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition, Content-Length, Content-Transfer-Encoding");

            // 设置强制下载的缓存控制，防止浏览器缓存和预览
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate, private");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");

            // 禁用浏览器的内容嗅探，强制使用我们设置的Content-Type
            response.setHeader("X-Content-Type-Options", "nosniff");

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = stream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            log.info("文件下载成功: fileId={}, originalFileName={}", fileId, originalFileName);
        } catch (SdkClientException e) { // Catch client-side exceptions including AmazonServiceException
            log.error("S3操作异常: ", e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("文件下载过程中发生IO异常: ", e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 根据文件ID获取文件元数据
     *
     * @param fileId 文件ID
     * @return 文件元数据实体
     */
    @Override
    public FileMetadataDTO getFileMetadata(String fileId) {
        LambdaQueryWrapper<FileMetadata> queryWrapper = Wrappers.<FileMetadata>lambdaQuery()
                .eq(FileMetadata::getId, fileId);
        FileMetadata fileMetadata = this.getOne(queryWrapper);
        if (fileMetadata == null) {
            return null;
        }
        FileMetadataDTO fileMetadataDTO = new FileMetadataDTO();
        BeanUtils.copyProperties(fileMetadata, fileMetadataDTO);
        return fileMetadataDTO;
    }

    /**
     * 获取文件直接访问URL（智能URL有效期管理）
     * 检查数据库中存储的URL是否过期，如果过期则生成新的URL并更新数据库
     *
     * @param fileId 文件ID
     * @return 文件URL
     */
    @Override
    public String getFileAccessUrl(String fileId) {
        FileMetadataDTO fileMetadataDTO = getFileMetadata(fileId);
        if (fileMetadataDTO == null) {
            log.error("获取文件访问URL失败：文件元数据未找到，fileId={}", fileId);
            return null;
        }

        String bucketName = fileMetadataDTO.getBucketName();
        String objectName = fileMetadataDTO.getObjectName();

        // 根据配置的公共读桶名称判断是否为公共访问URL
        if (StringUtils.hasText(minioProperties.getPublicBucketName()) && minioProperties.getPublicBucketName().equals(bucketName)) {
            // 如果是公共读桶，直接返回拼接的URL（永久有效）
            String publicUrl = String.format("%s/%s/%s", minioProperties.getEndpoint(), bucketName, objectName);
            log.info("返回公共桶URL: fileId={}, url={}", fileId, publicUrl);
            return publicUrl;
        } else {
            // 私有桶，需要检查预签名URL的有效期
            return getOrRefreshPresignedUrl(fileId, fileMetadataDTO, bucketName, objectName);
        }
    }

    /**
     * 获取或刷新预签名URL
     * 检查数据库中存储的URL是否过期，如果过期或即将过期则生成新的URL并更新数据库
     *
     * @param fileId 文件ID
     * @param fileMetadataDTO 文件元数据
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @return 有效的预签名URL
     */
    private String getOrRefreshPresignedUrl(String fileId, FileMetadataDTO fileMetadataDTO, String bucketName, String objectName) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime urlExpireTime = fileMetadataDTO.getUrlExpireTime();
        String currentAccessPath = fileMetadataDTO.getAccessPath();

        // 检查URL是否需要刷新（过期或即将在1小时内过期）
        boolean needRefresh = urlExpireTime == null ||
                             currentAccessPath == null ||
                             currentAccessPath.isEmpty() ||
                             urlExpireTime.isBefore(now.plusHours(1)); // 提前1小时刷新

        if (needRefresh) {
            log.info("URL需要刷新: fileId={}, currentExpireTime={}, needRefresh={}",
                    fileId, urlExpireTime, needRefresh);

            // 生成新的预签名URL
            String newPresignedUrl = generatePresignedUrl(bucketName, objectName);
            LocalDateTime newExpireTime = now.plusDays(7); // 7天有效期

            // 更新数据库记录（使用乐观锁确保并发安全）
            boolean updateSuccess = updateFileAccessPath(fileId, newPresignedUrl, newExpireTime);

            if (updateSuccess) {
                log.info("URL刷新成功: fileId={}, newExpireTime={}", fileId, newExpireTime);
                return newPresignedUrl;
            } else {
                log.warn("URL更新失败，返回当前URL: fileId={}", fileId);
                // 如果更新失败，返回当前URL（可能是并发更新导致的）
                return currentAccessPath != null ? currentAccessPath : generatePresignedUrl(bucketName, objectName);
            }
        } else {
            log.debug("URL仍然有效: fileId={}, expireTime={}", fileId, urlExpireTime);
            return currentAccessPath;
        }
    }

    /**
     * 生成预签名URL
     *
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @return 预签名URL
     */
    private String generatePresignedUrl(String bucketName, String objectName) {
        try {
            // 生成预签名URL，默认有效期为7天
            java.util.Date expiration = new java.util.Date();
            long expTimeMillis = expiration.getTime();
            expTimeMillis += TimeUnit.DAYS.toMillis(7); // 7天有效期
            expiration.setTime(expTimeMillis);

            GeneratePresignedUrlRequest generatePresignedUrlRequest =
                    new GeneratePresignedUrlRequest(bucketName, objectName)
                            .withMethod(HttpMethod.GET)
                            .withExpiration(expiration);

            URL url = amazonS3.generatePresignedUrl(generatePresignedUrlRequest);
            String presignedUrl = url.toString();

            log.debug("生成预签名URL成功: bucketName={}, objectName={}, url={}",
                    bucketName, objectName, presignedUrl);

            return presignedUrl;
        } catch (SdkClientException e) {
            log.error("生成预签名URL失败: bucketName={}, objectName={}", bucketName, objectName, e);
            throw new RuntimeException("生成预签名URL失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件访问路径和过期时间
     * 使用乐观锁确保并发安全性
     *
     * @param fileId 文件ID
     * @param newAccessPath 新的访问路径
     * @param newExpireTime 新的过期时间
     * @return 更新是否成功
     */
    private boolean updateFileAccessPath(String fileId, String newAccessPath, LocalDateTime newExpireTime) {
        try {
            FileMetadata updateEntity = new FileMetadata();
            updateEntity.setId(fileId);
            updateEntity.setAccessPath(newAccessPath);
            updateEntity.setUrlExpireTime(newExpireTime);

            // 使用updateById进行更新
            boolean success = this.updateById(updateEntity);

            if (success) {
                log.info("文件访问路径更新成功: fileId={}, newExpireTime={}", fileId, newExpireTime);
            } else {
                log.warn("文件访问路径更新失败: fileId={}", fileId);
            }

            return success;
        } catch (Exception e) {
            log.error("更新文件访问路径异常: fileId={}", fileId, e);
            return false;
        }
    }

    /**
     * 删除文件
     *
     * @param fileIds 文件ID列表
     */
    @Override
    public void deleteFiles(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return;
        }
        for (String fileId : fileIds) {
            FileMetadataDTO fileMetadataDTO = getFileMetadata(fileId);
            if (fileMetadataDTO == null) {
                log.warn("文件删除失败：文件元数据未找到，fileId={}", fileId);
                continue;
            }
            try {
                // 从MinIO中删除文件
                amazonS3.deleteObject(fileMetadataDTO.getBucketName(), fileMetadataDTO.getObjectName());
                // 从数据库中删除元数据
                this.removeById(fileId);
                log.info("文件删除成功: fileId={}, objectName={}", fileId, fileMetadataDTO.getObjectName());
            } catch (SdkClientException e) {
                log.error("S3操作异常，文件删除失败: fileId={}, objectName={}, error={}", fileId, fileMetadataDTO.getObjectName(), e.getMessage());
                throw new RuntimeException("文件删除失败: " + e.getMessage());
            }
        }
    }

    /**
     * 构建Content-Disposition头，支持中文文件名
     * 使用RFC 6266标准确保浏览器正确解析文件名
     *
     * @param originalFileName 原始文件名
     * @return Content-Disposition头值
     */
    private String buildContentDisposition(String originalFileName) {
        try {
            // 检查文件名是否包含非ASCII字符（如中文）
            boolean hasNonAscii = !originalFileName.matches("^[\\x00-\\x7F]*$");

            if (hasNonAscii) {
                // 包含中文等非ASCII字符，使用RFC 6266标准
                String encodedFileName = java.net.URLEncoder.encode(originalFileName, "UTF-8")
                        .replaceAll("\\+", "%20"); // 将+号替换为%20

                // 详细日志记录
                log.info("文件名编码处理: originalFileName={}, encodedFileName={}",
                        originalFileName, encodedFileName);

                // 优先使用filename*参数，这是现代浏览器的标准
                String result = String.format("attachment; filename*=UTF-8''%s", encodedFileName);

                log.info("生成的Content-Disposition: {}", result);
                return result;
            } else {
                // 纯ASCII文件名，直接使用
                return String.format("attachment; filename=\"%s\"",
                        originalFileName.replaceAll("\"", "\\\""));
            }
        } catch (Exception e) {
            log.warn("文件名编码失败，使用默认文件名: originalFileName={}, error={}", originalFileName, e.getMessage());
            // 如果编码失败，使用时间戳作为默认文件名
            String defaultName = "download_" + System.currentTimeMillis() + ".file";
            return String.format("attachment; filename=\"%s\"", defaultName);
        }
    }
}