
package com.chinaunicom.ai.knowledge.controller;

import com.chinaunicom.ai.knowledge.props.DemoProperties;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Demo控制器
 *
 * <AUTHOR>
 */
@RefreshScope
@RestController
@RequestMapping("test")
public class TestController {

	@Value("${demo.name}")
	private String name;

	private final DemoProperties properties;

	public TestController(DemoProperties properties) {
		this.properties = properties;
	}


	@GetMapping("name")
	public String getName() {
		return name;
	}

	@GetMapping("name-by-props")
	public String getNameByProps() {
		return properties.getName();
	}

}
