<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.chinaunicom.csf</groupId>
    <artifactId>csf3</artifactId>
    <version>1.8.0-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.chinaunicom.csf</groupId>
  <artifactId>csf3-file</artifactId>
  <name>${project.artifactId}</name>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <description>csf微服务示例</description>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.chinaunicom.csf</groupId>
        <artifactId>csf3-file-service-api</artifactId>
        <version>1.0.0-SNAPSHOT</version>
      </dependency>
      <!-- Spring Cloud BOM，统一管理 Spring Cloud 依赖版本 -->
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>2023.0.1</version> <!-- 适配 Spring Boot 3.2.x 版本 -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.chinaunicom.csf</groupId>
      <artifactId>csf-core-boot</artifactId>
      <version>1.8.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.chinaunicom.csf</groupId>
      <artifactId>csf3-core-swagger</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-archetype-plugin</artifactId>
        <version>3.2.1</version>
        <configuration>
          <encoding>UTF-8</encoding>
          <archetypeFilteredExtentions>java,xml,yml,yamlMETA-INF/*</archetypeFilteredExtentions>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>17</source>
          <target>17</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <modules>
    <module>csf3-file-service-api</module>
    <module>csf3-file-service</module>
  </modules>
</project>
