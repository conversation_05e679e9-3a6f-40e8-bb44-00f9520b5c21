package com.chinaunicom.ai.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 向量化服务配置
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Component
@ConfigurationProperties(prefix = "vectorization")
public class VectorizationConfig {

    /**
     * 数据库更新重试配置
     */
    private RetryConfig retry = new RetryConfig();

    /**
     * 并发控制配置
     */
    private ConcurrencyConfig concurrency = new ConcurrencyConfig();

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {
        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 初始重试延迟（毫秒）
         */
        private int initialDelay = 100;

        /**
         * 重试延迟倍数（指数退避）
         */
        private double backoffMultiplier = 2.0;

        /**
         * 最大重试延迟（毫秒）
         */
        private int maxDelay = 5000;
    }

    /**
     * 并发控制配置
     */
    @Data
    public static class ConcurrencyConfig {
        /**
         * 最大并发向量化任务数
         */
        private int maxConcurrentTasks = 10;

        /**
         * 批处理大小
         */
        private int batchSize = 5;

        /**
         * 批次间延迟（毫秒）
         */
        private int batchDelay = 1000;
    }
}
