package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.evaluation.config.VectorizationConcurrencyConfig;
import com.chinaunicom.ai.knowledge.evaluation.dto.DatasetItem;
import com.chinaunicom.ai.knowledge.evaluation.dto.ImportResult;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;


import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 评测数据导入服务
 * 负责从1doc_QA.json导入数据到数据库
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationDataImporter {
    
    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final EvaluationFileUploader fileUploader;
    private final ObjectMapper objectMapper;
    private final VectorizationConcurrencyConfig concurrencyConfig;
    
    /**
     * 从JSON文件导入数据到指定知识库
     */
    public ImportResult importDataset(Long knowledgeBaseId, Integer maxCount) {
        long startTime = System.currentTimeMillis();

        try {
            // 清理该知识库的旧评测数据，避免去重逻辑影响导入数量
            cleanupOldEvaluationData(knowledgeBaseId);

            // 读取JSON数据
            List<DatasetItem> datasetItems = loadDatasetFromJson();
            log.info("从JSON文件读取到 {} 条数据", datasetItems.size());
            
            // 限制导入数量
            if (maxCount != null && maxCount > 0 && maxCount < datasetItems.size()) {
                datasetItems = datasetItems.subList(0, maxCount);
                log.info("限制导入数量为 {} 条", maxCount);
            }
            
            // 使用分批处理机制，避免向量化服务过载
            return importDocumentsInBatches(datasetItems, knowledgeBaseId, startTime);
            
        } catch (Exception e) {
            log.error("数据导入失败", e);
            return ImportResult.failed("数据导入失败: " + e.getMessage());
        }
    }

    /**
     * 清理指定知识库的旧评测数据
     * 避免去重逻辑影响新的导入数量
     */
    private void cleanupOldEvaluationData(Long knowledgeBaseId) {
        try {
            // 删除该知识库相关的评测文档记录
            int deletedCount = evaluationDocumentMapper.delete(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<EvaluationDocument>()
                    .eq(EvaluationDocument::getKnowledgeBaseId, knowledgeBaseId)
            );

            if (deletedCount > 0) {
                log.info("清理知识库 {} 的旧评测数据，删除 {} 条记录", knowledgeBaseId, deletedCount);
            }
        } catch (Exception e) {
            log.warn("清理知识库 {} 的旧评测数据失败，继续导入流程", knowledgeBaseId, e);
        }
    }

    /**
     * 从JSON文件加载数据集
     */
    private List<DatasetItem> loadDatasetFromJson() throws IOException {
        ClassPathResource resource = new ClassPathResource("data_sets/1doc_QA.json");
        try (InputStream inputStream = resource.getInputStream()) {
            return objectMapper.readValue(inputStream, new TypeReference<List<DatasetItem>>() {});
        }
    }
    
    /**
     * 创建评测文档实体
     */
    private EvaluationDocument createEvaluationDocument(DatasetItem item, Long knowledgeBaseId) {
        EvaluationDocument document = new EvaluationDocument();
        document.setOriginalDocId(item.getId());
        document.setKnowledgeBaseId(knowledgeBaseId);
        document.setEvent(item.getEvent());
        document.setNews1(item.getNews1());
        document.setQuestions(item.getQuestions());
        document.setAnswers(item.getAnswers());
        document.setFileName(item.getId() + ".txt");
        // 生成带扩展名的fileObjectId，与现有系统兼容
        String baseObjectId = UUID.randomUUID().toString().replace("-", "");
        document.setFileObjectId(baseObjectId + ".txt");
        document.setStatus("PENDING");
        document.setCreateTime(LocalDateTime.now());
        document.setUpdateTime(LocalDateTime.now());
        return document;
    }
    
    /**
     * 获取数据集统计信息
     */
    public DatasetInfo getDatasetInfo() {
        try {
            List<DatasetItem> datasetItems = loadDatasetFromJson();
            DatasetInfo info = new DatasetInfo();
            info.setTotalCount(datasetItems.size());
            info.setFilePath("data_sets/1doc_QA.json");
            return info;
        } catch (IOException e) {
            log.error("读取数据集信息失败", e);
            return null;
        }
    }
    
    /**
     * 重用已存在的文档，将其关联到当前知识库
     */
    private boolean reuseExistingDocument(EvaluationDocument existing, Long knowledgeBaseId) {
        try {
            // 检查该文档是否已经关联到当前知识库
            if (knowledgeBaseId.equals(existing.getKnowledgeBaseId())) {
                log.debug("文档 {} 已经关联到知识库 {}，无需重复处理", existing.getOriginalDocId(), knowledgeBaseId);
                return true;
            }

            // 检查是否已有对应的知识库文档记录
            if (existing.getKnowledgeDocId() != null) {
                // 直接重新上传文档内容到当前知识库（不创建EvaluationDocument记录）
                return uploadDocumentContentToKnowledgeBase(existing, knowledgeBaseId);
            } else {
                // 如果没有知识库文档记录，重新上传
                return uploadDocumentContentToKnowledgeBase(existing, knowledgeBaseId);
            }

        } catch (Exception e) {
            log.error("重用文档 {} 失败", existing.getOriginalDocId(), e);
            return false;
        }
    }

    /**
     * 上传文档内容到知识库并创建关联记录
     * 策略：创建新的EvaluationDocument记录，但保持原始originalDocId不变，
     * 通过在测试时扩展ID匹配逻辑来处理重用文档的情况
     */
    private boolean uploadDocumentContentToKnowledgeBase(EvaluationDocument existing, Long knowledgeBaseId) {
        try {
            // 创建新的EvaluationDocument记录
            EvaluationDocument newDocument = new EvaluationDocument();
            // 为了避免唯一约束冲突，在originalDocId后添加知识库ID后缀
            // 在测试时会通过特殊逻辑处理这种情况
            newDocument.setOriginalDocId(existing.getOriginalDocId() + "_reuse_kb" + knowledgeBaseId);
            newDocument.setKnowledgeBaseId(knowledgeBaseId);
            newDocument.setEvent(existing.getEvent());
            newDocument.setNews1(existing.getNews1());
            newDocument.setQuestions(existing.getQuestions());
            newDocument.setAnswers(existing.getAnswers());
            newDocument.setFileName(existing.getFileName());
            // 生成新的fileObjectId
            String baseObjectId = UUID.randomUUID().toString().replace("-", "");
            newDocument.setFileObjectId(baseObjectId + ".txt");
            newDocument.setStatus("PENDING");
            newDocument.setCreateTime(LocalDateTime.now());
            newDocument.setUpdateTime(LocalDateTime.now());

            // 先插入数据库记录
            evaluationDocumentMapper.insert(newDocument);

            // 然后上传文件到知识库
            boolean uploadSuccess = fileUploader.uploadDocument(newDocument);
            if (uploadSuccess) {
                log.debug("成功重用文档: {} 到知识库 {}", existing.getOriginalDocId(), knowledgeBaseId);
                return true;
            } else {
                log.error("重用文档失败: {}", existing.getOriginalDocId());
                // 如果上传失败，删除刚插入的记录
                evaluationDocumentMapper.deleteById(newDocument.getId());
                return false;
            }

        } catch (Exception e) {
            log.error("重用文档 {} 失败", existing.getOriginalDocId(), e);
            return false;
        }
    }

    /**
     * 分批处理文档导入，避免向量化服务过载
     *
     * @param datasetItems 要导入的数据集项目
     * @param knowledgeBaseId 知识库ID
     * @param startTime 开始时间
     * @return 导入结果
     */
    private ImportResult importDocumentsInBatches(List<DatasetItem> datasetItems, Long knowledgeBaseId, long startTime) {
        // 从配置中获取向量化服务并发限制参数
        final int BATCH_SIZE = concurrencyConfig.getEffectiveBatchSize();
        final int BATCH_WAIT_SECONDS = concurrencyConfig.getBatchWaitSeconds();

        List<String> failedDocIds = new ArrayList<>();
        int successCount = 0;
        int totalBatches = (int) Math.ceil((double) datasetItems.size() / BATCH_SIZE);

        log.info("开始分批导入文档，总数: {}, 批次大小: {}, 总批次数: {}",
                datasetItems.size(), BATCH_SIZE, totalBatches);

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int startIndex = batchIndex * BATCH_SIZE;
            int endIndex = Math.min(startIndex + BATCH_SIZE, datasetItems.size());
            List<DatasetItem> batch = datasetItems.subList(startIndex, endIndex);

            log.info("处理第 {}/{} 批，文档数量: {} (索引 {}-{})",
                    batchIndex + 1, totalBatches, batch.size(), startIndex, endIndex - 1);

            // 处理当前批次
            BatchResult batchResult = processBatch(batch, knowledgeBaseId, batchIndex + 1);
            successCount += batchResult.getSuccessCount();
            failedDocIds.addAll(batchResult.getFailedDocIds());

            // 如果不是最后一批，等待一段时间让向量化服务处理
            if (batchIndex < totalBatches - 1) {
                log.info("等待 {} 秒后处理下一批，避免向量化服务过载...", BATCH_WAIT_SECONDS);
                try {
                    Thread.sleep(BATCH_WAIT_SECONDS * 1000L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("批次等待被中断，继续处理下一批");
                }
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        int failedCount = failedDocIds.size();

        log.info("分批导入完成，总数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                datasetItems.size(), successCount, failedCount, duration);

        if (failedCount == 0) {
            return ImportResult.success(datasetItems.size(), successCount, duration, knowledgeBaseId);
        } else {
            return ImportResult.partial(datasetItems.size(), successCount, failedCount, failedDocIds, duration, knowledgeBaseId);
        }
    }

    /**
     * 处理单个批次的文档
     *
     * @param batch 批次中的文档列表
     * @param knowledgeBaseId 知识库ID
     * @param batchNumber 批次编号
     * @return 批次处理结果
     */
    private BatchResult processBatch(List<DatasetItem> batch, Long knowledgeBaseId, int batchNumber) {
        List<String> failedDocIds = new ArrayList<>();
        int successCount = 0;

        for (DatasetItem item : batch) {
            try {
                // 检查是否已存在
                EvaluationDocument existing = evaluationDocumentMapper.selectByOriginalDocId(item.getId());
                if (existing != null) {
                    log.debug("批次 {} - 文档 {} 已存在，将其关联到当前知识库 {}", batchNumber, item.getId(), knowledgeBaseId);
                    // 将已存在的文档关联到当前知识库
                    boolean reuseSuccess = reuseExistingDocument(existing, knowledgeBaseId);
                    if (reuseSuccess) {
                        successCount++;
                        log.debug("批次 {} - 成功重用文档: {}", batchNumber, item.getId());
                    } else {
                        failedDocIds.add(item.getId());
                        log.error("批次 {} - 重用文档失败: {}", batchNumber, item.getId());
                    }
                    continue;
                }

                // 创建评测文档记录
                EvaluationDocument document = createEvaluationDocument(item, knowledgeBaseId);
                evaluationDocumentMapper.insert(document);

                // 上传文件到知识库
                boolean uploadSuccess = fileUploader.uploadDocument(document);
                if (uploadSuccess) {
                    successCount++;
                    log.debug("批次 {} - 成功导入文档: {}", batchNumber, item.getId());
                } else {
                    failedDocIds.add(item.getId());
                    log.error("批次 {} - 文档上传失败: {}", batchNumber, item.getId());
                }

            } catch (Exception e) {
                log.error("批次 {} - 导入文档 {} 时发生错误", batchNumber, item.getId(), e);
                failedDocIds.add(item.getId());
            }
        }

        log.info("批次 {} 处理完成，成功: {}, 失败: {}", batchNumber, successCount, failedDocIds.size());
        return new BatchResult(successCount, failedDocIds);
    }

    /**
     * 批次处理结果
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    private static class BatchResult {
        private int successCount;
        private List<String> failedDocIds;
    }

    @lombok.Data
    public static class DatasetInfo {
        private Integer totalCount;
        private String filePath;
    }
}
