package com.chinaunicom.ai.knowledge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:模型表
 * @date 2025/6/3 17:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AiModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建用户ID
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新用户ID
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 模型名称
     */
    private String displayName;

    /**
     * 模型类型 1.大语言模型 2.向量嵌入模型
     */
    private Long modelType;

    /**
     * 模型接口配置
     */
    private String modelApiCnf;

    private String tenantId;

    /**
     * 维度
     */
    private Integer dims;


}
