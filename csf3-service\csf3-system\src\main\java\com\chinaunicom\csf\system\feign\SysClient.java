
package com.chinaunicom.csf.system.feign;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.system.entity.Dept;
import com.chinaunicom.csf.system.entity.Role;
import com.chinaunicom.csf.system.entity.Tenant;
import com.chinaunicom.csf.system.service.IDeptService;
import com.chinaunicom.csf.system.service.IPostService;
import com.chinaunicom.csf.system.service.IRoleService;
import com.chinaunicom.csf.system.service.ITenantService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统服务Feign实现类
 *
 */
@Tag(name = "ExcludedTag")
@RestController
@AllArgsConstructor
public class SysClient implements ISysClient {

	private IDeptService deptService;

	private IPostService postService;

	private IRoleService roleService;

	private ITenantService tenantService;

	@Override
	@GetMapping(API_PREFIX + "/getDept")
	public Dept getDept(Long id) {
		return deptService.getById(id);
	}

	@Override
	@GetMapping(API_PREFIX + "/getDeptName")
	public String getDeptName(Long id) {
		return deptService.getById(id).getDeptName();
	}

	@Override
	public String getDeptIds(String tenantId, String deptNames) {
		return deptService.getDeptIds(tenantId, deptNames);
	}

	@Override
	public List<String> getDeptNames(String deptIds) {
		return deptService.getDeptNames(deptIds);
	}

	@Override
	public String getPostIds(String tenantId, String postNames) {
		return postService.getPostIds(tenantId, postNames);
	}

	@Override
	public List<String> getPostNames(String postIds) {
		return postService.getPostNames(postIds);
	}

	@Override
	@GetMapping(API_PREFIX + "/getRole")
	public Role getRole(Long id) {
		return roleService.getById(id);
	}

	@Override
	public String getRoleIds(String tenantId, String roleNames) {
		return roleService.getRoleIds(tenantId, roleNames);
	}

	@Override
	@GetMapping(API_PREFIX + "/getRoleName")
	public String getRoleName(Long id) {
		return roleService.getById(id).getRoleName();
	}

	@Override
	public List<String> getRoleNames(String roleIds) {
		return roleService.getRoleNames(roleIds);
	}

	@Override
	@GetMapping(API_PREFIX + "/getRoleAlias")
	public String getRoleAlias(Long id) {
		return roleService.getById(id).getRoleAlias();
	}

	@Override
	@GetMapping(API_PREFIX + "/tenant")
	public R<Tenant> getTenant(Long id) {
		return R.data(tenantService.getById(id));
	}

	@Override
	@GetMapping(API_PREFIX + "/tenant-id")
	public R<Tenant> getTenant(String tenantId) {
		return R.data(tenantService.getByTenantId(tenantId));
	}

	@Override
	public List<Tenant> getTenantList(String tenantIds) {
		LambdaQueryWrapper<Tenant> tenantLambdaQueryWrapper = new LambdaQueryWrapper<>();
		tenantLambdaQueryWrapper.in(Tenant::getTenantId, Func.toStrList(tenantIds));
		return tenantService.list(tenantLambdaQueryWrapper);
	}
}
