package com.chinaunicom.ai.knowledge.enums;

import lombok.Getter;

/**
 * 文档状态枚举
 */
@Getter
public enum DocumentStatusEnum {
    UPLOADING(0, "上传中"),
    UPLOADED(1, "已上传"),
    VECTORIZING(2, "向量化中"),
    VECTORIZED(3, "已向量化"),
    UPLOAD_FAILED(4, "上传失败"),
    VECTORIZE_FAILED(5, "向量化失败"),
    DELETED(6, "已删除"); // 新增：文档逻辑删除状态

    private final Integer code;
    private final String name;

    DocumentStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取状态名称
     *
     * @param code 状态码
     * @return 状态名称
     */
    public static String getNameByCode(Integer code) {
        for (DocumentStatusEnum status : DocumentStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "未知状态";
    }
}