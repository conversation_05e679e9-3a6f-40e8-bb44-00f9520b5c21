package com.chinaunicom.csf.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 完成分片上传请求DTO
 */
@Data
public class CompleteMultipartUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID (32位不含横线的UUID)
     */
    private String fileId;

    /**
     * MinIO 中的对象名
     */
    private String objectName;

    /**
     * 存储的桶名
     */
    private String bucketName;

    /**
     * Upload ID，用于完成分片上传
     */
    private String uploadId;

    /**
     * 已上传分片的信息列表
     */
    private List<PartETag> partETags;

    /**
     * 分片Etag信息
     */
    @Data
    public static class PartETag implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 分片编号
         */
        private Integer partNumber;
        /**
         * 分片Etag
         */
        private String etag;
    }
} 