/*
package com.chinaunicom.ai.knowledge.config;

import co.elastic.clients.elasticsearch.ElasticsearchClient; // Import ElasticsearchClient
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;

@Configuration
// 只有当 elasticsearch.enabled 为 true 时，才加载此配置类中的所有 Elasticsearch 相关 Bean
@ConditionalOnProperty(name = "elasticsearch.enabled", havingValue = "true", matchIfMissing = true)
// extends ElasticsearchConfiguration
public class ElasticsearchConfig {

    @Value("${spring.elasticsearch.uris}")
    private String elasticsearchUris;

    @Value("${spring.elasticsearch.username}")
    private String elasticsearchUsername;

    @Value("${spring.elasticsearch.password}")
    private String elasticsearchPassword;

    @Bean
    // @Override
    public ClientConfiguration clientConfiguration() {
        return ClientConfiguration.builder()
                .connectedTo(elasticsearchUris)
                .withBasicAuth(elasticsearchUsername, elasticsearchPassword)
                .build();
    }

    */
/**
     * 定义 ElasticsearchClient Bean。
     * 这是新的 Elasticsearch Java API Client，用于与 Elasticsearch 进行低级别交互。
     * 它将使用上面定义的 ClientConfiguration。
     * @param clientConfiguration 注入的 ClientConfiguration Bean
     * @return ElasticsearchClient 实例
     *//*

    @Bean
    public ElasticsearchClient elasticsearchClient(ClientConfiguration clientConfiguration) {
        return RestClients.create(clientConfiguration).rest();
    }

    */
/**
     * 定义 ElasticsearchTemplate bean。
     * 使用 org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate
     * 它与新的 ElasticsearchClient (Java API Client) 兼容。
     * @param elasticsearchClient Spring Data Elasticsearch 自动提供的 ElasticsearchClient 实例
     * @param elasticsearchConverter Spring Data Elasticsearch 自动提供的 ElasticsearchConverter 实例
     * @return ElasticsearchTemplate 实例
     *//*

    @Bean
    public ElasticsearchTemplate elasticsearchTemplate(ElasticsearchClient elasticsearchClient, ElasticsearchConverter elasticsearchConverter) {
        return new ElasticsearchTemplate(elasticsearchClient, elasticsearchConverter);
    }
}
*/
