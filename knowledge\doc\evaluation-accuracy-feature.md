# 评测功能平均准确率计算说明

## 概述

为EvaluationController的`/run-evaluation`接口新增了平均准确率计算功能，提供更精确的检索质量评估指标。

## 功能特性

### ✅ 新增字段

#### EvaluationResult
- **averageAccuracy** (Double): 平均准确率，范围0.0-1.0
  - 计算公式：所有问题准确率的平均值
  - 示例：0.667 表示66.7%的平均准确率

#### TestResult
- **accuracy** (Double): 单个问题的准确率，范围0.0-1.0
  - 计算公式：召回结果中正确文档数量 / 召回结果总数量
  - 示例：召回3个文档，其中1个正确，准确率 = 1/3 ≈ 0.333

### 🎯 计算逻辑

#### 单个问题准确率计算
```java
// 示例：召回文档列表 [doc1, doc2, doc3]，期望文档 doc2
List<String> recalledDocIds = ["doc1", "doc2", "doc3"];
String expectedDocId = "doc2";

int correctDocCount = 0;
for (String docId : recalledDocIds) {
    if (expectedDocId.equals(docId)) {
        correctDocCount++;
    }
}

double accuracy = recalledDocIds.size() > 0 ? 
    (double) correctDocCount / recalledDocIds.size() : 0.0;
// 结果：accuracy = 1/3 ≈ 0.333 (33.3%)
```

#### 平均准确率计算
```java
double totalAccuracy = 0.0;
int validQuestionCount = 0;

for (TestResult result : testResults) {
    if (result.getAccuracy() != null && 
        result.getExpectedDocId() != null && 
        !result.getExpectedDocId().trim().isEmpty()) {
        totalAccuracy += result.getAccuracy();
        validQuestionCount++;
    }
}

double averageAccuracy = validQuestionCount > 0 ? 
    totalAccuracy / validQuestionCount : 0.0;
```

### 🔍 边界情况处理

#### 1. 召回结果为空
```json
{
  "question": "测试问题",
  "expectedDocId": "doc1",
  "recalledDocIds": [],
  "accuracy": 0.0,
  "isCorrect": false
}
```

#### 2. 期望文档ID为空
```java
// 跳过该问题，不参与平均准确率计算
if (document.getOriginalDocId() == null || 
    document.getOriginalDocId().trim().isEmpty()) {
    log.warn("文档 {} 的originalDocId为空，跳过准确率计算", document.getId());
    continue;
}
```

#### 3. 执行异常
```json
{
  "question": "测试问题",
  "expectedDocId": "doc1",
  "accuracy": 0.0,
  "isCorrect": false,
  "recallCount": 0,
  "recalledDocIds": [],
  "similarityScores": []
}
```

#### 4. 没有有效问题
```json
{
  "knowledgeBaseId": 123,
  "totalQuestions": 0,
  "averageAccuracy": 0.0,
  "recallRate": 0.0
}
```

## 响应格式示例

### 完整响应示例
```json
{
  "code": 200,
  "success": true,
  "data": {
    "knowledgeBaseId": 123,
    "totalQuestions": 3,
    "correctRecalls": 2,
    "recallRate": 0.667,
    "averageAccuracy": 0.556,
    "avgExecutionTime": 150.0,
    "testResults": [
      {
        "question": "什么是人工智能？",
        "expectedDocId": "doc1",
        "isCorrect": true,
        "accuracy": 0.5,
        "recallCount": 2,
        "recalledDocIds": ["doc1", "doc2"],
        "similarityScores": [0.95, 0.78],
        "executionTime": 120
      },
      {
        "question": "机器学习的应用？",
        "expectedDocId": "doc2",
        "isCorrect": true,
        "accuracy": 0.333,
        "recallCount": 3,
        "recalledDocIds": ["doc2", "doc3", "doc4"],
        "similarityScores": [0.88, 0.72, 0.65],
        "executionTime": 180
      },
      {
        "question": "深度学习原理？",
        "expectedDocId": "doc3",
        "isCorrect": false,
        "accuracy": 0.0,
        "recallCount": 2,
        "recalledDocIds": ["doc4", "doc5"],
        "similarityScores": [0.70, 0.68],
        "executionTime": 150
      }
    ],
    "startTime": "2025-01-07T10:30:00",
    "endTime": "2025-01-07T10:30:05",
    "totalDuration": 5000
  },
  "msg": "评测测试完成"
}
```

### 计算验证
```
问题1: accuracy = 1/2 = 0.5 (召回2个，正确1个)
问题2: accuracy = 1/3 ≈ 0.333 (召回3个，正确1个)
问题3: accuracy = 0/2 = 0.0 (召回2个，正确0个)

平均准确率 = (0.5 + 0.333 + 0.0) / 3 ≈ 0.278

但由于问题3的expectedDocId存在且有效，所以：
averageAccuracy = (0.5 + 0.333 + 0.0) / 3 ≈ 0.278
```

## 指标对比

### 召回率 vs 准确率

| 指标 | 定义 | 计算公式 | 关注点 |
|------|------|----------|--------|
| **召回率** | 正确召回的问题比例 | 正确召回数 / 总问题数 | 能否找到正确答案 |
| **准确率** | 召回结果中正确文档的比例 | 正确文档数 / 召回文档数 | 召回结果的精确性 |

### 示例对比
```
场景1: 召回[doc1, doc2], 期望doc1
- 召回率贡献: 1 (找到了正确文档)
- 准确率: 1/2 = 0.5 (50%的召回结果是正确的)

场景2: 召回[doc1, doc2, doc3, doc4, doc5], 期望doc1  
- 召回率贡献: 1 (找到了正确文档)
- 准确率: 1/5 = 0.2 (20%的召回结果是正确的)
```

## 性能考虑

### 计算复杂度
- **时间复杂度**: O(n*m)，其中n为问题数，m为平均召回文档数
- **空间复杂度**: O(1)，只使用常量额外空间
- **数据库查询**: 每个召回文档需要一次ID映射查询

### 优化建议
1. **批量查询**: 考虑批量查询文档ID映射关系
2. **缓存机制**: 缓存文档ID映射关系
3. **异步处理**: 对于大量问题，考虑异步处理

## 向后兼容性

### ✅ 完全兼容
- 保留所有现有字段和功能
- 新增字段使用默认值，不影响现有逻辑
- API接口路径和参数保持不变

### 现有字段保持不变
- `recallRate`: 召回率计算逻辑不变
- `correctRecalls`: 正确召回数统计不变
- `testResults`: 现有字段全部保留
- `isCorrect`: 布尔值判断逻辑不变

## 日志增强

### 新增日志信息
```
INFO - 评测完成，知识库ID: 123, 总问题数: 10, 正确召回数: 8, 召回率: 80.00%, 平均准确率: 65.50%
```

### 调试日志
```
DEBUG - 测试完成 - 问题: 什么是人工智能？, 期望文档: doc1, 召回正确: true, 召回数量: 3, 准确率: 33.33%
```

## 使用建议

### 指标解读
1. **高召回率 + 高准确率**: 理想状态，检索质量优秀
2. **高召回率 + 低准确率**: 能找到答案但噪音较多，需要优化排序
3. **低召回率 + 高准确率**: 结果精确但覆盖不足，需要扩大召回
4. **低召回率 + 低准确率**: 检索效果差，需要全面优化

### 优化方向
- **提升召回率**: 优化向量模型、调整相似度阈值
- **提升准确率**: 优化排序算法、引入重排序机制
- **平衡两者**: 根据业务需求调整召回和精确性的权衡
