<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.csf.system.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deptResultMap" type="com.chinaunicom.csf.system.entity.Dept">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_name" property="deptName"/>
        <result column="full_name" property="fullName"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.chinaunicom.csf.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select
        id, parent_id, dept_name, full_name, sort, remark, is_deleted
    </sql>

    <select id="selectDeptPage" resultMap="deptResultMap">
        select * from csf_dept where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dept_name as title, id as 'value', id as 'key' from csf_dept where is_deleted = 0
        <if test="_parameter!=null">
            and tenant_id = #{_parameter}
        </if>
    </select>
    <select id="tree" resultMap="treeNodeResultMap" databaseId="dm">
        select id, parent_id, dept_name as title, id as "value", id as "key" from csf_dept where is_deleted = 0
        <if test="_parameter!=null">
            and tenant_id = #{_parameter}
        </if>
    </select>

    <select id="getDeptNames" resultType="java.lang.String">
        SELECT
        dept_name
        FROM
        csf_dept
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>
