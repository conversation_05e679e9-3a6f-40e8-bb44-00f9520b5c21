package com.chinaunicom.ai.knowledge.service;

import com.chinaunicom.ai.knowledge.config.DualRouteConfig;
import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.dto.DualRouteSearchResult;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.json.JsonData;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 双路检索服务
 * 管理精确匹配和混合检索的并行执行
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DualRouteSearchService {
    
    private final ElasticsearchOperations elasticsearchTemplate;
    private final IElasticSearchService elasticSearchService;
    
    /**
     * 执行双路并行检索
     * 
     * @param indexName 索引名称
     * @param queryText 查询文本
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @param knowledgeBaseId 知识库ID
     * @param tenantId 租户ID
     * @param config 双路检索配置
     * @param hybridConfig 混合检索配置
     * @return 双路检索结果
     */
    public DualRouteSearchResult executeSearch(String indexName, String queryText, float[] queryVector,
                                             int topK, Long knowledgeBaseId, String tenantId,
                                             DualRouteConfig config, HybridSearchConfig hybridConfig) {
        
        log.info("🚀 开始双路并行检索 - 索引: {}, 查询: '{}', topK: {}, 超时: {}秒", 
                indexName, queryText, topK, config.getSearchTimeoutSeconds());
        
        DualRouteSearchResult result = new DualRouteSearchResult();
        
        // 并行执行两路检索
        CompletableFuture<Void> exactMatchFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<AgentSearchResultVO> exactResults = exactMatchSearch(
                        indexName, queryText, topK, knowledgeBaseId);
                result.setExactMatchResults(exactResults);
                result.setExactMatchSuccess(true);
                log.debug("✅ 精确匹配完成 - 结果数: {}", exactResults.size());
            } catch (Exception e) {
                log.warn("❌ 精确匹配失败: {}", e.getMessage());
                result.setExactMatchResults(new ArrayList<>());
                result.setExactMatchSuccess(false);
            } finally {
                result.setExactMatchTime(System.currentTimeMillis() - startTime);
            }
        });
        
        CompletableFuture<Void> hybridSearchFuture = CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                List<AgentSearchResultVO> hybridResults = elasticSearchService.searchHybrid(
                        indexName, queryText, queryVector, topK, knowledgeBaseId, tenantId, hybridConfig);
                result.setHybridSearchResults(hybridResults);
                result.setHybridSearchSuccess(true);
                log.debug("✅ 混合检索完成 - 结果数: {}", hybridResults.size());
            } catch (Exception e) {
                log.warn("❌ 混合检索失败: {}", e.getMessage());
                result.setHybridSearchResults(new ArrayList<>());
                result.setHybridSearchSuccess(false);
            } finally {
                result.setHybridSearchTime(System.currentTimeMillis() - startTime);
            }
        });
        
        // 等待两路检索完成，设置超时
        try {
            CompletableFuture.allOf(exactMatchFuture, hybridSearchFuture)
                    .get(config.getSearchTimeoutSeconds(), TimeUnit.SECONDS);
            
            log.info("🎯 双路检索完成 - 精确匹配: {}个结果({}ms), 混合检索: {}个结果({}ms)", 
                    result.getExactMatchResults().size(), result.getExactMatchTime(),
                    result.getHybridSearchResults().size(), result.getHybridSearchTime());
                    
        } catch (Exception e) {
            log.warn("⏰ 双路检索超时或异常: {}", e.getMessage());
            result.setErrorMessage(e.getMessage());
            
            // 取消未完成的任务
            exactMatchFuture.cancel(true);
            hybridSearchFuture.cancel(true);
            
            // 构建降级结果
            return buildFallbackResult(indexName, queryText, queryVector, topK, 
                    knowledgeBaseId, tenantId, hybridConfig);
        }
        
        return result;
    }
    
    /**
     * 精确匹配检索
     * 使用ES的match查询 + operator=AND实现精确匹配
     */
    private List<AgentSearchResultVO> exactMatchSearch(String indexName, String queryText, 
                                                      int topK, Long knowledgeBaseId) {
        
        List<AgentSearchResultVO> results = new ArrayList<>();
        
        try {
            // 构建精确匹配查询（所有词都必须匹配）
            MatchQuery exactQuery = MatchQuery.of(m -> m
                    .field("text")
                    .query(queryText)
                    .operator(co.elastic.clients.elasticsearch._types.query_dsl.Operator.And)
                    .boost(2.0f) // 提高精确匹配的权重
            );
            
            co.elastic.clients.elasticsearch._types.query_dsl.Query esQuery = 
                    co.elastic.clients.elasticsearch._types.query_dsl.Query.of(q -> q.match(exactQuery));
            
            // 构建查询
            Query query = NativeSearchQuery.builder()
                    .withQuery(esQuery)
                    .withMaxResults(topK)
                    .build();
            
            // 执行查询
            @SuppressWarnings("rawtypes")
            SearchHits<Map> searchHits = elasticsearchTemplate.search(query, Map.class,
                    org.springframework.data.elasticsearch.core.mapping.IndexCoordinates.of(indexName));
            
            // 转换结果
            for (SearchHit<Map> hit : searchHits) {
                AgentSearchResultVO vo = convertHitToSearchResult(hit, knowledgeBaseId);
                if (vo != null) {
                    results.add(vo);
                }
            }
            
            log.debug("精确匹配检索完成 - 索引: {}, 查询: '{}', 结果数: {}", indexName, queryText, results.size());
            
        } catch (Exception e) {
            log.error("精确匹配检索失败 - 索引: {}, 查询: '{}', 错误: {}", indexName, queryText, e.getMessage(), e);
            throw e;
        }
        
        return results;
    }
    
    /**
     * 构建降级结果
     * 当双路检索超时或失败时，降级到混合检索
     */
    private DualRouteSearchResult buildFallbackResult(String indexName, String queryText, float[] queryVector,
                                                     int topK, Long knowledgeBaseId, String tenantId,
                                                     HybridSearchConfig hybridConfig) {
        
        log.info("🔄 执行降级策略 - 使用混合检索");
        
        DualRouteSearchResult fallbackResult = new DualRouteSearchResult();
        
        try {
            long startTime = System.currentTimeMillis();
            List<AgentSearchResultVO> hybridResults = elasticSearchService.searchHybrid(
                    indexName, queryText, queryVector, topK, knowledgeBaseId, tenantId, hybridConfig);
            
            fallbackResult.setHybridSearchResults(hybridResults);
            fallbackResult.setHybridSearchSuccess(true);
            fallbackResult.setHybridSearchTime(System.currentTimeMillis() - startTime);
            fallbackResult.setExactMatchResults(new ArrayList<>());
            fallbackResult.setExactMatchSuccess(false);
            
            log.info("✅ 降级策略执行成功 - 混合检索结果数: {}", hybridResults.size());
            
        } catch (Exception e) {
            log.error("❌ 降级策略也失败了: {}", e.getMessage());
            fallbackResult.setErrorMessage("双路检索和降级策略都失败: " + e.getMessage());
        }
        
        return fallbackResult;
    }
    
    /**
     * 将SearchHit转换为AgentSearchResultVO
     */
    @SuppressWarnings("rawtypes")
    private AgentSearchResultVO convertHitToSearchResult(SearchHit<Map> hit, Long knowledgeBaseId) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> source = hit.getContent();

            AgentSearchResultVO vo = new AgentSearchResultVO();
            vo.setKnowledgeBaseId(knowledgeBaseId);
            vo.setScore(hit.getScore());

            // 设置文档内容
            if (source.get("text") != null) {
                vo.setFragmentContent(String.valueOf(source.get("text")));
            }

            // 从 metadata 中获取文档信息
            if (source.get("metadata") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> metadata = (Map<String, Object>) source.get("metadata");

                // 设置文档ID
                Object docIdObj = metadata.get("mysqlDocId");
                if (docIdObj != null) {
                    vo.setDocumentId(Long.valueOf(String.valueOf(docIdObj)));
                }

                // 设置文档名称
                Object fileNameObj = metadata.get("fileName");
                if (fileNameObj != null) {
                    vo.setDocumentName(String.valueOf(fileNameObj));
                }
            }

            return vo;

        } catch (Exception e) {
            log.warn("转换搜索结果失败: {}", e.getMessage());
            return null;
        }
    }
}
