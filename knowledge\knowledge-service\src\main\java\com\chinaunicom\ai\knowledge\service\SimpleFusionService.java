package com.chinaunicom.ai.knowledge.service;

import com.chinaunicom.ai.knowledge.config.DualRouteConfig;
import com.chinaunicom.ai.knowledge.dto.DualRouteSearchResult;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 简单融合服务
 * 处理双路检索结果的分数标准化、去重和加权融合
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleFusionService {
    
    private final RerankService rerankService;
    
    /**
     * 融合双路检索结果
     * 
     * @param searchResult 双路检索结果
     * @param config 双路检索配置
     * @param queryText 查询文本
     * @param knowledgeBaseId 知识库ID
     * @param knowledgeBaseName 知识库名称
     * @return 融合并重排序后的最终结果
     */
    public List<AgentSearchResultVO> fuseResults(DualRouteSearchResult searchResult, DualRouteConfig config,
                                               String queryText, Long knowledgeBaseId, String knowledgeBaseName) {
        
        log.info("🔀 开始融合双路检索结果 - 精确匹配: {}个, 混合检索: {}个", 
                searchResult.getExactMatchResults().size(), 
                searchResult.getHybridSearchResults().size());
        
        // 1. 收集所有结果
        List<AgentSearchResultVO> allResults = new ArrayList<>();
        Map<String, String> resultSources = new HashMap<>(); // 记录结果来源
        
        // 添加精确匹配结果
        if (searchResult.isExactMatchSuccess() && !CollectionUtils.isEmpty(searchResult.getExactMatchResults())) {
            for (AgentSearchResultVO result : searchResult.getExactMatchResults()) {
                allResults.add(result);
                resultSources.put(generateResultKey(result), "精确匹配");
            }
        }
        
        // 添加混合检索结果
        if (searchResult.isHybridSearchSuccess() && !CollectionUtils.isEmpty(searchResult.getHybridSearchResults())) {
            for (AgentSearchResultVO result : searchResult.getHybridSearchResults()) {
                allResults.add(result);
                resultSources.put(generateResultKey(result), "混合检索");
            }
        }
        
        if (allResults.isEmpty()) {
            log.warn("⚠️ 双路检索无任何结果，返回空列表");
            return new ArrayList<>();
        }
        
        // 2. 分数标准化
        List<AgentSearchResultVO> normalizedResults = normalizeScoresMinMax(allResults, resultSources);
        
        // 3. 基础去重
        List<AgentSearchResultVO> deduplicatedResults = removeBasicDuplicates(normalizedResults, config);
        
        // 4. 简单加权融合
        List<AgentSearchResultVO> fusedResults = applySimpleWeights(deduplicatedResults, config, resultSources);
        
        // 5. 限制结果数量
        List<AgentSearchResultVO> limitedResults = fusedResults.stream()
                .limit(config.getMaxFinalResults())
                .collect(Collectors.toList());
        
        log.info("🎯 融合完成 - 原始: {}个, 去重后: {}个, 最终: {}个", 
                allResults.size(), deduplicatedResults.size(), limitedResults.size());
        
        // 6. 调用Rerank服务进行最终重排序
        return callRerankService(queryText, limitedResults, knowledgeBaseId, knowledgeBaseName);
    }
    
    /**
     * Min-Max分数标准化
     */
    private List<AgentSearchResultVO> normalizeScoresMinMax(List<AgentSearchResultVO> results, 
                                                           Map<String, String> resultSources) {
        
        if (results.size() <= 1) {
            return new ArrayList<>(results);
        }
        
        // 按来源分组进行标准化
        Map<String, List<AgentSearchResultVO>> sourceGroups = results.stream()
                .collect(Collectors.groupingBy(result -> 
                        resultSources.getOrDefault(generateResultKey(result), "未知")));
        
        List<AgentSearchResultVO> normalizedResults = new ArrayList<>();
        
        for (Map.Entry<String, List<AgentSearchResultVO>> entry : sourceGroups.entrySet()) {
            String source = entry.getKey();
            List<AgentSearchResultVO> sourceResults = entry.getValue();
            
            if (sourceResults.size() == 1) {
                // 单个结果设置为中等分数
                sourceResults.get(0).setScore(5.0f);
                normalizedResults.addAll(sourceResults);
                continue;
            }
            
            // 计算最小值和最大值
            float minScore = sourceResults.stream().map(AgentSearchResultVO::getScore).min(Float::compare).orElse(0.0f);
            float maxScore = sourceResults.stream().map(AgentSearchResultVO::getScore).max(Float::compare).orElse(0.0f);
            
            // 处理边界情况
            if (Math.abs(maxScore - minScore) < 0.001f) {
                // 所有分数相同，设置为中等分数
                sourceResults.forEach(result -> result.setScore(5.0f));
                log.debug("分数范围过小，统一设置为5.0 - 来源: {}", source);
            } else {
                // Min-Max标准化到0-10范围
                for (AgentSearchResultVO result : sourceResults) {
                    float normalizedScore = (result.getScore() - minScore) / (maxScore - minScore) * 10.0f;
                    result.setScore(normalizedScore);
                }
                log.debug("Min-Max标准化完成 - 来源: {}, 原始范围: [{}, {}], 标准化范围: [0, 10]", 
                        source, minScore, maxScore);
            }
            
            normalizedResults.addAll(sourceResults);
        }
        
        return normalizedResults;
    }
    
    /**
     * 基础去重处理
     * 基于文档ID进行去重，保留分数更高的结果
     */
    private List<AgentSearchResultVO> removeBasicDuplicates(List<AgentSearchResultVO> results, DualRouteConfig config) {
        
        Map<Long, AgentSearchResultVO> uniqueResults = new HashMap<>();
        int duplicateCount = 0;
        
        for (AgentSearchResultVO result : results) {
            Long docId = result.getDocumentId();
            if (docId == null) {
                continue;
            }
            
            if (uniqueResults.containsKey(docId)) {
                // 保留分数更高的结果
                AgentSearchResultVO existing = uniqueResults.get(docId);
                if (result.getScore() > existing.getScore()) {
                    uniqueResults.put(docId, result);
                }
                duplicateCount++;
            } else {
                uniqueResults.put(docId, result);
            }
        }
        
        log.debug("基础去重完成 - 原始: {}个, 去重: {}个, 重复: {}个", 
                results.size(), uniqueResults.size(), duplicateCount);
        
        return new ArrayList<>(uniqueResults.values());
    }
    
    /**
     * 简单加权融合
     * 根据结果来源应用不同的权重
     */
    private List<AgentSearchResultVO> applySimpleWeights(List<AgentSearchResultVO> results, DualRouteConfig config,
                                                        Map<String, String> resultSources) {
        
        for (AgentSearchResultVO result : results) {
            String source = resultSources.getOrDefault(generateResultKey(result), "未知");
            float originalScore = result.getScore();
            float weightedScore;
            
            if ("精确匹配".equals(source)) {
                weightedScore = originalScore * config.getExactMatchWeight().floatValue();
            } else if ("混合检索".equals(source)) {
                weightedScore = originalScore * config.getHybridSearchWeight().floatValue();
            } else {
                weightedScore = originalScore * 0.5f; // 未知来源使用默认权重
            }
            
            result.setScore(weightedScore);
        }
        
        // 按分数降序排序
        results.sort((a, b) -> Float.compare(b.getScore(), a.getScore()));
        
        log.debug("加权融合完成 - 精确匹配权重: {}, 混合检索权重: {}", 
                config.getExactMatchWeight(), config.getHybridSearchWeight());
        
        return results;
    }
    
    /**
     * 调用Rerank服务进行最终重排序
     */
    private List<AgentSearchResultVO> callRerankService(String queryText, List<AgentSearchResultVO> results,
                                                       Long knowledgeBaseId, String knowledgeBaseName) {
        
        if (CollectionUtils.isEmpty(results) || results.size() <= 1) {
            log.debug("结果数量不足，跳过Rerank重排序");
            return results;
        }
        
        try {
            log.info("🔄 调用Rerank服务进行最终重排序 - 结果数: {}", results.size());
            
            // 转换为RecallDocSegment进行rerank
            List<RecallDocSegment> segments = results.stream()
                    .map(result -> {
                        RecallDocSegment segment = new RecallDocSegment();
                        segment.setDocId(result.getDocumentId());
                        segment.setDocTitle(result.getDocumentName());
                        segment.setContent(result.getFragmentContent());
                        segment.setScore(result.getScore());
                        return segment;
                    })
                    .collect(Collectors.toList());
            
            // 调用rerank服务
            List<RecallDocSegment> rerankedSegments = rerankService.rerankDocuments(queryText, segments, null);
            
            // 转换回AgentSearchResultVO
            List<AgentSearchResultVO> rerankedResults = rerankedSegments.stream()
                    .map(segment -> {
                        AgentSearchResultVO result = new AgentSearchResultVO();
                        result.setKnowledgeBaseId(knowledgeBaseId);
                        result.setKnowledgeBaseName(knowledgeBaseName);
                        result.setDocumentId(segment.getDocId());
                        result.setDocumentName(segment.getDocTitle());
                        result.setFragmentContent(segment.getContent());
                        result.setScore(segment.getScore() != null ? segment.getScore() : 0.0f);
                        return result;
                    })
                    .collect(Collectors.toList());
            
            log.info("✅ Rerank重排序完成 - 最终结果数: {}", rerankedResults.size());
            return rerankedResults;
            
        } catch (Exception e) {
            log.warn("❌ Rerank重排序失败，返回融合结果 - Error: {}", e.getMessage());
            return results;
        }
    }
    
    /**
     * 生成结果的唯一键
     */
    private String generateResultKey(AgentSearchResultVO result) {
        return String.format("%d_%s", 
                result.getDocumentId() != null ? result.getDocumentId() : 0L,
                result.getFragmentContent() != null ? result.getFragmentContent().hashCode() : 0);
    }
}
