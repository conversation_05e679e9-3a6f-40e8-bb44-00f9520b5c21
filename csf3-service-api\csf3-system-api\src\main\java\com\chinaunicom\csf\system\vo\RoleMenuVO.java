
package com.chinaunicom.csf.system.vo;

import com.chinaunicom.csf.system.entity.RoleMenu;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "RoleMenuVO对象", description = "RoleMenuVO对象")
public class RoleMenuVO extends RoleMenu {
	private static final long serialVersionUID = 1L;

}
