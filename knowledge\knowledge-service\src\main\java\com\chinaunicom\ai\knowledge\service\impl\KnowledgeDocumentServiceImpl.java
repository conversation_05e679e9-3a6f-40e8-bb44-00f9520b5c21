package com.chinaunicom.ai.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.ai.knowledge.dto.DocumentDeleteRequest;
import com.chinaunicom.ai.knowledge.dto.DocumentRenameDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeDocumentQueryDTO;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.KnowledgeDocumentService;
import com.chinaunicom.ai.knowledge.vo.KnowledgeDocumentVO;
import com.chinaunicom.csf.core.log.exception.ServiceException;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识文档 服务实现类
 * @date 2025/6/3 17:14
 */
@Slf4j
@Service
public class KnowledgeDocumentServiceImpl extends ServiceImpl<KnowledgeDocumentMapper, KnowledgeDocument> implements KnowledgeDocumentService {
    // 如果需要验证知识库是否存在，可以注入KnowledgeBaseMapper或Service
    // @Autowired
    // private KnowledgeBaseMapper knowledgeBaseMapper;

    @Autowired
    private FileUploadService fileUploadService; // 用于删除MinIO文件

    @Autowired
    private IElasticSearchService elasticSearchService; // 注入接口

    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;

    /**
     * 根据知识库ID查询知识库下的文档列表，支持文件名模糊查询和分页
     * 文档列表需要展示文档名称、文档大小（单位为字节）、上传时间、状态、状态的中文名。
     * @param queryDTO 包含知识库ID、文件名关键词、分页信息的查询DTO
     * @return 文档列表分页数据VO
     */
    @Override
    public IPage<KnowledgeDocumentVO> getKnowledgeDocumentsByBaseId(KnowledgeDocumentQueryDTO queryDTO) {
        String tenantId = SecureUtil.getTenantId();

        // 1. 构建分页对象
        Page<KnowledgeDocument> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 2. 构建查询条件
        LambdaQueryWrapper<KnowledgeDocument> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeDocument::getBaseId, queryDTO.getBaseId())
                .eq(KnowledgeDocument::getTenantId, tenantId)
                .eq(KnowledgeDocument::getIsDeleted, 0); // 只查询未逻辑删除的文档

        // 添加文件名模糊查询条件
        if (StringUtils.hasText(queryDTO.getFileName())) {
            queryWrapper.like(KnowledgeDocument::getFileName, queryDTO.getFileName());
        }

        queryWrapper.orderByDesc(KnowledgeDocument::getCreateTime); // 按上传时间倒序

        // 3. 执行分页查询
        IPage<KnowledgeDocument> documentPage = baseMapper.selectPage(page, queryWrapper);

        // 4. 将查询结果转换为VO并封装到分页对象中
        List<KnowledgeDocumentVO> vos = documentPage.getRecords().stream()
                .map(doc -> {
                    KnowledgeDocumentVO vo = new KnowledgeDocumentVO();
                    BeanUtils.copyProperties(doc, vo);
                    vo.setStatusName(DocumentStatusEnum.getNameByCode(doc.getStatus())); // 获取状态中文名
                    return vo;
                })
                .collect(Collectors.toList());

        // 创建新的分页对象，包含VO列表
        IPage<KnowledgeDocumentVO> resultPage = new Page<>(documentPage.getCurrent(), documentPage.getSize(), documentPage.getTotal());
        resultPage.setRecords(vos);

        return resultPage;
    }

    /**
     * 根据文档ID重命名文档，保留原文档后缀名
     * @param renameDTO 包含文档ID和新的文件名前缀的DTO
     * @return 是否重命名成功
     */
    @Override
    public boolean renameDocument(DocumentRenameDTO renameDTO) {
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();

        // 1. 查询文档是否存在且属于当前租户，并且未被逻辑删除
        KnowledgeDocument existingDocument = baseMapper.selectById(renameDTO.getDocumentId());
        if (existingDocument == null || existingDocument.getIsDeleted() == 1 || !tenantId.equals(existingDocument.getTenantId())) {
            throw new ServiceException("文档不存在或无权操作");
        }

        // 2. 提取旧文档的后缀名
        String oldFileName = existingDocument.getFileName();
        String fileExtension = "";
        int lastDotIndex = oldFileName.lastIndexOf('.');
        if (lastDotIndex != -1 && lastDotIndex < oldFileName.length() - 1) { // 确保有点且不是最后一个字符
            fileExtension = oldFileName.substring(lastDotIndex); // 包含点，例如 ".pdf"
        }

        // 3. 构造新的完整文件名：新的前缀 + 旧的后缀名
        String newFullFileName = renameDTO.getNewFileNamePrefix() + fileExtension;

        // 4. 更新文档名称和更新时间、更新用户
        existingDocument.setFileName(newFullFileName);
        existingDocument.setUpdateTime(LocalDateTime.now());
        existingDocument.setUpdateUser(userId);

        // 5. 执行数据库更新
        int updateCount = baseMapper.updateById(existingDocument);
        return updateCount > 0;
    }

    /**
     * 根据文档ID列表删除知识库文档，支持单个或批量删除。
     * 执行逻辑删除数据库记录，并物理删除MinIO文件。
     * @param deleteRequest 包含要删除的文档ID列表的请求DTO
     * @return 成功删除的数量
     */
    @Override
    @Transactional // 开启事务，确保批量操作的原子性
    public int deleteDocuments(DocumentDeleteRequest deleteRequest) {
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();
        int successCount = 0;

        if (deleteRequest.getDocumentIds() == null || deleteRequest.getDocumentIds().isEmpty()) {
            log.warn("文档ID列表为空，无需执行删除操作。");
            return 0;
        }

        String indexName = null;
        for (Long documentId : deleteRequest.getDocumentIds()) {
            KnowledgeDocument document = baseMapper.selectById(documentId);
            Long kbId = document.getBaseId(); // 获取知识库ID
            if (org.apache.commons.lang3.StringUtils.isBlank(indexName)) {
                KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(kbId);// 获取知识库索引名称
                indexName = knowledgeBase.getIndexName();
            }

            // 1. 检查文档是否存在、是否已逻辑删除以及是否属于当前租户
            if (document == null || document.getIsDeleted() == 1 || !tenantId.equals(document.getTenantId())) {
                log.warn("跳过无效或无权删除的文档ID: {} (文档不存在、已删除或租户不匹配)", documentId);
                continue; // 跳过当前文档，继续处理下一个
            }

            try {
                // 2. 逻辑删除MySQL中的文档记录
                document.setIsDeleted(1); // 设置为已删除状态
                document.setStatus(DocumentStatusEnum.DELETED.getCode()); // 设置文档状态为已删除
                document.setUpdateTime(LocalDateTime.now());
                document.setUpdateUser(userId);
                int updateCount = baseMapper.updateById(document);

                if (updateCount > 0) {
                    log.info("成功逻辑删除MySQL文档记录，ID: {}", documentId);
                    successCount++;

                    // 3. 物理删除MinIO上的文件
                    // 只有当存在 fileObjectId 时才尝试删除 MinIO 文件
                    if (StringUtils.hasText(document.getFileObjectId())) {
                        fileUploadService.deleteMinioFile(document.getFileObjectId());
                        log.info("成功从MinIO删除文件，FileObjectId: {}", document.getFileObjectId());
                    } else {
                        log.warn("文档ID: {} 没有关联的MinIO文件对象ID，跳过MinIO文件删除。", documentId);
                    }

                    // 4. 删除Elasticsearch中的对应index下metadata的mysqlDocId是document.getId()的数据
                    elasticSearchService.deleteDocumentByMysqlDocId(indexName, document.getId());

                } else {
                    log.warn("未能逻辑删除MySQL文档记录，ID: {} (可能已被其他操作修改或删除)。", documentId);
                }
            } catch (ServiceException e) {
                log.error("删除文档失败，文档ID: {}，业务异常: {}", documentId, e.getMessage());
                // 不重新抛出，允许批处理继续，但记录错误
            } catch (Exception e) {
                log.error("删除文档时发生意外错误，文档ID: {}", documentId, e);
                // 不重新抛出，允许批处理继续
            }
        }
        return successCount;
    }
}
