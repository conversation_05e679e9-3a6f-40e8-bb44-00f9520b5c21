package com.example.demo.controller;

import com.chinaunicom.csf.plugins.sms.exception.SmsException;
import com.chinaunicom.csf.plugins.sms.template.SmsTemplate;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @date 2023/6/15 9:52
 */
@RestController
@Tag(description = "短信发送", name = "短信发送")
@RequestMapping("/sms")
public class SmsController {

	@Resource
	@Lazy
	private SmsTemplate smsTemplate;

	@GetMapping("/send")
	public void send(String phone, String code) throws SmsException {
		smsTemplate.send(phone, "1949239", null, code);
	}

}
