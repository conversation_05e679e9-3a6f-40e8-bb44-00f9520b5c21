package com.chinaunicom.csf.service;

import com.chinaunicom.csf.dto.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件上传服务接口
 */
public interface IFileUploadService {

    /**
     * 上传文件
     *
     * @param request 文件上传请求
     * @return 文件元数据DTO
     */
    FileMetadataDTO uploadFile(FileUploadRequest request);

    /**
     * 初始化分片上传
     *
     * @param request 初始化分片上传请求
     * @return 初始化分片上传响应
     */
    InitMultipartUploadResponse initMultipartUpload(InitMultipartUploadRequest request);

    /**
     * 上传文件分片
     *
     * @param request 上传分片请求
     * @return etag
     */
    String uploadPart(UploadPartRequest request);

    /**
     * 完成分片上传
     *
     * @param request 完成分片上传请求
     * @return 文件元数据DTO
     */
    FileMetadataDTO completeMultipartUpload(com.chinaunicom.csf.dto.CompleteMultipartUploadRequest request);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @param response HTTP响应
     */
    void downloadFile(String fileId, HttpServletResponse response);

    /**
     * 根据文件ID获取文件元数据
     *
     * @param fileId 文件ID
     * @return 文件元数据实体
     */
    FileMetadataDTO getFileMetadata(String fileId);

    /**
     * 获取文件直接访问URL
     *
     * @param fileId 文件ID
     * @return 文件URL
     */
    String getFileAccessUrl(String fileId);

    /**
     * 删除文件
     *
     * @param fileIds 文件ID列表
     */
    void deleteFiles(List<String> fileIds);
} 