# 知识库系统数据库设计文档

## 📋 **文档信息**

| 项目名称 | 知识库管理系统 |
|----------|----------------|
| 文档版本 | v1.0.0 |
| 创建日期 | 2025-06-12 |
| 数据库类型 | MySQL 8.0+ |
| 字符集 | utf8mb4_unicode_ci |

## 🎯 **系统概述**

知识库系统是一个基于向量检索的智能知识管理平台，支持文档上传、向量化存储、语义检索和测试功能。系统采用MySQL存储元数据，ElasticSearch存储向量数据的混合架构。

## 📊 **数据库表结构**

### 1. **ai_model** - AI模型表

存储系统中可用的AI模型信息，包括大语言模型和向量嵌入模型。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，模型ID |
| create_user | BIGINT | - | NULL | - | 创建用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_user | BIGINT | - | NULL | - | 更新用户ID |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| status | INT | - | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| is_deleted | INT | - | NOT NULL | 0 | 是否已删除：0-未删除，1-已删除 |
| display_name | VARCHAR | 255 | NOT NULL | - | 模型显示名称 |
| model_type | BIGINT | - | NOT NULL | - | 模型类型：1-大语言模型，2-向量嵌入模型 |
| model_api_cnf | TEXT | - | NULL | - | 模型API配置信息（JSON格式） |
| dims | INT | - | NULL | - | 向量维度（仅向量模型有效） |
| tenant_id | VARCHAR | 12 | NOT NULL | '000000' | 租户ID |

**索引设计：**
- PRIMARY KEY: `id`
- INDEX: `idx_model_type` (model_type)
- INDEX: `idx_tenant_id` (tenant_id)
- INDEX: `idx_status_deleted` (status, is_deleted)

### 2. **knowledge_base** - 知识库表

存储知识库的基本信息和配置。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，知识库ID |
| create_user | BIGINT | - | NULL | - | 创建用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_user | BIGINT | - | NULL | - | 更新用户ID |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| status | INT | - | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| is_deleted | INT | - | NOT NULL | 0 | 是否已删除：0-未删除，1-已删除 |
| name | VARCHAR | 255 | NOT NULL | - | 知识库名称 |
| descrip | TEXT | - | NULL | - | 知识库描述 |
| vec_model | BIGINT | - | NOT NULL | - | 关联的向量模型ID |
| index_name | VARCHAR | 255 | NULL | - | ElasticSearch索引名称 |
| tenant_id | VARCHAR | 12 | NOT NULL | '000000' | 租户ID |

**索引设计：**
- PRIMARY KEY: `id`
- INDEX: `idx_vec_model` (vec_model)
- INDEX: `idx_tenant_id` (tenant_id)
- INDEX: `idx_create_user` (create_user)
- INDEX: `idx_status_deleted` (status, is_deleted)

**外键约束：**
- FOREIGN KEY: `vec_model` REFERENCES `ai_model(id)`

### 3. **knowledge_document** - 知识文档表

存储上传到知识库中的文档信息。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键，文档ID |
| create_user | BIGINT | - | NULL | - | 创建用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_user | BIGINT | - | NULL | - | 更新用户ID |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| status | INT | - | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| is_deleted | INT | - | NOT NULL | 0 | 是否已删除：0-未删除，1-已删除 |
| base_id | BIGINT | - | NOT NULL | - | 所属知识库ID |
| file_name | VARCHAR | 500 | NOT NULL | - | 文件名称 |
| file_size | BIGINT | - | NULL | - | 文件大小（字节） |
| slice_total | BIGINT | - | NULL | - | 文档切片总数 |
| char_total | BIGINT | - | NULL | - | 文档字符总数 |
| tenant_id | VARCHAR | 12 | NOT NULL | '000000' | 租户ID |

**索引设计：**
- PRIMARY KEY: `id`
- INDEX: `idx_base_id` (base_id)
- INDEX: `idx_tenant_id` (tenant_id)
- INDEX: `idx_create_user` (create_user)
- INDEX: `idx_status_deleted` (status, is_deleted)

**外键约束：**
- FOREIGN KEY: `base_id` REFERENCES `knowledge_base(id)`

### 4. **knowledge_test_history** - 知识库检索测试历史表

存储知识库检索测试的历史记录。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | - | 主键，测试记录ID（32位不含横线的UUID） |
| question | TEXT | - | NOT NULL | - | 检索测试的问题文本 |
| knowledge_base_id | BIGINT | - | NOT NULL | - | 知识库ID |
| execution_time_ms | BIGINT | - | NOT NULL | - | 查询执行时间（毫秒） |
| result_count | INT | - | NOT NULL | 0 | 返回结果数量 |
| create_user | BIGINT | - | NULL | - | 创建用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_user | BIGINT | - | NULL | - | 更新用户ID |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| status | INT | - | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| is_deleted | INT | - | NOT NULL | 0 | 是否已删除：0-未删除，1-已删除 |
| tenant_id | VARCHAR | 12 | NOT NULL | '000000' | 租户ID |

**索引设计：**
- PRIMARY KEY: `id`
- INDEX: `idx_knowledge_base_id` (knowledge_base_id)
- INDEX: `idx_create_time` (create_time)
- INDEX: `idx_tenant_id` (tenant_id)
- INDEX: `idx_create_user` (create_user)

### 5. **knowledge_test_result** - 知识库检索测试结果表

存储每次检索测试的具体结果详情。

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | - | 主键，测试结果ID（32位不含横线的UUID） |
| test_history_id | VARCHAR | 32 | NOT NULL | - | 测试历史记录ID |
| content | TEXT | - | NOT NULL | - | 匹配到的文档分片内容 |
| score | DECIMAL | 10,6 | NOT NULL | - | 匹配分数（0-1之间） |
| document_id | BIGINT | - | NOT NULL | - | 文档ID |
| document_name | VARCHAR | 500 | NOT NULL | - | 文档名称 |
| knowledge_base_id | BIGINT | - | NOT NULL | - | 知识库ID |
| create_user | BIGINT | - | NULL | - | 创建用户ID |
| create_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_user | BIGINT | - | NULL | - | 更新用户ID |
| update_time | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 |
| status | INT | - | NOT NULL | 1 | 状态：1-正常，0-禁用 |
| is_deleted | INT | - | NOT NULL | 0 | 是否已删除：0-未删除，1-已删除 |
| tenant_id | VARCHAR | 12 | NOT NULL | '000000' | 租户ID |

**索引设计：**
- PRIMARY KEY: `id`
- INDEX: `idx_test_history_id` (test_history_id)
- INDEX: `idx_document_id` (document_id)
- INDEX: `idx_knowledge_base_id` (knowledge_base_id)
- INDEX: `idx_tenant_id` (tenant_id)

**外键约束：**
- FOREIGN KEY: `test_history_id` REFERENCES `knowledge_test_history(id)` ON DELETE CASCADE

## 🔗 **表关系图**

```mermaid
erDiagram
    ai_model ||--o{ knowledge_base : "vec_model"
    knowledge_base ||--o{ knowledge_document : "base_id"
    knowledge_base ||--o{ knowledge_test_history : "knowledge_base_id"
    knowledge_test_history ||--o{ knowledge_test_result : "test_history_id"
    knowledge_document ||--o{ knowledge_test_result : "document_id"

    ai_model {
        BIGINT id PK
        VARCHAR display_name
        BIGINT model_type
        TEXT model_api_cnf
        INT dims
        VARCHAR tenant_id
    }

    knowledge_base {
        BIGINT id PK
        VARCHAR name
        TEXT descrip
        BIGINT vec_model FK
        VARCHAR index_name
        VARCHAR tenant_id
    }

    knowledge_document {
        BIGINT id PK
        BIGINT base_id FK
        VARCHAR file_name
        BIGINT file_size
        BIGINT slice_total
        VARCHAR tenant_id
    }

    knowledge_test_history {
        VARCHAR id PK
        TEXT question
        BIGINT knowledge_base_id FK
        BIGINT execution_time_ms
        INT result_count
        VARCHAR tenant_id
    }

    knowledge_test_result {
        VARCHAR id PK
        VARCHAR test_history_id FK
        TEXT content
        DECIMAL score
        BIGINT document_id FK
        VARCHAR document_name
        VARCHAR tenant_id
    }
```

## 📋 **业务规则**

### 1. **数据完整性规则**

#### 1.1 **主键规则**
- 所有表必须有主键
- `ai_model`、`knowledge_base`、`knowledge_document` 使用自增BIGINT主键
- `knowledge_test_history`、`knowledge_test_result` 使用32位UUID字符串主键

#### 1.2 **外键规则**
- `knowledge_base.vec_model` 必须引用有效的向量嵌入模型（`ai_model.model_type = 2`）
- `knowledge_document.base_id` 必须引用有效的知识库
- `knowledge_test_history.knowledge_base_id` 必须引用有效的知识库
- `knowledge_test_result.test_history_id` 必须引用有效的测试历史记录

#### 1.3 **租户隔离规则**
- 所有表都包含 `tenant_id` 字段，确保多租户数据隔离
- 默认租户ID为 '000000'
- 跨表查询时必须验证租户ID一致性

### 2. **数据约束规则**

#### 2.1 **状态字段约束**
- `status`: 1-正常，0-禁用
- `is_deleted`: 0-未删除，1-已删除
- 查询时默认过滤已删除数据 (`is_deleted = 0`)

#### 2.2 **模型类型约束**
- `ai_model.model_type`: 1-大语言模型，2-向量嵌入模型
- 知识库只能关联向量嵌入模型 (`model_type = 2`)

#### 2.3 **分数约束**
- `knowledge_test_result.score`: 范围 0.000000 - 1.000000
- 精度为6位小数

### 3. **业务逻辑规则**

#### 3.1 **知识库创建规则**
1. 验证关联的向量模型存在且为嵌入模型
2. 验证模型属于当前租户
3. 在MySQL创建记录的同时在ElasticSearch创建索引
4. 索引命名规则：`kb-{tenant_id}-{knowledge_base_id}`

#### 3.2 **检索测试规则**
1. 一次测试对应一个知识库
2. 测试历史和测试结果采用级联删除
3. 执行时间以毫秒为单位存储
4. 测试结果按分数降序排列

#### 3.3 **软删除规则**
1. 所有删除操作都是逻辑删除（`is_deleted = 1`）
2. 删除知识库时同时删除相关文档和测试数据
3. 删除测试历史时级联删除测试结果

## ⚡ **性能优化策略**

### 1. **索引优化**

#### 1.1 **查询频率分析**
- **高频查询**：按租户ID查询、按知识库ID查询、按状态查询
- **中频查询**：按创建时间排序、按模型类型查询
- **低频查询**：按用户ID查询、全文搜索

#### 1.2 **复合索引设计**
```sql
-- 知识库列表查询优化
CREATE INDEX idx_kb_tenant_status_deleted ON knowledge_base(tenant_id, status, is_deleted);

-- 文档列表查询优化
CREATE INDEX idx_doc_base_tenant_deleted ON knowledge_document(base_id, tenant_id, is_deleted);

-- 测试历史查询优化
CREATE INDEX idx_test_kb_tenant_time ON knowledge_test_history(knowledge_base_id, tenant_id, create_time DESC);

-- 测试结果查询优化
CREATE INDEX idx_result_history_score ON knowledge_test_result(test_history_id, score DESC);
```

### 2. **分区策略**

#### 2.1 **时间分区**
```sql
-- 测试历史表按月分区（建议）
ALTER TABLE knowledge_test_history
PARTITION BY RANGE (YEAR(create_time) * 100 + MONTH(create_time)) (
    PARTITION p202501 VALUES LESS THAN (202502),
    PARTITION p202502 VALUES LESS THAN (202503),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 2.2 **租户分区**
```sql
-- 大型部署可考虑按租户分区
ALTER TABLE knowledge_base
PARTITION BY HASH(CRC32(tenant_id)) PARTITIONS 16;
```

### 3. **查询优化**

#### 3.1 **常用查询模式**
```sql
-- 1. 知识库列表查询（分页）
SELECT kb.*, COUNT(kd.id) as doc_count
FROM knowledge_base kb
LEFT JOIN knowledge_document kd ON kb.id = kd.base_id AND kd.is_deleted = 0
WHERE kb.tenant_id = ? AND kb.is_deleted = 0
GROUP BY kb.id
ORDER BY kb.update_time DESC
LIMIT ?, ?;

-- 2. 测试历史查询（按知识库）
SELECT id, question, ROUND(execution_time_ms/1000.0, 3) as second, create_time
FROM knowledge_test_history
WHERE knowledge_base_id = ? AND tenant_id = ? AND is_deleted = 0
ORDER BY create_time DESC
LIMIT ?, ?;

-- 3. 测试结果查询（按历史ID）
SELECT content, score, document_name
FROM knowledge_test_result
WHERE test_history_id = ? AND is_deleted = 0
ORDER BY score DESC;
```

## 🛠️ **数据库维护**

### 1. **定期维护任务**

#### 1.1 **数据清理**
```sql
-- 清理6个月前的测试数据（可根据业务需求调整）
DELETE FROM knowledge_test_result
WHERE test_history_id IN (
    SELECT id FROM knowledge_test_history
    WHERE create_time < DATE_SUB(NOW(), INTERVAL 6 MONTH)
);

DELETE FROM knowledge_test_history
WHERE create_time < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

#### 1.2 **统计信息更新**
```sql
-- 定期更新表统计信息
ANALYZE TABLE ai_model, knowledge_base, knowledge_document,
             knowledge_test_history, knowledge_test_result;
```

### 2. **监控指标**

#### 2.1 **性能监控**
- 查询响应时间（目标：< 100ms）
- 索引使用率（目标：> 95%）
- 表扫描比例（目标：< 5%）

#### 2.2 **容量监控**
- 表大小增长趋势
- 索引大小占比
- 分区数据分布

### 3. **备份策略**

#### 3.1 **备份频率**
- **全量备份**：每日凌晨执行
- **增量备份**：每4小时执行
- **日志备份**：实时备份

#### 3.2 **备份验证**
- 定期恢复测试
- 数据一致性检查
- 备份文件完整性验证

## 📝 **SQL脚本**

### 1. **建表脚本**

详细的建表脚本请参考：
- `doc/sql/knowledge_test_history.sql` - 测试相关表
- 其他表的建表脚本可通过MyBatis-Plus代码生成器生成

### 2. **初始化数据**

```sql
-- 插入默认向量模型（示例）
INSERT INTO ai_model (display_name, model_type, dims, tenant_id, status, is_deleted)
VALUES ('text-embedding-ada-002', 2, 1536, '000000', 1, 0);

-- 插入默认大语言模型（示例）
INSERT INTO ai_model (display_name, model_type, tenant_id, status, is_deleted)
VALUES ('gpt-3.5-turbo', 1, '000000', 1, 0);
```

## 🔄 **版本历史**

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0.0 | 2025-06-12 | 初始版本，包含5个核心表设计 | AI Assistant |

## 📞 **联系信息**

如有疑问或建议，请联系开发团队。

---

**注意**：本文档应随着系统演进持续更新，确保与实际数据库结构保持一致。
