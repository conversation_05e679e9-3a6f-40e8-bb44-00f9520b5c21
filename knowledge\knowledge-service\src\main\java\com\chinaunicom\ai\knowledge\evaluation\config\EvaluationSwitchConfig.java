package com.chinaunicom.ai.knowledge.evaluation.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


import jakarta.annotation.PostConstruct;

/**
 * 评测功能开关配置类
 * 
 * 支持通过Nacos动态配置评测功能的开启/关闭状态
 * 当功能关闭时，所有评测接口将返回统一的错误响应
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Slf4j
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "evaluation.controller")
public class EvaluationSwitchConfig {

    /**
     * 评测功能开关状态
     * 默认值：true（开启）
     * 
     * 配置项：evaluation.controller.enabled
     * 取值：
     * - true：评测功能正常可用
     * - false：评测功能关闭，所有接口拒绝访问
     */
    private boolean enabled = true;

    /**
     * 功能关闭时的HTTP状态码
     * 默认值：503（服务不可用）
     * 
     * 配置项：evaluation.controller.disabled-http-status
     * 建议值：
     * - 503：服务不可用（推荐）
     * - 423：资源被锁定
     * - 503：服务暂时不可用
     */
    private int disabledHttpStatus = 503;

    /**
     * 功能关闭时的错误码
     * 默认值：EVALUATION_DISABLED
     * 
     * 配置项：evaluation.controller.disabled-error-code
     */
    private String disabledErrorCode = "EVALUATION_DISABLED";

    /**
     * 功能关闭时的错误信息
     * 默认值：评测功能暂时关闭，请稍后再试
     * 
     * 配置项：evaluation.controller.disabled-message
     */
    private String disabledMessage = "评测功能暂时关闭，请稍后再试";

    /**
     * 是否记录功能关闭时的访问日志
     * 默认值：true
     * 
     * 配置项：evaluation.controller.log-disabled-access
     */
    private boolean logDisabledAccess = true;

    /**
     * 功能关闭时的详细说明信息
     * 默认值：系统维护中，评测相关功能暂时不可用
     * 
     * 配置项：evaluation.controller.disabled-detail
     */
    private String disabledDetail = "系统维护中，评测相关功能暂时不可用";

    /**
     * 配置初始化后的回调
     * 验证配置参数并记录配置信息
     */
    @PostConstruct
    public void init() {
        validate();
        logConfigInfo("配置初始化");
    }

    /**
     * 验证配置参数的有效性
     */
    private void validate() {
        if (disabledHttpStatus < 100 || disabledHttpStatus > 599) {
            log.warn("无效的HTTP状态码: {}, 使用默认值503", disabledHttpStatus);
            disabledHttpStatus = 503;
        }

        if (disabledErrorCode == null || disabledErrorCode.trim().isEmpty()) {
            log.warn("错误码不能为空，使用默认值");
            disabledErrorCode = "EVALUATION_DISABLED";
        }

        if (disabledMessage == null || disabledMessage.trim().isEmpty()) {
            log.warn("错误信息不能为空，使用默认值");
            disabledMessage = "评测功能暂时关闭，请稍后再试";
        }
    }

    /**
     * 记录配置信息
     * 
     * @param action 触发记录的动作
     */
    private void logConfigInfo(String action) {
        log.info("评测功能开关配置 - {}: enabled={}, httpStatus={}, errorCode={}, message={}",
                action, enabled, disabledHttpStatus, disabledErrorCode, disabledMessage);
    }

    /**
     * 检查评测功能是否可用
     * 
     * @return true-功能可用，false-功能关闭
     */
    public boolean isEvaluationEnabled() {
        return enabled;
    }

    /**
     * 获取功能关闭时的完整错误信息
     * 
     * @return 包含错误码和详细信息的完整错误描述
     */
    public String getFullDisabledMessage() {
        if (disabledDetail != null && !disabledDetail.trim().isEmpty()) {
            return disabledMessage + "。" + disabledDetail;
        }
        return disabledMessage;
    }

    /**
     * 当配置发生变化时的回调
     * 由于使用了@RefreshScope，配置更新时会自动调用
     */
    public void onConfigRefresh() {
        validate();
        logConfigInfo("配置刷新");

        if (!enabled) {
            log.warn("⚠️ 评测功能已被关闭！所有评测接口将拒绝访问");
        } else {
            log.info("✅ 评测功能已开启，所有接口正常可用");
        }
    }

    /**
     * 获取当前配置的摘要信息
     * 用于监控和调试
     *
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        return String.format("EvaluationSwitch[enabled=%s, httpStatus=%d, errorCode=%s]",
                enabled, disabledHttpStatus, disabledErrorCode);
    }
}
