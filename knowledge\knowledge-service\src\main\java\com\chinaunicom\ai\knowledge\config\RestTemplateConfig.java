package com.chinaunicom.ai.knowledge.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 用于HTTP客户端调用外部服务
 */
@Configuration
public class RestTemplateConfig {

    @Value("${rerank.api.timeout.connect:10000}")
    private int connectTimeout;

    @Value("${rerank.api.timeout.read:30000}")
    private int readTimeout;

    /**
     * 创建RestTemplate Bean
     * 配置连接超时和读取超时
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        // 设置连接超时时间（毫秒）
        factory.setConnectTimeout(connectTimeout);
        // 设置读取超时时间（毫秒）
        factory.setReadTimeout(readTimeout);

        return new RestTemplate(factory);
    }
}
