<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.mapper.AiModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.entity.AiModel">
        <id column="id" property="id" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="display_name" property="displayName" />
        <result column="model_type" property="modelType" />
        <result column="model_api_cnf" property="modelApiCnf" />
        <result column="tenant_id" property="tenantId" />
        <result column="dims" property="dims" />
    </resultMap>

</mapper>
