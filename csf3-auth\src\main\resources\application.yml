#第三方登陆配置（可放到nacos配置中心）
social:
  enabled: true
  # 第三方认证成功后回调本方的域名
  domain: http://127.0.0.1:1888
  jap:
    CHGITLAB:
      just-auth-config:
        client-id: xxxxxxxxxxxxxxxxxxxx
        client-secret: xxxxxxxxxxxxxxxxxxxx
        # 第三方认证成功后回调本方的地址
        redirect-uri: ${social.domain}/gitlab-login
        # 第三方认证的域名
        auth-domain: https://gitlab.xxxx.cn
      # 自定义接入第三方认证的包扫描
      #scan-packages:
      #  - com.chinaunicom.csf.social
