<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>csf3</artifactId>
        <groupId>com.chinaunicom.csf</groupId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3-ops</artifactId>
    <name>${project.artifactId}</name>
    <version>1.8.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>csf3-admin</module>
        <module>csf3-develop</module>
        <module>csf3-report</module>
        <module>csf3-resource</module>
        <module>csf3-seata-order</module>
        <module>csf3-seata-storage</module>
        <module>csf3-mq-consumer</module>
        <module>csf3-mq-producer</module>
    </modules>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.4</version>
            </plugin>
        </plugins>
    </build>
</project>
