package com.chinaunicom.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.ai.knowledge.dto.KnowledgeTestHistoryQueryDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeTestRecallDTO;
import com.chinaunicom.ai.knowledge.vo.DatasetTestHistoryItemVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeTestHistoryListVO;

/**
 * 知识库检索测试服务接口
 *
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
public interface KnowledgeTestService {

    /**
     * 执行知识库检索测试
     *
     * @param testRecallDTO 检索测试请求DTO
     * @return 测试历史项VO，包含测试结果
     */
    DatasetTestHistoryItemVO executeRecallTest(KnowledgeTestRecallDTO testRecallDTO);

    /**
     * 根据测试历史ID获取测试详情
     * 包含通过关联查询获取的知识库名称等完整信息
     *
     * @param testHistoryId 测试历史记录ID
     * @return 测试历史项VO，包含完整的关联信息
     */
    DatasetTestHistoryItemVO getTestHistoryDetail(String testHistoryId);

    /**
     * 分页查询知识库检索测试历史列表
     * 支持按知识库ID和问题关键词筛选
     *
     * @param queryDTO 查询条件DTO
     * @return 分页的测试历史列表
     */
    IPage<KnowledgeTestHistoryListVO> getTestHistoryList(KnowledgeTestHistoryQueryDTO queryDTO);

    /**
     * 删除某个历史检索测试记录
     * 逻辑删除测试历史记录和相关的测试结果
     *
     * @param testHistoryId 测试历史记录ID
     * @return 是否删除成功
     */
    Boolean deleteTestHistory(String testHistoryId);
}
