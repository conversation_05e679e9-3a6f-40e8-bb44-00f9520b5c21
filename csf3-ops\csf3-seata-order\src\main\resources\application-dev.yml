#服务器端口
server:
  port: 8501

#数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${csf.datasource.dev.url}
    username: ${csf.datasource.dev.username}
    password: ${csf.datasource.dev.password}

# seata配置
seata:
  #registry:
  #  type: nacos
  #  nacos:
  #    server-addr: localhost
  #config:
  #  type: nacos
  #  nacos:
  #    server-addr: localhost
  tx-service-group: csf-seata-order-group
  service:
    grouplist:
      default: 127.0.0.1:8091
    vgroup-mapping:
      csf-seata-order-group: default
    disable-global-transaction: false
  client:
    rm:
      report-success-enable: false
