package com.chinaunicom.csf.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 知识库与智能体关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent_apps_knowledgebase_relation")
@Schema(description="知识库与智能体关联表")
public class AgentAppsKnowledgebaseRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联记录 ID，自增主键，唯一标识智能体与知识库的关联关系")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "关联的智能体 ID，外键关联 agent_apps.id，级联删除")
    @TableField("agent_id")
    private Integer agentId;

    @Schema(description = "关联的知识库 ID，外键关联 knowledge_bases.id（需提前创建该表）")
    @TableField("knowledgebase_id")
    private Integer knowledgebaseId;

    @Schema(description = "知识库优先级，数值越大优先级越高（如：1=最高优先级，5=最低）")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "关联记录创建时间，自动填充当前时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @Schema(description = "关联记录更新时间，修改时自动更新")
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
