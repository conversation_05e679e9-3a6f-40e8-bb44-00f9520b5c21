export harbor_repo=10.19.64.203:8080/csf-sb3
echo "harbor_repo=$harbor_repo"
cd ${PROJECT_DIR}
version=`awk '/<version>[^<]+<\/version>/{gsub(/<version>|<\/version>/,"",$1);print $1;exit;}' pom.xml`
echo "version=$version"
#mvn clean package
declare -a project_arr=(
#"csf-sb3-gateway"
#"csf-sb3-auth"
#"csf-sb3-ops/csf-sb3-admin"
#"csf-sb3-ops/csf-sb3-develop"
#"csf-sb3-ops/csf-sb3-mq-consumer"
#"csf-sb3-ops/csf-sb3-mq-producer"
#"csf-sb3-ops/csf-sb3-report"
#"csf-sb3-ops/csf-sb3-resource"
#"csf-sb3-ops/csf-sb3-seata-order"
#"csf-sb3-ops/csf-sb3-seata-storage"
"csf-ops/csf-swagger"
#"csf-sb3-service/csf-sb3-desk"
#"csf-sb3-service/csf-sb3-log"
#"csf-sb3-service/csf-sb3-system"
#"csf-sb3-service/csf-sb3-user"
)

for project_path in "${project_arr[@]}"
do
    echo "cd ${PROJECT_DIR}/$project_path"
    cd ${PROJECT_DIR}/$project_path

    app=${$(pwd)##*/}
    echo "app=$app"
    docker build . -t $app:$version
    echo "docker has built image $app:$version"
    docker tag $app:$version $harbor_repo/$app:$version
    docker push $harbor_repo/$app:$version &
    echo "docker has pushed $app:$version to $harbor_repo"
done
