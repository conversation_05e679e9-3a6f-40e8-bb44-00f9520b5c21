
package com.chinaunicom.csf.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

import java.util.function.Consumer;

/**
 * 消费者启动器
 *
 */
@SpringBootApplication
@Slf4j
public class ConsumerApplication {

	public static void main(String[] args) {
		SpringApplication.run(ConsumerApplication.class, args);
	}

	/**
	 * 消费者，bean名称必须与配置文件中的bindingName一致
	 */
	@Bean
	public Consumer<String> csfMsgTest() {
		return message -> log.info("消费csfMsgTest: {}", message);
	}

}
