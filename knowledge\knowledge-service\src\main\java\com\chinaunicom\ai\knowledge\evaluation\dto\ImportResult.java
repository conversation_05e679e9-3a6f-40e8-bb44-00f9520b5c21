package com.chinaunicom.ai.knowledge.evaluation.dto;

import lombok.Data;

import java.util.List;

/**
 * 数据导入结果DTO
 */
@Data
public class ImportResult {
    
    /**
     * 导入状态：SUCCESS-成功, FAILED-失败, PARTIAL-部分成功
     */
    private String status;
    
    /**
     * 总数据条数
     */
    private Integer totalCount;
    
    /**
     * 成功导入条数
     */
    private Integer successCount;
    
    /**
     * 失败条数
     */
    private Integer failedCount;
    
    /**
     * 失败的文档ID列表
     */
    private List<String> failedDocIds;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 导入耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;
    
    public static ImportResult success(Integer totalCount, Integer successCount, Long duration, Long knowledgeBaseId) {
        ImportResult result = new ImportResult();
        result.setStatus("SUCCESS");
        result.setTotalCount(totalCount);
        result.setSuccessCount(successCount);
        result.setFailedCount(0);
        result.setDuration(duration);
        result.setKnowledgeBaseId(knowledgeBaseId);
        return result;
    }
    
    public static ImportResult partial(Integer totalCount, Integer successCount, Integer failedCount, 
                                     List<String> failedDocIds, Long duration, Long knowledgeBaseId) {
        ImportResult result = new ImportResult();
        result.setStatus("PARTIAL");
        result.setTotalCount(totalCount);
        result.setSuccessCount(successCount);
        result.setFailedCount(failedCount);
        result.setFailedDocIds(failedDocIds);
        result.setDuration(duration);
        result.setKnowledgeBaseId(knowledgeBaseId);
        return result;
    }
    
    public static ImportResult failed(String errorMessage) {
        ImportResult result = new ImportResult();
        result.setStatus("FAILED");
        result.setErrorMessage(errorMessage);
        return result;
    }
}
