package com.chinaunicom.csf.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.resource.entity.Attachment;
import com.chinaunicom.csf.resource.service.IAttachmentService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 附件表 控制器
 *
 */
@RestController
@AllArgsConstructor
@RequestMapping("/attachment")
@Tag(name = "附件表", description = "附件表")
public class AttachmentController{

	private final IAttachmentService attachmentService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入attachment")
	public R<Attachment> detail(Attachment attachment) {
		Attachment detail = attachmentService.getOne(Condition.getQueryWrapper(attachment));
		return R.data(detail);
	}

	/**
	 * 分页 附件表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页",description = "传入attachment")
	public R<IPage<Attachment>> list(Attachment attachment, Query query) {
		IPage<Attachment> pages = attachmentService.page(Condition.getPage(query), Condition.getQueryWrapper(attachment));
		return R.data(pages);
	}

	/**
	 * 自定义分页 附件表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页",description = "传入attachment")
	public R<IPage<Attachment>> page(Attachment attachment, Query query) {
		IPage<Attachment> pages = attachmentService.page(Condition.getPage(query), Condition.getQueryWrapper(attachment));
		return R.data(pages);
	}

	/**
	 * 新增 附件表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增",description = "传入attachment")
	public R save(@Valid @RequestBody Attachment attachment) {
		return R.status(attachmentService.save(attachment));
	}

	/**
	 * 修改 附件表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改",description = "传入attachment")
	public R update(@Valid @RequestBody Attachment attachment) {
		return R.status(attachmentService.updateById(attachment));
	}

	/**
	 * 新增或修改 附件表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改",description = "传入attachment")
	public R submit(@Valid @RequestBody Attachment attachment) {
		return R.status(attachmentService.saveOrUpdate(attachment));
	}


	/**
	 * 删除 附件表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除",description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(attachmentService.deleteLogic(Func.toLongList(ids)));
	}


}
