package com.chinaunicom.ai.knowledge.evaluation.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 评测文档状态同步定时任务
 * 
 * 定期检查并修复 evaluation_document 和 knowledge_document 表之间的状态不一致问题
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationDocumentStatusSyncScheduler {

    private final EvaluationDocumentStatusSyncService statusSyncService;

    /**
     * 定时同步状态任务
     * 每30分钟执行一次，检查并修复状态不一致的数据
     */
    @Scheduled(fixedRate = 1800000) // 30分钟 = 1,800,000毫秒
    public void syncDocumentStatuses() {
        try {
            log.info("开始执行评测文档状态同步任务");
            
            // 先报告当前不一致情况
            statusSyncService.reportInconsistentStatuses();
            
            // 执行批量修复
            int fixedCount = statusSyncService.fixInconsistentStatuses();
            
            if (fixedCount > 0) {
                log.info("✅ 评测文档状态同步任务完成，修复了 {} 个不一致记录", fixedCount);
            } else {
                log.debug("✅ 评测文档状态同步任务完成，所有状态一致");
            }
            
        } catch (Exception e) {
            log.error("❌ 评测文档状态同步任务执行失败", e);
        }
    }

    /**
     * 手动触发状态同步
     * 可以通过管理接口调用
     */
    public void manualSync() {
        log.info("手动触发评测文档状态同步");
        syncDocumentStatuses();
    }
}
