package com.chinaunicom.ai.knowledge.evaluation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 评测结果DTO
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Schema(description = "评测结果")
public class EvaluationResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private Long knowledgeBaseId;
    
    /**
     * 总问题数
     */
    @Schema(description = "总问题数")
    private Integer totalQuestions;
    
    /**
     * 正确召回数
     */
    @Schema(description = "正确召回数")
    private Integer correctRecalls;
    
    /**
     * 召回率（正确召回数/总问题数）
     */
    @Schema(description = "召回率（正确召回数/总问题数）")
    private Double recallRate;
    
    /**
     * 平均准确率（所有问题准确率的平均值）
     * 准确率 = 召回结果中正确文档数量 / 召回结果总数量
     * 范围：0.0-1.0
     */
    @Schema(description = "平均准确率")
    private Double averageAccuracy;
    
    /**
     * 平均执行时间（毫秒）
     */
    @Schema(description = "平均执行时间（毫秒）")
    private Double avgExecutionTime;

    /**
     * 召回率计算公式说明
     */
    @Schema(description = "召回率计算公式说明")
    private String recallRateFormula;

    /**
     * 平均准确率计算公式说明
     */
    @Schema(description = "平均准确率计算公式说明")
    private String averageAccuracyFormula;

    /**
     * 详细的准确率统计信息
     */
    @Schema(description = "详细的准确率统计信息")
    private AccuracyStatistics accuracyStatistics;

    /**
     * 测试结果列表
     */
    @Schema(description = "测试结果列表")
    private List<TestResult> testResults;
    
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private String startTime;
    
    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private String endTime;
    
    /**
     * 总耗时（毫秒）
     */
    @Schema(description = "总耗时（毫秒）")
    private Long totalDuration;
    
    /**
     * 单个测试结果
     */
    @Data
    @Schema(description = "单个测试结果")
    public static class TestResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 问题文本
         */
        @Schema(description = "问题文本")
        private String question;
        
        /**
         * 期望的文档ID
         */
        @Schema(description = "期望的文档ID")
        private String expectedDocId;
        
        /**
         * 是否召回正确
         */
        @Schema(description = "是否召回正确")
        private Boolean isCorrect;
        
        /**
         * 单个问题的准确率
         * 计算公式：召回结果中正确文档数量 / 召回结果总数量
         * 范围：0.0-1.0
         */
        @Schema(description = "准确率")
        private Double accuracy;
        
        /**
         * 召回的文档数量
         */
        @Schema(description = "召回的文档数量")
        private Integer recallCount;
        
        /**
         * 召回的文档ID列表
         */
        @Schema(description = "召回的文档ID列表")
        private List<String> recalledDocIds;
        
        /**
         * 相似度分数列表
         */
        @Schema(description = "相似度分数列表")
        private List<Double> similarityScores;
        
        /**
         * 执行时间（毫秒）
         */
        @Schema(description = "执行时间（毫秒）")
        private Long executionTime;
    }


}
