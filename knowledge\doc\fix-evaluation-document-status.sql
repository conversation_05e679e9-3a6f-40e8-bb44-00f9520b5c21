-- =====================================================
-- 修复 evaluation_document 和 knowledge_document 状态不一致问题
-- 
-- 问题描述：
-- evaluation_document 表中状态为 VECTORIZE_FAILED 的记录，
-- 对应的 knowledge_document 表中状态实际为 3 (VECTORIZED)
-- 
-- 解决方案：
-- 将 evaluation_document 表中这些记录的状态同步为 VECTORIZED
-- =====================================================

-- 1. 首先查看不一致数据的详细情况
SELECT 
    '修复前数据统计' as action,
    COUNT(*) as total_inconsistent_records,
    ed.knowledge_base_id
FROM evaluation_document ed
LEFT JOIN knowledge_document kd ON ed.knowledge_doc_id = kd.id
WHERE ed.status = 'VECTORIZE_FAILED' 
  AND kd.status = 3  -- VECTORIZED
GROUP BY ed.knowledge_base_id
ORDER BY total_inconsistent_records DESC;

-- 2. 备份受影响的数据（可选，建议在生产环境执行）
CREATE TABLE evaluation_document_backup_20250709 AS
SELECT ed.* 
FROM evaluation_document ed
LEFT JOIN knowledge_document kd ON ed.knowledge_doc_id = kd.id
WHERE ed.status = 'VECTORIZE_FAILED' 
  AND kd.status = 3;

-- 3. 修复状态不一致的数据
UPDATE evaluation_document ed
LEFT JOIN knowledge_document kd ON ed.knowledge_doc_id = kd.id
SET 
    ed.status = 'VECTORIZED',
    ed.update_time = NOW()
WHERE ed.status = 'VECTORIZE_FAILED' 
  AND kd.status = 3  -- VECTORIZED
  AND kd.id IS NOT NULL;

-- 4. 验证修复结果
SELECT 
    '修复后验证' as action,
    COUNT(*) as remaining_inconsistent_records,
    ed.knowledge_base_id
FROM evaluation_document ed
LEFT JOIN knowledge_document kd ON ed.knowledge_doc_id = kd.id
WHERE ed.status = 'VECTORIZE_FAILED' 
  AND kd.status = 3  -- VECTORIZED
GROUP BY ed.knowledge_base_id;

-- 5. 查看修复后的状态分布
SELECT 
    '修复后状态分布' as action,
    ed.status as evaluation_status,
    COUNT(*) as count,
    ed.knowledge_base_id
FROM evaluation_document ed
WHERE ed.knowledge_base_id IN (29, 53, 70)  -- 受影响的知识库
GROUP BY ed.knowledge_base_id, ed.status
ORDER BY ed.knowledge_base_id, ed.status;

-- 6. 检查是否还有其他类型的不一致
SELECT 
    '其他不一致检查' as action,
    ed.status as eval_status,
    kd.status as knowledge_status,
    CASE kd.status
        WHEN 0 THEN 'UPLOADING'
        WHEN 1 THEN 'UPLOADED'
        WHEN 2 THEN 'VECTORIZING'
        WHEN 3 THEN 'VECTORIZED'
        WHEN 4 THEN 'UPLOAD_FAILED'
        WHEN 5 THEN 'VECTORIZE_FAILED'
        WHEN 6 THEN 'DELETED'
        ELSE 'UNKNOWN'
    END as knowledge_status_name,
    COUNT(*) as count
FROM evaluation_document ed
LEFT JOIN knowledge_document kd ON ed.knowledge_doc_id = kd.id
WHERE (
    (ed.status = 'VECTORIZING' AND kd.status != 2) OR
    (ed.status = 'VECTORIZED' AND kd.status != 3) OR
    (ed.status = 'VECTORIZE_FAILED' AND kd.status NOT IN (4, 5)) OR
    (ed.status = 'UPLOADED' AND kd.status NOT IN (1, 2))
)
GROUP BY ed.status, kd.status
ORDER BY count DESC;
