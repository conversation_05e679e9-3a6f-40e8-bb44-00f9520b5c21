spring:
  application:
    name: knowledge-service
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
#自定义配置
demo:
  name: knowledge

#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:com/chinaunicom/ai/knowledge/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.chinaunicom.ai.knowledge.**.entity

#swagger扫描路径配置
swagger:
  base-packages:
    - com.chinaunicom.csf
    - com.chinaunicom.ai.knowledge

server:
  undertow:
    max-file-size: 20MB

# 向量化处理配置
vectorization:
  retry:
    max-attempts: 3          # 最大重试次数
    initial-delay: 100       # 初始重试延迟（毫秒）
    backoff-multiplier: 2.0  # 重试延迟倍数
    max-delay: 5000          # 最大重试延迟（毫秒）
  concurrency:
    max-concurrent-tasks: 10 # 最大并发向量化任务数
    batch-size: 5           # 批处理大小
    batch-delay: 1000       # 批次间延迟（毫秒）