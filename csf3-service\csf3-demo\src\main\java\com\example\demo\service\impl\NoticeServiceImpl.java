
package com.example.demo.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.example.demo.entity.Notice;
import com.example.demo.mapper.NoticeMapper;
import com.example.demo.service.INoticeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 */
@Service
public class NoticeServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements INoticeService {

	@Override
	public IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice) {
		return page.setRecords(baseMapper.selectNoticePage(page, notice));
	}

	@Override
	public boolean updateContent(Long id, String content) {
		return lambdaUpdate().eq(Notice::getId, id).set(Notice::getContent, content).update();
	}

}
