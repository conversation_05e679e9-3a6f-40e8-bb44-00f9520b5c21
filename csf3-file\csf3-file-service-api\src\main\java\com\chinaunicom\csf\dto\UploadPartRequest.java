package com.chinaunicom.csf.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 上传分片请求DTO
 */
@Data
public class UploadPartRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID (32位不含横线的UUID)
     */
    private String fileId;

    /**
     * MinIO 中的对象名
     */
    private String objectName;

    /**
     * 存储的桶名
     */
    private String bucketName;

    /**
     * Upload ID，用于后续分片上传
     */
    private String uploadId;

    /**
     * 分片编号
     */
    private Integer partNumber;

    /**
     * 分片文件
     */
    private MultipartFile partFile;
}
