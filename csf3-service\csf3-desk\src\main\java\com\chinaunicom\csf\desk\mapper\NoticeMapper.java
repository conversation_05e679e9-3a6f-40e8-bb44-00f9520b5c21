
package com.chinaunicom.csf.desk.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.datascope.annotation.DataAuth;
import com.chinaunicom.csf.core.datascope.enums.DataScopeEnum;
import com.chinaunicom.csf.desk.entity.Notice;

import java.util.List;

/**
 * Mapper 接口
 *
 * @since 2018-09-29
 */
public interface NoticeMapper extends BaseMapper<Notice> {

	/**
	 * 前N条数据
	 * @param number
	 * @return
	 */
	List<Notice> topList(Integer number);

	/**
	 * 自定义分页
	 * @param page
	 * @param notice
	 * @return
	 */
	// 表示当前用户只能查询到自己创建的数据
	@DataAuth(column = "create_user", type = DataScopeEnum.OWN)
	List<Notice> selectNoticePage(IPage page, Notice notice);

}
