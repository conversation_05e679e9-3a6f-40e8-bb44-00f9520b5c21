# 平均准确率公式详细显示示例

## 📊 基于test4实际数据的示例

### 原始数据（来自test4评测结果）
```json
{
  "evaluationResult": {
    "totalQuestions": 10,
    "averageAccuracy": 0.8133333333333332,
    "testResults": [
      {"accuracy": 1.0},      // 问题1: 1/1 = 1.0000
      {"accuracy": 1.0},      // 问题2: 1/1 = 1.0000  
      {"accuracy": 1.0},      // 问题3: 3/3 = 1.0000
      {"accuracy": 1.0},      // 问题4: 4/4 = 1.0000
      {"accuracy": 0.6666666666666666}, // 问题5: 2/3 = 0.6667
      {"accuracy": 1.0},      // 问题6: 2/2 = 1.0000
      {"accuracy": 0.6666666666666666}, // 问题7: 2/3 = 0.6667
      {"accuracy": 0.8},      // 问题8: 4/5 = 0.8000
      {"accuracy": 0.0},      // 问题9: 0/0 = 0.0000
      {"accuracy": 1.0}       // 问题10: 2/2 = 1.0000
    ]
  }
}
```

### 修改前的公式显示
```json
{
  "averageAccuracyFormula": "平均准确率 = 所有问题准确率的平均值 = Σ(召回结果中正确文档数/召回结果总数) / 有效问题数 = 8.1333 / 10 = 0.8133"
}
```

### 修改后的公式显示
```json
{
  "averageAccuracyFormula": "平均准确率 = 所有问题准确率的平均值 = (1.0000 + 1.0000 + 1.0000 + 1.0000 + 0.6667 + 1.0000 + 0.6667 + 0.8000 + 0.0000 + 1.0000) / 10 = 8.1333 / 10 = 0.8133"
}
```

## 🔍 详细分析

### 计算步骤展示
1. **收集准确率**: 从10个问题中提取准确率值
2. **格式化数值**: 每个准确率保留4位小数
3. **构建求和**: 用"+"连接所有准确率值
4. **显示除法**: 总和除以有效问题数
5. **最终结果**: 显示最终的平均准确率

### 手动验证
```
1.0000 + 1.0000 + 1.0000 + 1.0000 + 0.6667 + 1.0000 + 0.6667 + 0.8000 + 0.0000 + 1.0000
= 8.1334 (四舍五入)
= 8.1334 / 10 
= 0.8133
```

## 🎯 优势对比

### 修改前的问题
- ❌ 只显示最终结果，无法验证计算过程
- ❌ 用户无法看到每个问题的贡献
- ❌ 难以发现计算错误或异常值

### 修改后的优势  
- ✅ 完整显示计算过程，透明度高
- ✅ 可以看到每个问题的准确率贡献
- ✅ 便于手动验证和问题排查
- ✅ 提供更好的可解释性

## 📋 不同场景的示例

### 场景1：所有问题都正确
```
平均准确率 = 所有问题准确率的平均值 = (1.0000 + 1.0000 + 1.0000) / 3 = 3.0000 / 3 = 1.0000
```

### 场景2：部分问题无召回
```
平均准确率 = 所有问题准确率的平均值 = (1.0000 + 0.0000 + 0.5000) / 3 = 1.5000 / 3 = 0.5000
```

### 场景3：单个问题
```
平均准确率 = 所有问题准确率的平均值 = (0.7500) / 1 = 0.7500 / 1 = 0.7500
```

### 场景4：无有效问题
```
平均准确率 = 所有问题准确率的平均值 = 0 / 0 = 0.0000
```

## 🔧 技术实现要点

### 1. 数值格式化
- 使用`String.format("%.4f", value)`保证4位小数
- 确保显示一致性

### 2. 字符串拼接
- 使用`StringBuilder`提高性能
- 使用`String.join(" + ", list)`简化拼接

### 3. 边界处理
- 处理空列表情况
- 处理除零情况
- 过滤无效问题

### 4. 可读性优化
- 清晰的公式结构
- 合理的空格和符号使用
- 逻辑清晰的计算步骤

## 🧪 测试验证

### API测试命令
```bash
# 测试普通评测接口
curl -X POST "http://localhost:8080/evaluation/run-evaluation" \
  -H "Content-Type: application/json" \
  -d '{"knowledgeBaseId": 65}'

# 测试端到端评测接口  
curl -X POST "http://localhost:8080/evaluation/run-full-evaluation" \
  -H "Content-Type: application/json" \
  -d '{"maxDocuments": 10, "useNewRecallFormula": true}'
```

### 验证要点
1. 检查`averageAccuracyFormula`字段格式
2. 验证计算过程的正确性
3. 确认数值精度符合要求
4. 测试各种边界情况
