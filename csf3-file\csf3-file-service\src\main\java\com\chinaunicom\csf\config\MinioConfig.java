package com.chinaunicom.csf.config;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.chinaunicom.csf.props.MinioProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * S3 (兼容MinIO) 配置类
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class MinioConfig {

    private final MinioProperties minioProperties;

    /**
     * 注入 AmazonS3 客户端 (兼容MinIO)
     * @return AmazonS3 客户端
     */
    @Bean
    public AmazonS3 amazonS3Client() {
        log.info("初始化 AmazonS3 客户端 (兼容MinIO)");
        AWSCredentials credentials = new BasicAWSCredentials(
                minioProperties.getAccessKey(),
                minioProperties.getSecretKey()
        );
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(
                        minioProperties.getEndpoint(),
                        minioProperties.getRegion()
                ))
                .withPathStyleAccessEnabled(true) // 必须开启路径风格访问，以兼容MinIO
                .build();
    }
} 