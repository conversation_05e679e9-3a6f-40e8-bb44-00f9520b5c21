
package com.chinaunicom.csf.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinaunicom.csf.core.boot.ctrl.CsfController;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.secure.CsfUser;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.constant.CsfConstant;
import com.chinaunicom.csf.core.tool.node.INode;
import com.chinaunicom.csf.core.tool.utils.CacheUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.system.entity.Role;
import com.chinaunicom.csf.system.service.IRoleService;
import com.chinaunicom.csf.system.vo.GrantVO;
import com.chinaunicom.csf.system.vo.RoleVO;
import com.chinaunicom.csf.system.wrapper.RoleWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static com.chinaunicom.csf.core.tool.utils.CacheUtil.SYS_CACHE;

/**
 * 控制器
 *
 */
@RestController
@AllArgsConstructor
@RequestMapping("/role")
@Tag(description = "角色", name = "角色")
public class RoleController extends CsfController {

	private IRoleService roleService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情",description = "传入role")
	public R<RoleVO> detail(Role role) {
		Role detail = roleService.getOne(Condition.getQueryWrapper(role));
		return R.data(RoleWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@Parameters({
		@Parameter(name = "roleName", description = "参数名称",  in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "roleAlias", description = "角色别名",  in = ParameterIn.QUERY, schema = @Schema(type = "string"))
	})
	@ApiOperationSupport(order = 2)
	@Operation(summary = "列表",description = "传入role")
	public R<List<INode>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> role, CsfUser csfUser) {
		QueryWrapper<Role> queryWrapper = Condition.getQueryWrapper(role, Role.class);
		List<Role> list = roleService.list((!csfUser.getTenantId().equals(CsfConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Role::getTenantId, csfUser.getTenantId()) : queryWrapper);
		return R.data(RoleWrapper.build().listNodeVO(list));
	}

	/**
	 * 获取角色树形结构
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "树形结构",description = "树形结构")
	public R<List<RoleVO>> tree(String tenantId, CsfUser csfUser) {
		List<RoleVO> tree = roleService.tree(Func.toStr(tenantId, csfUser.getTenantId()));
		return R.data(tree);
	}


	/**
	 * 获取指定角色树形结构
	 */
	@GetMapping("/tree-by-id")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "树形结构",description = "树形结构")
	public R<List<RoleVO>> treeById(Long roleId, CsfUser csfUser) {
		Role role = roleService.getById(roleId);
		List<RoleVO> tree = roleService.tree(Func.notNull(role) ? role.getTenantId() : csfUser.getTenantId());
		return R.data(tree);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "新增或修改",description = "传入role")
	public R submit(@Valid @RequestBody Role role, CsfUser user) {
		CacheUtil.clear(SYS_CACHE);
		if (Func.isEmpty(role.getId())) {
			role.setTenantId(user.getTenantId());
		}
		return R.status(roleService.saveOrUpdate(role));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "删除",description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(roleService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 设置菜单权限
	 */
	@PostMapping("/grant")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "权限设置",description = "传入roleId集合以及menuId集合")
	public R grant(@RequestBody GrantVO grantVO) {
		CacheUtil.clear(SYS_CACHE);
		boolean temp = roleService.grant(grantVO.getRoleIds(), grantVO.getMenuIds(), grantVO.getDataScopeIds(),grantVO.getApiScopeIds());
		return R.status(temp);
	}
}
