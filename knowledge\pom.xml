<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.chinaunicom.csf</groupId>
        <artifactId>csf3</artifactId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chinaunicom.ai</groupId>
    <artifactId>knowledge</artifactId>
    <name>${project.artifactId}</name>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <description>knowledge-service</description>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.chinaunicom.ai</groupId>
                <artifactId>knowledge-service-api</artifactId>
                <version>1.0.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf-core-boot</artifactId>
            <version>1.8.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-swagger</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <archetypeFilteredExtentions>java,xml,yml,yamlMETA-INF/*</archetypeFilteredExtentions>
                </configuration>
            </plugin>
        </plugins>
    </build>
<modules>
    <module>knowledge-service-api</module>
    <module>knowledge-service</module>
  </modules>
</project>
