package com.chinaunicom.ai.knowledge.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.chinaunicom.ai.knowledge.config.S3Properties;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.chinaunicom.ai.knowledge.vo.FileUploadInitResultVO;
import com.chinaunicom.csf.core.log.exception.ServiceException;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class FileUploadService {

    // 创建固定大小的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    @PreDestroy
    public void destroy() {
        executorService.shutdown();
    }

    @Autowired
    private AmazonS3 amazonS3;
    @Autowired
    private S3Properties s3Properties;
    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;
    @Autowired
    private PythonVectorizationService pythonVectorizationService; // 新增：用于触发Python向量化服务

    // 临时保存 uploadId 和对应的 objectKey
    private Map<String, String> uploadIdToKey = new ConcurrentHashMap<>();
    // 临时保存 uploadId 对应的已上传 PartETag 列表
    private Map<String, List<PartETag>> partETagsMap = new ConcurrentHashMap<>();

    /**
     * 初始化 MinIO 分片上传并保存文档元数据
     * MinIO上的文件名会加上文件类型后缀。
     * @param knowledgeBaseId 知识库ID
     * @param fileName 文件名称
     * @param fileType 文件类型
     * @param fileSize 文件大小
     * @return 包含 uploadId 和 documentId 的VO
     */
    public FileUploadInitResultVO initMultipartUpload(Long knowledgeBaseId, String fileName, String fileType, Long fileSize) {
        log.info("开始初始化分片上传，知识库ID: {}, 文件名: {}, 文件类型: {}, 文件大小: {}", knowledgeBaseId, fileName, fileType, fileSize);
        String bucket = s3Properties.getBucketName();
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();
        if (StringUtils.isBlank(tenantId) || userId == null || userId.equals(-1L)) {
            throw new ServiceException("请登录");
        }

        // 检查并创建存储桶
        if (!amazonS3.doesBucketExistV2(bucket)) {
            amazonS3.createBucket(bucket);
        }

        // 构造唯一 objectKey (MinIO上的文件ID)，确保包含文件类型后缀
        String baseObjectId = UUID.randomUUID().toString().replaceAll("-", "");
        String fileObjectId;
        if (StringUtils.hasText(fileType)) {
            // 确保文件类型不带点，然后添加点前缀
            String cleanFileType = fileType.toLowerCase().startsWith(".") ? fileType.substring(1) : fileType;
            fileObjectId = baseObjectId + "." + cleanFileType;
        } else {
            fileObjectId = baseObjectId; // 如果fileType为空，则不加后缀
        }


        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucket, fileObjectId);
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(fileType); // 设置文件类型
        initRequest.setObjectMetadata(objectMetadata);
        InitiateMultipartUploadResult initResult = amazonS3.initiateMultipartUpload(initRequest);
        String uploadId = initResult.getUploadId();

        // 保存映射关系
        uploadIdToKey.put(uploadId, fileObjectId);
        partETagsMap.put(uploadId, new ArrayList<>());

        // 保存文档元数据到数据库
        KnowledgeDocument document = new KnowledgeDocument();
        document.setBaseId(knowledgeBaseId);
        document.setFileName(fileName);
        document.setFileType(fileType); // 保存原始fileType
        document.setFileSize(fileSize);
        document.setFileObjectId(fileObjectId); // MinIO上的完整文件名（包含后缀）
        document.setFileUrl(bucket + "/" + fileObjectId); // 简单的文件URL
        document.setStatus(DocumentStatusEnum.UPLOADING.getCode()); // 设置为上传中状态
        document.setIsDeleted(0);
        document.setCreateUser(userId);
        document.setCreateTime(LocalDateTime.now());
        document.setTenantId(tenantId);
        knowledgeDocumentMapper.insert(document); // 插入数据库

        FileUploadInitResultVO result = new FileUploadInitResultVO();
        result.setUploadId(uploadId);
        result.setDocumentId(document.getId()); // 返回新创建的文档ID
        log.info("分片上传初始化成功，文档ID: {}, UploadId: {}, 文件对象ID: {}", document.getId(), uploadId, fileObjectId);
        return result;
    }

    /**
     * 上传单个分片
     * @param uploadId 分片上传ID
     * @param partNumber 分片序号
     * @param filePart 文件分片
     * @return 分片ETag
     * @throws IOException IO异常
     */
    public String uploadPart(String uploadId, int partNumber, MultipartFile filePart) throws IOException {
        log.info("开始上传文件分片，UploadId: {}, 分片序号: {}, 分片大小: {}", uploadId, partNumber, filePart.getSize());
        String bucket = s3Properties.getBucketName();
        String key = uploadIdToKey.get(uploadId);
        if (key == null) {
            log.error("无效的上传ID: {}", uploadId);
            throw new IllegalArgumentException("无效的上传ID");
        }

        // 构建上传请求
        UploadPartRequest uploadRequest = new UploadPartRequest()
                .withBucketName(bucket)
                .withKey(key)
                .withUploadId(uploadId)
                .withPartNumber(partNumber)
                .withInputStream(filePart.getInputStream())
                .withPartSize(filePart.getSize());
        // 执行上传
        UploadPartResult uploadResult = amazonS3.uploadPart(uploadRequest);
        // 保存 ETag
        PartETag partETag = uploadResult.getPartETag();
        partETagsMap.get(uploadId).add(partETag);
        log.info("文件分片上传成功，UploadId: {}, 分片序号: {}, ETag: {}", uploadId, partNumber, partETag.getETag());
        return partETag.getETag();
    }

    /**
     * 完成分片上传并更新数据库记录，触发向量化服务
     * @param uploadId 分片上传ID
     * @param documentId 知识文档ID
     */
    public void completeMultipartUpload(String uploadId, Long documentId) {
        log.info("开始完成分片上传，UploadId: {}, 文档ID: {}", uploadId, documentId);
        String bucket = s3Properties.getBucketName();
        String fileObjectId = uploadIdToKey.get(uploadId);
        List<PartETag> partETags = partETagsMap.get(uploadId);
        Long userId = SecureUtil.getUserId();

        if (fileObjectId == null || partETags == null) {
            log.error("完成上传失败：uploadId或partETags不存在，uploadId: {}", uploadId);
            // 更新文档状态为上传失败
            KnowledgeDocument document = new KnowledgeDocument();
            document.setId(documentId);
            document.setStatus(DocumentStatusEnum.UPLOAD_FAILED.getCode());
            document.setUpdateTime(LocalDateTime.now());
            document.setUpdateUser(userId);
            knowledgeDocumentMapper.updateById(document);
            return;
        }

        try {
            // 合并分片
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(
                    bucket, fileObjectId, uploadId, partETags);
            amazonS3.completeMultipartUpload(compRequest);
            log.info("MinIO分片合并完成，UploadId: {}, 文件对象ID: {}", uploadId, fileObjectId);

            // 清理临时保存的映射关系
            uploadIdToKey.remove(uploadId);
            partETagsMap.remove(uploadId);
            log.info("已清理分片上传临时映射关系，UploadId: {}", uploadId);

            // 更新文档状态为已上传，并异步触发向量化
            KnowledgeDocument document = knowledgeDocumentMapper.selectById(documentId);
            if (document != null) {
                document.setStatus(DocumentStatusEnum.UPLOADED.getCode()); // 设置为已上传状态
                document.setUpdateTime(LocalDateTime.now());
                document.setUpdateUser(userId);
                knowledgeDocumentMapper.updateById(document);
                log.info("文档状态更新为已上传，文档ID: {}, 文件对象ID: {}", documentId, fileObjectId);

                // 异步触发Python服务进行向量化
                executorService.submit(() -> {
                    try {
                        log.info("触发Python服务进行向量化，文档ID: {}", documentId);
                        pythonVectorizationService.triggerVectorization(
                                document.getId(),
                                document.getBaseId(),
                                document.getFileObjectId(),
                                document.getFileName()
                        );
                        // 向量化成功后，Python服务会回调更新文档状态为VECTORIZED
                    } catch (Exception e) {
                        log.error("触发Python向量化服务失败，文档ID: {}", documentId, e);
                        // 更新文档状态为向量化失败 - 使用重试机制
                        try {
                            updateDocumentStatusWithRetry(documentId, DocumentStatusEnum.VECTORIZE_FAILED, userId);
                        } catch (Exception updateException) {
                            log.error("更新文档状态失败，文档ID: {}", documentId, updateException);
                        }
                    }
                });
            } else {
                log.warn("完成上传时未找到文档记录，文档ID: {}", documentId);
            }

        } catch (Exception e) {
            log.error("完成Multipart Upload失败，uploadId: {}, documentId: {}", uploadId, documentId, e);
            // 更新文档状态为上传失败
            KnowledgeDocument document = new KnowledgeDocument();
            document.setId(documentId);
            document.setStatus(DocumentStatusEnum.UPLOAD_FAILED.getCode());
            document.setUpdateTime(LocalDateTime.now());
            document.setUpdateUser(userId);
            knowledgeDocumentMapper.updateById(document);
        }
    }

    /**
     * 从MinIO下载文件到临时文件
     * @param bucketName 存储桶名称
     * @param key 对象键
     * @param fileType 文件类型
     * @return 临时文件
     * @throws IOException IO异常
     */
    public File downloadS3ObjectToTempFile(String bucketName, String key, String fileType) throws IOException {
        // 创建临时文件（根据业务需求设置前缀和后缀）
        File tempFile = File.createTempFile(key, "." + fileType);
        tempFile.deleteOnExit(); // 确保程序退出时删除临时文件

        try (S3Object s3Object = amazonS3.getObject(bucketName, key);
             InputStream inputStream = s3Object.getObjectContent();
             FileOutputStream outputStream = new FileOutputStream(tempFile)) {

            // 将 S3 对象内容复制到临时文件
            IOUtils.copy(inputStream, outputStream);
            log.info("文件下载到临时文件成功，桶: {}, 对象键: {}, 临时文件路径: {}", bucketName, key, tempFile.getAbsolutePath());
            return tempFile;
        } catch (Exception e) {
            log.error("下载S3对象到临时文件失败，桶: {}, 对象键: {}", bucketName, key, e);
            throw e;
        }
    }

    /**
     * 删除MinIO上的文件
     * @param fileObjectId MinIO文件对象ID
     */
    public void deleteMinioFile(String fileObjectId) {
        log.info("开始删除MinIO文件，文件对象ID: {}", fileObjectId);
        String bucket = s3Properties.getBucketName();
        try {
            amazonS3.deleteObject(bucket, fileObjectId);
            log.info("成功从MinIO删除文件: {}", fileObjectId);
        } catch (Exception e) {
            log.error("从MinIO删除文件失败: {}", fileObjectId, e);
            throw new RuntimeException("删除MinIO文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 带重试机制的文档状态更新
     * 解决异步任务中的数据库锁等待问题
     */
    private void updateDocumentStatusWithRetry(Long documentId, DocumentStatusEnum status, Long userId) {
        int maxRetries = 3;
        int baseDelay = 200; // 毫秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                KnowledgeDocument updateDoc = new KnowledgeDocument();
                updateDoc.setId(documentId);
                updateDoc.setStatus(status.getCode());
                updateDoc.setUpdateTime(LocalDateTime.now());
                updateDoc.setUpdateUser(userId);

                int updateCount = knowledgeDocumentMapper.updateById(updateDoc);
                if (updateCount > 0) {
                    log.debug("异步任务中文档状态更新成功，文档ID: {}, 状态: {}, 尝试次数: {}",
                            documentId, status.getName(), attempt);
                    return;
                } else {
                    log.warn("异步任务中文档状态更新失败，影响行数为0，文档ID: {}, 状态: {}",
                            documentId, status.getName());
                    return;
                }

            } catch (org.springframework.dao.CannotAcquireLockException e) {
                log.warn("异步任务中文档状态更新遇到锁等待，文档ID: {}, 状态: {}, 尝试次数: {}/{}",
                        documentId, status.getName(), attempt, maxRetries);

                if (attempt == maxRetries) {
                    log.error("异步任务中文档状态更新最终失败，文档ID: {}, 状态: {}",
                            documentId, status.getName(), e);
                    throw e; // 重新抛出异常
                }

                // 等待后重试
                try {
                    Thread.sleep(baseDelay * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }

            } catch (Exception e) {
                log.error("异步任务中文档状态更新发生异常，文档ID: {}, 状态: {}, 尝试次数: {}",
                        documentId, status.getName(), attempt, e);

                if (attempt == maxRetries) {
                    throw e; // 重新抛出异常
                }

                // 等待后重试
                try {
                    Thread.sleep(baseDelay * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
    }
}