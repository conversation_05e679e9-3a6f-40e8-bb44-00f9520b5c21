package com.chinaunicom.ai.knowledge.evaluation.service;

import com.amazonaws.services.s3.AmazonS3;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinaunicom.ai.knowledge.evaluation.dto.CleanupResult;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationTestHistoryMapper;

import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 评测数据清理服务
 * 负责清理评测相关的所有数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationCleanupService {
    
    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final EvaluationTestHistoryMapper evaluationTestHistoryMapper;
    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    private final AmazonS3 amazonS3;
    private final IElasticSearchService elasticSearchService;
    
    @Value("${minio.bucket-name}")
    private String bucketName;
    
    /**
     * 清理指定知识库的所有评测数据
     */
    public CleanupResult cleanupEvaluationData(Long knowledgeBaseId, Boolean cleanupFiles, Boolean cleanupIndex) {
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始清理评测数据，知识库ID: {}", knowledgeBaseId);
            
            // 1. 获取所有评测文档
            List<EvaluationDocument> documents = evaluationDocumentMapper.selectByKnowledgeBaseId(knowledgeBaseId);
            log.info("找到 {} 个评测文档需要清理", documents.size());
            
            // 2. 清理MinIO文件
            int deletedFiles = 0;
            List<String> failedFiles = new ArrayList<>();
            if (cleanupFiles && !documents.isEmpty()) {
                for (EvaluationDocument document : documents) {
                    try {
                        if (document.getFileObjectId() != null) {
                            amazonS3.deleteObject(bucketName, document.getFileObjectId());
                            deletedFiles++;
                            log.debug("删除MinIO文件: {}", document.getFileObjectId());
                        }
                    } catch (Exception e) {
                        log.warn("删除MinIO文件失败: {}", document.getFileObjectId(), e);
                        failedFiles.add(document.getFileObjectId());
                    }
                }
            }
            
            // 3. 清理Elasticsearch索引数据
            int deletedEsDocuments = 0;
            if (cleanupIndex && !documents.isEmpty()) {
                String indexName = "knowledge_base_" + knowledgeBaseId;
                for (EvaluationDocument document : documents) {
                    if (document.getKnowledgeDocId() != null) {
                        try {
                            boolean deleted = elasticSearchService.deleteDocumentByMysqlDocId(indexName, document.getKnowledgeDocId());
                            if (deleted) {
                                deletedEsDocuments++;
                                log.debug("删除ES文档片段: 知识库文档ID={}, 索引={}", document.getKnowledgeDocId(), indexName);
                            }
                        } catch (Exception e) {
                            log.warn("删除ES文档片段失败: 知识库文档ID={}, 索引={}", document.getKnowledgeDocId(), indexName, e);
                        }
                    }
                }
                log.info("ES索引清理完成，删除文档片段数: {}", deletedEsDocuments);
            }

            // 4. 清理知识库文档记录
            int deletedKnowledgeDocuments = 0;
            for (EvaluationDocument document : documents) {
                if (document.getKnowledgeDocId() != null) {
                    try {
                        knowledgeDocumentMapper.deleteById(document.getKnowledgeDocId());
                        deletedKnowledgeDocuments++;
                        log.debug("删除知识库文档: {}", document.getKnowledgeDocId());
                    } catch (Exception e) {
                        log.warn("删除知识库文档失败: {}", document.getKnowledgeDocId(), e);
                    }
                }
            }
            
            // 5. 清理评测历史记录
            int deletedTestHistory = cleanupTestHistory(knowledgeBaseId);

            // 6. 清理评测文档记录
            QueryWrapper<EvaluationDocument> docWrapper = new QueryWrapper<>();
            docWrapper.eq("knowledge_base_id", knowledgeBaseId);
            int deletedDocuments = evaluationDocumentMapper.delete(docWrapper);
            
            long duration = System.currentTimeMillis() - startTime;
            
            log.info("评测数据清理完成，知识库ID: {}, 删除文档: {}, 删除历史: {}, 删除知识库文档: {}, 删除文件: {}, 删除ES片段: {}, 耗时: {}ms",
                    knowledgeBaseId, deletedDocuments, deletedTestHistory, deletedKnowledgeDocuments, deletedFiles, deletedEsDocuments, duration);
            
            if (failedFiles.isEmpty()) {
                return CleanupResult.success(knowledgeBaseId, deletedDocuments, deletedTestHistory, 
                        deletedKnowledgeDocuments, deletedFiles, duration, cleanupFiles, cleanupIndex);
            } else {
                return CleanupResult.partial(knowledgeBaseId, deletedDocuments, deletedTestHistory,
                        deletedKnowledgeDocuments, deletedFiles, failedFiles, duration, cleanupFiles, cleanupIndex);
            }
            
        } catch (Exception e) {
            log.error("清理评测数据失败，知识库ID: {}", knowledgeBaseId, e);
            return CleanupResult.failed("清理评测数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 仅清理评测历史记录
     */
    public int cleanupTestHistory(Long knowledgeBaseId) {
        try {
            QueryWrapper<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationTestHistory> wrapper = new QueryWrapper<>();
            wrapper.eq("knowledge_base_id", knowledgeBaseId);
            int deletedCount = evaluationTestHistoryMapper.delete(wrapper);
            
            log.info("清理评测历史完成，知识库ID: {}, 删除记录数: {}", knowledgeBaseId, deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("清理评测历史失败，知识库ID: {}", knowledgeBaseId, e);
            throw new RuntimeException("清理评测历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理指定原始文档ID的数据
     */
    public boolean cleanupByOriginalDocId(String originalDocId) {
        try {
            log.info("开始清理指定文档数据，原始文档ID: {}", originalDocId);
            
            // 1. 查找评测文档
            EvaluationDocument document = evaluationDocumentMapper.selectByOriginalDocId(originalDocId);
            if (document == null) {
                log.warn("未找到指定的评测文档: {}", originalDocId);
                return false;
            }
            
            // 2. 清理MinIO文件
            if (document.getFileObjectId() != null) {
                try {
                    amazonS3.deleteObject(bucketName, document.getFileObjectId());
                    log.debug("删除MinIO文件: {}", document.getFileObjectId());
                } catch (Exception e) {
                    log.warn("删除MinIO文件失败: {}", document.getFileObjectId(), e);
                }
            }
            
            // 3. 清理Elasticsearch索引数据
            if (document.getKnowledgeDocId() != null) {
                try {
                    String indexName = "knowledge_base_" + document.getKnowledgeBaseId();
                    boolean deleted = elasticSearchService.deleteDocumentByMysqlDocId(indexName, document.getKnowledgeDocId());
                    if (deleted) {
                        log.debug("删除ES文档片段: 知识库文档ID={}, 索引={}", document.getKnowledgeDocId(), indexName);
                    }
                } catch (Exception e) {
                    log.warn("删除ES文档片段失败: 知识库文档ID={}", document.getKnowledgeDocId(), e);
                }
            }

            // 4. 清理知识库文档
            if (document.getKnowledgeDocId() != null) {
                try {
                    knowledgeDocumentMapper.deleteById(document.getKnowledgeDocId());
                    log.debug("删除知识库文档: {}", document.getKnowledgeDocId());
                } catch (Exception e) {
                    log.warn("删除知识库文档失败: {}", document.getKnowledgeDocId(), e);
                }
            }
            
            // 5. 清理相关测试历史
            QueryWrapper<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationTestHistory> historyWrapper = new QueryWrapper<>();
            historyWrapper.eq("expected_doc_id", originalDocId);
            evaluationTestHistoryMapper.delete(historyWrapper);

            // 6. 清理评测文档记录
            evaluationDocumentMapper.deleteById(document.getId());
            
            log.info("清理指定文档数据完成，原始文档ID: {}", originalDocId);
            return true;
            
        } catch (Exception e) {
            log.error("清理指定文档数据失败，原始文档ID: {}", originalDocId, e);
            return false;
        }
    }
    
    /**
     * 获取清理预览信息
     */
    public CleanupPreview getCleanupPreview(Long knowledgeBaseId) {
        try {
            int documentCount = evaluationDocumentMapper.countByKnowledgeBaseId(knowledgeBaseId);
            int historyCount = evaluationTestHistoryMapper.countByKnowledgeBaseId(knowledgeBaseId);
            
            CleanupPreview preview = new CleanupPreview();
            preview.setKnowledgeBaseId(knowledgeBaseId);
            preview.setDocumentCount(documentCount);
            preview.setHistoryCount(historyCount);
            
            return preview;
            
        } catch (Exception e) {
            log.error("获取清理预览失败，知识库ID: {}", knowledgeBaseId, e);
            throw new RuntimeException("获取清理预览失败: " + e.getMessage());
        }
    }
    
    @lombok.Data
    public static class CleanupPreview {
        private Long knowledgeBaseId;
        private Integer documentCount;
        private Integer historyCount;
    }
}
