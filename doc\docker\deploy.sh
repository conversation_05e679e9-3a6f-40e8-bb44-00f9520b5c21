#!/bin/bash
CWD=$(dirname "$(readlink -f "$0")")

# 使用说明，用来提示输入参数
function usage() {
	echo "Usage: sh 执行脚本.sh [port|base|mvnJars|cpJars|modules|front|elk|restart|stop|rm]"
	exit 1
}

# 开启所需端口
function port(){
	firewall-cmd --add-port=80/tcp --permanent
	firewall-cmd --add-port=8080/tcp --permanent
	firewall-cmd --add-port=8848/tcp --permanent
	firewall-cmd --add-port=9848/tcp --permanent
	firewall-cmd --add-port=9849/tcp --permanent
	firewall-cmd --add-port=6379/tcp --permanent
	firewall-cmd --add-port=3306/tcp --permanent
	firewall-cmd --add-port=9100/tcp --permanent
	firewall-cmd --add-port=9200/tcp --permanent
	firewall-cmd --add-port=9201/tcp --permanent
	firewall-cmd --add-port=9202/tcp --permanent
	firewall-cmd --add-port=9203/tcp --permanent
	firewall-cmd --add-port=9300/tcp --permanent
	service firewalld restart
}

# 启动基础环境（必须）
function base(){
  # 基础组件
	docker-compose up -d csf-mysql csf-redis csf-sentinel csf-nacos
}

# 打包jar
function mvnJars() {
  cd ./../../
  mvn clean package -DskipTests
  cd ${CWD}
}

# 启动程序模块（必须）
function modules(){
  cpJars
  rm
	docker-compose up -d --build csf-user csf-admin csf-auth csf-desk csf-demo csf-system csf-report csf-log csf-develop csf-gateway csf-swagger
}

# 开发平台前端(vue 3.0)
function front() {
  docker-compose up -d --build csf-nginx
}

# ELK组件
function elk() {
  docker-compose up -d elasticsearch logstash kibana
}

# 重启
function restart(){
	docker-compose restart csf-user csf-admin csf-auth csf-desk csf-demo csf-system csf-report csf-log csf-develop csf-gateway csf-swagger
}

# 关闭所有环境/模块
function stop() {
	docker-compose stop csf-user csf-admin csf-auth csf-desk csf-demo csf-system csf-report csf-log csf-develop csf-gateway csf-swagger
}

# 删除所有环境/模块
function rm() {
  docker-compose stop csf-user csf-admin csf-auth csf-desk csf-demo csf-system csf-report csf-log csf-develop csf-gateway csf-swagger
	docker-compose rm -f csf-user csf-admin csf-auth csf-desk csf-demo csf-system csf-report csf-log csf-develop csf-gateway csf-swagger
	docker rmi docker_csf-user docker_csf-admin docker_csf-auth docker_csf-desk docker_csf-demo docker_csf-system docker_csf-report docker_csf-log docker_csf-develop docker_csf-gateway docker_csf-swagger
}

# 拷贝jar包到docker打包路径下
function cpJars() {
  cd ${CWD}
  pwd
  cp ./../../csf3-auth/target/*.jar ./csf/csf-auth/jar/
  cp ./../../csf3-gateway/target/*.jar ./csf/csf-gateway/jar/
  cp ./../../csf3-ops/csf3-admin/target/*.jar ./csf/csf-ops/csf-admin/jar/
  cp ./../../csf3-ops/csf3-develop/target/*.jar ./csf/csf-ops/csf-develop/jar/
  cp ./../../csf3-ops/csf3-mq-consumer/target/*.jar ./csf/csf-ops/csf-mq-consumer/jar/
  cp ./../../csf3-ops/csf3-mq-producer/target/*.jar ./csf/csf-ops/csf-mq-producer/jar/
  cp ./../../csf3-ops/csf3-report/target/*.jar ./csf/csf-ops/csf-report/jar/
  cp ./../../csf3-ops/csf3-resource/target/*.jar ./csf/csf-ops/csf-resource/jar/
  cp ./../../csf3-ops/csf3-seata-order/target/*.jar ./csf/csf-ops/csf-seata-order/jar/
  cp ./../../csf3-ops/csf3-seata-storage/target/*.jar ./csf/csf-ops/csf-seata-storage/jar/
  cp ./../../csf3-ops/csf3-swagger/target/*.jar ./csf/csf-ops/csf-swagger/jar/
  cp ./../../csf3-service/csf3-demo/target/*.jar ./csf/csf-service/csf-demo/jar/
  cp ./../../csf3-service/csf3-desk/target/*.jar ./csf/csf-service/csf-desk/jar/
  cp ./../../csf3-service/csf3-log/target/*.jar ./csf/csf-service/csf-log/jar/
  cp ./../../csf3-service/csf3-system/target/*.jar ./csf/csf-service/csf-system/jar/
  cp ./../../csf3-service/csf3-user/target/*.jar ./csf/csf-service/csf-user/jar/
  echo "完成jar包复制"
}

function all() {
    base
    mvnJars
    rm
    modules
    restart
    front
    docker-compose ps
}

# 根据输入参数，选择执行对应方法，不输入则执行使用说明
case "$1" in
"all")
	all
;;
"port")
	port
;;
"cpJars")
  cpJars
;;
"base")
	base
;;
"mvnJars")
	mvnJars
;;
"modules")
	modules
;;
"elk")
	elk
;;
"front")
	front
;;
"restart")
	restart
;;
"stop")
	stop
;;
"rm")
	rm
;;
*)
	usage
;;
esac
