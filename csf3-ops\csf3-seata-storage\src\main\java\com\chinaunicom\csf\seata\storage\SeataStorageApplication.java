
package com.chinaunicom.csf.seata.storage;

import com.chinaunicom.csf.core.launch.constant.AppConstant;
import com.chinaunicom.csf.plugins.transaction.annotation.SeataCloudApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Storage启动器
 */
@SeataCloudApplication
@EnableFeignClients(AppConstant.BASE_PACKAGES)
public class SeataStorageApplication {

	public static void main(String[] args) {
		SpringApplication.run(SeataStorageApplication.class, args);
	}

}

