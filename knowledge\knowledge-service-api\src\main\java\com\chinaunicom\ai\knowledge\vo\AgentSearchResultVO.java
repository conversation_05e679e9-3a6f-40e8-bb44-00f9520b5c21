package com.chinaunicom.ai.knowledge.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 智能体向量搜索结果VO
 */
@Data
@Schema(description = "智能体向量搜索结果VO")
public class AgentSearchResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库ID")
    private Long knowledgeBaseId;

    @Schema(description = "知识库名称")
    private String knowledgeBaseName;

    @Schema(description = "文档ID")
    private Long documentId;

    @Schema(description = "文档名称")
    private String documentName;

    @Schema(description = "文档片段内容")
    private String fragmentContent;

    @Schema(description = "匹配分数")
    private float score;
}