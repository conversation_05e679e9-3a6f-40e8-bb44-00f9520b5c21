package com.chinaunicom.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseService;
import com.chinaunicom.ai.knowledge.entity.Notice;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface NoticeService extends BaseService<Notice> {

	/**
	 * 自定义分页
	 * @param page
	 * @param notice
	 * @return
	 */
	IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice);

	/**
	 * 获取前N条数据
	 * @param number
	 * @return
	 */
	List<Notice> top(Integer number);
}
