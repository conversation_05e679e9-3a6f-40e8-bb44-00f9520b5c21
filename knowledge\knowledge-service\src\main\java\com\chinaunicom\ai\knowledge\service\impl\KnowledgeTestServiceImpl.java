package com.chinaunicom.ai.knowledge.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.ai.knowledge.dto.KnowledgeTestHistoryQueryDTO;
import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.dto.KnowledgeTestRecallDTO;
import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.entity.KnowledgeTestHistory;
import com.chinaunicom.ai.knowledge.entity.KnowledgeTestResult;
import com.chinaunicom.ai.knowledge.enums.ModelTypeEnum;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeTestHistoryMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeTestResultMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.KnowledgeTestService;
import com.chinaunicom.ai.knowledge.service.RerankService;
import com.chinaunicom.ai.knowledge.service.impl.PythonEmbeddingService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import com.chinaunicom.ai.knowledge.vo.DatasetTestHistoryItemVO;
import com.chinaunicom.ai.knowledge.vo.DatasetTestResultVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeTestHistoryListVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 知识库检索测试服务实现类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Slf4j
@Service
@AllArgsConstructor
public class KnowledgeTestServiceImpl extends ServiceImpl<KnowledgeTestHistoryMapper, KnowledgeTestHistory>
        implements KnowledgeTestService {

    private final KnowledgeTestResultMapper testResultMapper;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final IElasticSearchService elasticSearchService;
    private final PythonEmbeddingService pythonEmbeddingService;
    private final AiModelMapper aiModelMapper;
    private final RerankService rerankService;
    private final HybridSearchConfig hybridSearchConfig; // 注入混合检索配置

    /**
     * 执行知识库检索测试
     * 
     * @param testRecallDTO 检索测试请求DTO
     * @return 测试历史项VO，包含测试结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DatasetTestHistoryItemVO executeRecallTest(KnowledgeTestRecallDTO testRecallDTO) {
        log.info("开始执行知识库检索测试，知识库ID: {}, 问题文本: {}",
                testRecallDTO.getKnowledgeBaseId(), testRecallDTO.getQueryText());

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 生成32位不含横线的UUID
        String testHistoryId = generateUUID32();

        try {
            // 1. 调用带分数的召回服务获取结果
            List<AgentSearchResultVO> searchResults = executeRecallWithScore(testRecallDTO);

            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executionTimeMs = endTime - startTime;
            double executionTimeSeconds = executionTimeMs / 1000.0;

            // 2. 获取知识库信息并保存测试历史记录
            KnowledgeTestHistory testHistory = createTestHistory(testHistoryId, testRecallDTO,
                    executionTimeMs, searchResults.size());
            save(testHistory);

            // 3. 保存测试结果并转换为VO
            List<DatasetTestResultVO> testResultVOs = saveAndConvertSearchResults(
                    testHistoryId, searchResults);
            
            // 4. 构建返回结果
            DatasetTestHistoryItemVO resultVO = new DatasetTestHistoryItemVO();
            resultVO.setId(testHistoryId);
            resultVO.setQuestion(testRecallDTO.getQueryText());
            resultVO.setCreateTime(testHistory.getCreateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            resultVO.setSecond(executionTimeSeconds);
            resultVO.setTestResult(testResultVOs);
            
            log.info("知识库检索测试完成，测试ID: {}, 执行时间: {}ms, 结果数量: {}",
                    testHistoryId, executionTimeMs, searchResults.size());
            
            return resultVO;
            
        } catch (Exception e) {
            log.error("执行知识库检索测试失败，测试ID: {}", testHistoryId, e);
            throw new RuntimeException("执行知识库检索测试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成32位不含横线的UUID
     */
    private String generateUUID32() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 创建测试历史记录
     */
    private KnowledgeTestHistory createTestHistory(String testHistoryId, KnowledgeTestRecallDTO testRecallDTO,
                                                  long executionTimeMs, int resultCount) {
        String tenantId = SecureUtil.getTenantId();
        if (!StringUtils.hasText(tenantId)) {
            tenantId = "000000";
        }

        Long currentUserId = SecureUtil.getUserId();
        LocalDateTime now = LocalDateTime.now();

        KnowledgeTestHistory testHistory = new KnowledgeTestHistory();
        testHistory.setId(testHistoryId);
        testHistory.setQuestion(testRecallDTO.getQueryText());
        testHistory.setKnowledgeBaseId(testRecallDTO.getKnowledgeBaseId());
        testHistory.setExecutionTimeMs(executionTimeMs);
        testHistory.setResultCount(resultCount);
        testHistory.setCreateUser(currentUserId);
        testHistory.setCreateTime(now);
        testHistory.setUpdateUser(currentUserId);
        testHistory.setUpdateTime(now);
        testHistory.setStatus(1);
        testHistory.setIsDeleted(0);
        testHistory.setTenantId(tenantId);

        return testHistory;
    }



    /**
     * 执行带分数的召回查询
     * 重新实现召回逻辑以保留分数信息，基于KnowledgeBaseServiceImpl.recallDocuments的逻辑
     */
    private List<AgentSearchResultVO> executeRecallWithScore(KnowledgeTestRecallDTO testRecallDTO) {
        String tenantId = SecureUtil.getTenantId();

        Long knowledgeBaseId = testRecallDTO.getKnowledgeBaseId();
        String queryText = testRecallDTO.getQueryText();

        if (knowledgeBaseId == null) {
            log.warn("召回请求中知识库ID为空，不执行召回操作。");
            return new ArrayList<>();
        }
        if (!StringUtils.hasText(queryText)) {
            log.warn("召回请求中问题文本为空，不执行召回操作。");
            return new ArrayList<>();
        }

        try {
            // 1. 检查知识库是否存在且属于当前租户
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
                log.warn("召回操作跳过无效知识库ID: {} (不存在、已删除或无权操作)", knowledgeBaseId);
                return new ArrayList<>();
            }

            // 2. 获取该知识库关联的向量模型名称
            AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
            if (aiModel == null || !ModelTypeEnum.EMBEDDING.getCode().equals(aiModel.getModelType())) {
                log.warn("召回操作跳过知识库ID: {}，因为未找到有效关联的向量嵌入模型或模型类型不匹配。", knowledgeBaseId);
                return new ArrayList<>();
            }
            String embedModelName = aiModel.getDisplayName();

            // 3. 获取问题文本的向量
            float[] queryVector = pythonEmbeddingService.vectorizeText(queryText, embedModelName);
            if (queryVector == null || queryVector.length == 0) {
                log.warn("知识库ID: {} 对应的模型 {} 向量化后的问题向量为空，跳过ES搜索。", knowledgeBaseId, embedModelName);
                return new ArrayList<>();
            }

            // 4. 调用ElasticSearch服务进行检索（支持混合检索）
            int topK = 5; // 默认返回5个结果
            List<AgentSearchResultVO> searchResults;

            // 根据配置决定使用混合检索还是纯向量检索
            if (hybridSearchConfig.getEnabled() && hybridSearchConfig.isValid()) {
                log.debug("使用混合检索 - 知识库: {}, 查询: '{}'", knowledgeBaseId, queryText);
                searchResults = elasticSearchService.searchHybrid(
                        knowledgeBase.getIndexName(),
                        queryText,
                        queryVector,
                        topK,
                        knowledgeBaseId,
                        tenantId,
                        hybridSearchConfig
                );
            } else {
                log.debug("使用纯向量检索 - 知识库: {}, 查询: '{}'", knowledgeBaseId, queryText);
                searchResults = elasticSearchService.searchVector(
                        knowledgeBase.getIndexName(),
                        queryVector,
                        topK,
                        knowledgeBaseId,
                        tenantId
                );
            }

            // 5. 使用Rerank服务对召回结果进行重排序
            if (!searchResults.isEmpty()) {
                try {
                    List<AgentSearchResultVO> rerankedResults = rerankAgentSearchResults(queryText, searchResults, knowledgeBaseId, knowledgeBase.getName());
                    log.info("知识库ID: {} 召回完成（含Rerank重排序），返回结果数量: {}", knowledgeBaseId, rerankedResults.size());
                    return rerankedResults;
                } catch (Exception e) {
                    log.warn("Rerank重排序失败，返回原始召回结果 - 知识库ID: {}, Error: {}", knowledgeBaseId, e.getMessage());
                }
            }

            log.info("知识库ID: {} 召回完成（原始排序），返回结果数量: {}", knowledgeBaseId, searchResults.size());
            return searchResults;

        } catch (Exception e) {
            log.error("执行带分数的召回查询失败，知识库ID: {}", knowledgeBaseId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 对AgentSearchResultVO列表进行rerank重排序
     * 优化版本，直接操作AgentSearchResultVO避免数据转换
     */
    private List<AgentSearchResultVO> rerankAgentSearchResults(String queryText, List<AgentSearchResultVO> searchResults,
                                                              Long knowledgeBaseId, String knowledgeBaseName) {
        try {
            // 转换为RecallDocSegment进行rerank
            List<RecallDocSegment> segments = searchResults.stream()
                    .map(result -> {
                        RecallDocSegment segment = new RecallDocSegment();
                        segment.setDocId(result.getDocumentId());
                        segment.setDocTitle(result.getDocumentName());
                        segment.setContent(result.getFragmentContent());
                        segment.setScore(result.getScore());
                        return segment;
                    })
                    .collect(java.util.stream.Collectors.toList());

            // 调用rerank服务
            List<RecallDocSegment> rerankedSegments = rerankService.rerankDocuments(queryText, segments, null);

            // 转换回AgentSearchResultVO
            return rerankedSegments.stream()
                    .map(segment -> {
                        AgentSearchResultVO result = new AgentSearchResultVO();
                        result.setKnowledgeBaseId(knowledgeBaseId);
                        result.setKnowledgeBaseName(knowledgeBaseName);
                        result.setDocumentId(segment.getDocId());
                        result.setDocumentName(segment.getDocTitle());
                        result.setFragmentContent(segment.getContent());
                        result.setScore(segment.getScore() != null ? segment.getScore() : 0.0f);
                        return result;
                    })
                    .collect(java.util.stream.Collectors.toList());

        } catch (Exception e) {
            log.warn("Rerank重排序失败，返回原始结果 - Error: {}", e.getMessage());
            return searchResults;
        }
    }

    /**
     * 保存搜索结果并转换为VO
     */
    private List<DatasetTestResultVO> saveAndConvertSearchResults(String testHistoryId,
                                                                 List<AgentSearchResultVO> searchResults) {
        String tenantId = SecureUtil.getTenantId();
        if (!StringUtils.hasText(tenantId)) {
            tenantId = "000000";
        }

        Long currentUserId = SecureUtil.getUserId();
        LocalDateTime now = LocalDateTime.now();

        List<DatasetTestResultVO> resultVOs = new ArrayList<>();

        for (AgentSearchResultVO searchResult : searchResults) {
            // 生成测试结果ID
            String testResultId = generateUUID32();

            // 创建测试结果实体（不存储知识库名称，遵循数据库范式）
            KnowledgeTestResult testResult = new KnowledgeTestResult();
            testResult.setId(testResultId);
            testResult.setTestHistoryId(testHistoryId);
            testResult.setContent(searchResult.getFragmentContent());
            testResult.setScore(BigDecimal.valueOf(searchResult.getScore()));
            testResult.setDocumentId(searchResult.getDocumentId());
            testResult.setDocumentName(searchResult.getDocumentName());
            testResult.setKnowledgeBaseId(searchResult.getKnowledgeBaseId());
            testResult.setCreateUser(currentUserId);
            testResult.setCreateTime(now);
            testResult.setUpdateUser(currentUserId);
            testResult.setUpdateTime(now);
            testResult.setStatus(1);
            testResult.setIsDeleted(0);
            testResult.setTenantId(tenantId);

            // 保存到数据库
            testResultMapper.insert(testResult);

            // 转换为VO
            DatasetTestResultVO resultVO = new DatasetTestResultVO();
            resultVO.setId(testResultId);
            resultVO.setContent(searchResult.getFragmentContent());
            resultVO.setScore((double) searchResult.getScore());
            resultVO.setDocumentId(String.valueOf(searchResult.getDocumentId()));
            resultVO.setDocumentName(searchResult.getDocumentName());

            resultVOs.add(resultVO);
        }

        return resultVOs;
    }

    /**
     * 根据测试历史ID获取测试详情
     * 包含通过关联查询获取的知识库名称等完整信息
     *
     * @param testHistoryId 测试历史记录ID
     * @return 测试历史项VO，包含完整的关联信息
     */
    @Override
    public DatasetTestHistoryItemVO getTestHistoryDetail(String testHistoryId) {
        // 1. 查询测试历史记录
        KnowledgeTestHistory testHistory = getById(testHistoryId);
        if (testHistory == null) {
            throw new RuntimeException("测试历史记录不存在: " + testHistoryId);
        }

        // 2. 查询测试结果列表
        List<KnowledgeTestResult> testResults = testResultMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeTestResult>()
                        .eq(KnowledgeTestResult::getTestHistoryId, testHistoryId)
                        .eq(KnowledgeTestResult::getIsDeleted, 0)
                        .orderByDesc(KnowledgeTestResult::getScore)
        );

        // 4. 转换为VO
        List<DatasetTestResultVO> testResultVOs = testResults.stream()
                .map(result -> {
                    DatasetTestResultVO resultVO = new DatasetTestResultVO();
                    resultVO.setId(result.getId());
                    resultVO.setContent(result.getContent());
                    resultVO.setScore(result.getScore().doubleValue());
                    resultVO.setDocumentId(String.valueOf(result.getDocumentId()));
                    resultVO.setDocumentName(result.getDocumentName());
                    return resultVO;
                })
                .collect(java.util.stream.Collectors.toList());

        // 5. 构建返回结果
        DatasetTestHistoryItemVO resultVO = new DatasetTestHistoryItemVO();
        resultVO.setId(testHistoryId);
        resultVO.setQuestion(testHistory.getQuestion());
        resultVO.setCreateTime(testHistory.getCreateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        resultVO.setSecond(testHistory.getExecutionTimeMs() / 1000.0);
        resultVO.setTestResult(testResultVOs);

        return resultVO;
    }

    /**
     * 分页查询知识库检索测试历史列表
     * 根据指定知识库ID查询测试历史，支持问题关键词筛选
     *
     * @param queryDTO 查询条件DTO，必须包含知识库ID
     * @return 分页的测试历史列表
     */
    @Override
    public IPage<KnowledgeTestHistoryListVO> getTestHistoryList(KnowledgeTestHistoryQueryDTO queryDTO) {
        String tenantId = SecureUtil.getTenantId();

        // 验证知识库ID是否为空
        if (queryDTO.getKnowledgeBaseId() == null) {
            throw new RuntimeException("知识库ID不能为空");
        }

        // 验证知识库是否存在且属于当前租户
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(queryDTO.getKnowledgeBaseId());
        if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
            throw new RuntimeException("知识库不存在或无权访问");
        }

        // 创建分页对象
        Page<KnowledgeTestHistoryListVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 调用Mapper进行分页查询
        return baseMapper.selectTestHistoryList(
                page,
                queryDTO.getKnowledgeBaseId(),
                queryDTO.getQuestionKeyword(),
                tenantId
        );
    }

    /**
     * 删除某个历史检索测试记录
     * 逻辑删除测试历史记录和相关的测试结果
     *
     * @param testHistoryId 测试历史记录ID
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTestHistory(String testHistoryId) {
        log.info("开始删除测试历史记录，测试ID: {}", testHistoryId);

        try {
            // 1. 检查测试历史记录是否存在
            KnowledgeTestHistory testHistory = getById(testHistoryId);
            if (testHistory == null) {
                log.warn("测试历史记录不存在: {}", testHistoryId);
                return false;
            }

            // 2. 检查权限（只能删除自己租户的记录）
            String tenantId = SecureUtil.getTenantId();
            if (!tenantId.equals(testHistory.getTenantId())) {
                log.warn("无权删除测试历史记录: {}，租户不匹配", testHistoryId);
                return false;
            }

            // 3. 逻辑删除测试历史记录
            testHistory.setIsDeleted(1);
            testHistory.setUpdateUser(SecureUtil.getUserId());
            testHistory.setUpdateTime(LocalDateTime.now());
            updateById(testHistory);

            // 4. 逻辑删除相关的测试结果
            testResultMapper.update(null,
                    new com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper<KnowledgeTestResult>()
                            .eq(KnowledgeTestResult::getTestHistoryId, testHistoryId)
                            .set(KnowledgeTestResult::getIsDeleted, 1)
                            .set(KnowledgeTestResult::getUpdateUser, SecureUtil.getUserId())
                            .set(KnowledgeTestResult::getUpdateTime, LocalDateTime.now())
            );

            log.info("成功删除测试历史记录: {}", testHistoryId);
            return true;

        } catch (Exception e) {
            log.error("删除测试历史记录失败，测试ID: {}", testHistoryId, e);
            throw new RuntimeException("删除测试历史记录失败: " + e.getMessage(), e);
        }
    }
}
