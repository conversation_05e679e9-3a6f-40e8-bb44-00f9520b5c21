
package com.chinaunicom.csf.seata.order;

import com.chinaunicom.csf.core.launch.constant.AppConstant;
import com.chinaunicom.csf.plugins.transaction.annotation.SeataCloudApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Order启动器
 */
@SeataCloudApplication
@EnableFeignClients(AppConstant.BASE_PACKAGES)
public class SeataOrderApplication {

	public static void main(String[] args) {
		SpringApplication.run(SeataOrderApplication.class, args);
	}

}

