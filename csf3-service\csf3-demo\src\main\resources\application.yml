#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:com/example/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.example.**.entity

#swagger扫描路径配置
swagger:
  base-packages:
    - org.springbalde
    - com.example

demo:
  name: demo

csf:
  data-security:
    audit:
      enable: true

debug: true

sms:
  name: cloopen
  endpoint: app.cloopen.com:8883
  access-key: xxxx
  secret-key: xxxx
  app-id: xxxx

#feign:
#  hystrix:
#    enabled: true
xxl:
  job:
    enable: true
    accessToken: default_token
    admin:
      addresses: http://localhost:8080/xxl-job-admin
    executor:
      address: ''
      appname: xxl-job-executor-sample
      ip: ''
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
      port: 9999