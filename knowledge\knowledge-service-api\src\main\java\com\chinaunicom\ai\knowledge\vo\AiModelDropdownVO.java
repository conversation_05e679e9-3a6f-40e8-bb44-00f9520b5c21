package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * AI模型下拉框VO
 */
@Data
@Schema(description = "AI模型下拉框VO")
public class AiModelDropdownVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "模型ID")
    private Long id;

    @Schema(description = "模型名称")
    private String displayName;
}