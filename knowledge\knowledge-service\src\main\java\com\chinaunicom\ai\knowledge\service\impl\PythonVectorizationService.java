package com.chinaunicom.ai.knowledge.service.impl;

import com.alibaba.fastjson2.JSON;
import com.chinaunicom.ai.knowledge.config.VectorizationConfig;
import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Python向量化服务调用
 */
@Slf4j
@Service
public class PythonVectorizationService {

    @Value("${python.vectorization.service.url}")
    private String pythonVectorizationServiceUrl; // Python服务的URL配置

    @Autowired
    private WebClient.Builder webClientBuilder; // 使用WebClient进行异步HTTP调用
    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;
    @Autowired
    private AiModelMapper aiModelMapper;
    @Autowired
    private KnowledgeBaseMapper knowledgeBaseMapper;
    @Autowired
    private VectorizationConfig vectorizationConfig;

    /**
     * 触发Python服务进行文档向量化
     * @param documentId 知识文档ID
     * @param knowledgeBaseId 知识库ID
     * @param fileObjectId MinIO文件对象ID
     * @param fileName 文件名
     */
    public void triggerVectorization(Long documentId, Long knowledgeBaseId, String fileObjectId, String fileName) {
        // 1. 更新文档状态为向量化中（使用重试机制）
        updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZING);

        // 2. 获取向量维度和ES索引名称
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
        if (knowledgeBase == null) {
            log.error("触发向量化失败：未找到知识库，ID: {}", knowledgeBaseId);
            updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZE_FAILED);
            return;
        }
        AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
        if (aiModel == null || aiModel.getDims() == null) {
            log.error("触发向量化失败：未找到关联模型或模型维度，知识库ID: {}", knowledgeBaseId);
            updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZE_FAILED);
            return;
        }
        String indexName = knowledgeBase.getIndexName(); // ES索引名称

        // 3. 构建请求体
        Map<String, Object> requestBody = new HashMap<>(); // 这个Map将作为List的元素
        requestBody.put("mysqlDocId", documentId);
        requestBody.put("minioId", fileObjectId);
        requestBody.put("embedModel", aiModel.getDisplayName());
        requestBody.put("fileName", fileName); // 此时 fileName 已经是 MinIO 上的带后缀文件名
        requestBody.put("indexName", indexName); // 将ES索引名称传递给Python服务，Python会从这里解析 tenantId 和 knowledgeBaseId
        // 以下两行移除，因为 Python 会从 indexName 中解析，不再显式传递到 requestBody 中
        // requestBody.put("knowledgeBaseId", knowledgeBaseId);
        // requestBody.put("tenantId", tenantId);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(requestBody);

        // 根据您最后的确认，这里使用 `body` 包装 `list`
        Map<String, Object> body = new HashMap<>();
        body.put("vecTar", list); // 包装 list 到 "vecTar" 键下

        log.info("Python向量化服务请求body: {}", JSON.toJSONString(body));

        // 4. 调用Python服务
        webClientBuilder.build()
                .post()
                .uri(pythonVectorizationServiceUrl + "/v1/parser/document_loader")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body) // 发送包装后的 body
                .retrieve()
                .bodyToMono(Map.class) // 假设Python服务成功返回JSON Map
                .subscribe(
                        response -> {
                            log.info("Python向量化服务响应: {}", response);
                            // 根据Python服务返回结果更新文档状态
                            // 假设Python服务成功返回 code: 200
                            try {
                                if (response != null && "200".equals(String.valueOf(response.get("code")))) {
                                    updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZED);
                                } else {
                                    log.warn("Python向量化服务返回非成功状态，文档ID: {}, 响应: {}", documentId, response);
                                    updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZE_FAILED);
                                }
                            } catch (Exception e) {
                                log.error("处理Python向量化服务响应时发生异常，文档ID: {}", documentId, e);
                                updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZE_FAILED);
                            }
                        },
                        error -> {
                            log.error("调用Python向量化服务失败，文档ID: {}", documentId, error);
                            try {
                                updateDocumentStatus(documentId, DocumentStatusEnum.VECTORIZE_FAILED);
                            } catch (Exception e) {
                                log.error("更新文档状态失败，文档ID: {}", documentId, e);
                            }
                        }
                );
    }

    /**
     * 更新文档状态（带重试机制）
     * @param documentId 文档ID
     * @param status 目标状态
     */
    private void updateDocumentStatus(Long documentId, DocumentStatusEnum status) {
        VectorizationConfig.RetryConfig retryConfig = vectorizationConfig.getRetry();
        int maxRetries = retryConfig.getMaxAttempts();
        int initialDelay = retryConfig.getInitialDelay();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                KnowledgeDocument document = new KnowledgeDocument();
                document.setId(documentId);
                document.setStatus(status.getCode());
                document.setUpdateTime(LocalDateTime.now());

                int updateCount = knowledgeDocumentMapper.updateById(document);
                if (updateCount > 0) {
                    log.debug("文档状态更新成功，文档ID: {}, 状态: {}, 尝试次数: {}",
                            documentId, status.getName(), attempt);
                    return; // 更新成功，退出重试循环
                } else {
                    log.warn("文档状态更新失败，影响行数为0，文档ID: {}, 状态: {}", documentId, status.getName());
                    return; // 没有找到记录，不需要重试
                }

            } catch (org.springframework.dao.CannotAcquireLockException e) {
                log.warn("文档状态更新遇到锁等待，文档ID: {}, 状态: {}, 尝试次数: {}/{}",
                        documentId, status.getName(), attempt, maxRetries);

                if (attempt == maxRetries) {
                    log.error("文档状态更新最终失败，已达到最大重试次数，文档ID: {}, 状态: {}",
                            documentId, status.getName(), e);
                    // 不抛出异常，避免影响其他文档的处理
                    return;
                }

                // 等待一段时间后重试，使用指数退避
                try {
                    long delay = Math.min(
                        (long)(initialDelay * Math.pow(retryConfig.getBackoffMultiplier(), attempt - 1)),
                        retryConfig.getMaxDelay()
                    );
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("重试等待被中断，文档ID: {}", documentId);
                    return;
                }

            } catch (Exception e) {
                log.error("文档状态更新发生未知异常，文档ID: {}, 状态: {}, 尝试次数: {}",
                        documentId, status.getName(), attempt, e);

                if (attempt == maxRetries) {
                    log.error("文档状态更新最终失败，文档ID: {}, 状态: {}", documentId, status.getName());
                    return;
                }

                // 对于其他异常也进行重试
                try {
                    long delay = Math.min(
                        (long)(initialDelay * Math.pow(retryConfig.getBackoffMultiplier(), attempt - 1)),
                        retryConfig.getMaxDelay()
                    );
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }
}