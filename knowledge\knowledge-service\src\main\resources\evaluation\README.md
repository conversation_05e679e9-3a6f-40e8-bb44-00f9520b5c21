# 知识库评测系统使用指南

## 📋 系统概述

基于1doc_QA数据集的知识库检索质量评测系统，通过ID匹配验证召回准确性，提供客观的检索质量评估。

## 🎯 核心功能

### 1. 数据导入
- 从`1doc_QA.json`导入评测数据
- 每个JSON元素作为一个独立文档
- 使用`news1`字段作为文档内容
- 建立原始ID与知识库文档ID的映射关系

### 2. 评测执行
- 使用`questions`字段作为查询问题
- 调用知识库检索服务获取召回结果
- 通过ID匹配验证召回准确性
- 计算召回率、精确率等关键指标

### 3. 结果分析
- 详细的测试历史记录
- 统计信息和评测报告
- 支持多次评测对比

## 🚀 快速开始

### 1. 数据库初始化

```sql
-- 执行SQL脚本创建表结构
source knowledge/knowledge-service/src/main/resources/sql/evaluation_tables.sql
```

### 2. 数据导入

```bash
# 导入全部数据到知识库ID=1
curl -X POST "http://localhost:10001/evaluation/import-dataset?knowledgeBaseId=1"

# 导入前100条数据
curl -X POST "http://localhost:10001/evaluation/import-dataset?knowledgeBaseId=1&maxCount=100"
```

### 3. 执行评测

```bash
# 对知识库ID=1执行评测
curl -X POST "http://localhost:10001/evaluation/run-evaluation?knowledgeBaseId=1"
```

### 4. 查看结果

```bash
# 获取评测统计信息
curl -X GET "http://localhost:10001/evaluation/statistics?knowledgeBaseId=1"

# 获取数据集信息
curl -X GET "http://localhost:10001/evaluation/dataset-info"
```

## 📊 评测指标

### 核心指标
- **召回率 (Recall Rate)**: 正确召回的问题数 / 总问题数
- **平均执行时间**: 单次检索的平均耗时
- **成功率**: 成功执行的测试数 / 总测试数

### 评测逻辑
1. 使用问题文本进行向量检索
2. 获取召回的文档片段列表
3. 通过知识库文档ID查找对应的原始文档ID
4. 检查召回结果中是否包含期望的原始文档ID
5. 如果包含则认为召回正确，否则为召回错误

## 🔧 技术架构

### 数据流程
```
1doc_QA.json → 解析JSON → 创建TXT文件 → 上传MinIO → 向量化 → 建立映射
```

### 评测流程
```
问题文本 → 向量检索 → 召回文档 → ID映射 → 准确性验证 → 记录结果
```

### 核心组件
- **EvaluationDataImporter**: 数据导入服务
- **EvaluationFileUploader**: 文件上传服务
- **EvaluationTestService**: 评测执行服务
- **EvaluationController**: API控制器

## 📁 数据结构

### 输入数据格式 (1doc_QA.json)
```json
{
    "id": "64fa9b27b82641eb8ecbe14c",
    "event": "事件描述",
    "news1": "新闻正文内容（作为文档内容）",
    "questions": "评测问题文本",
    "answers": "标准答案"
}
```

### 评测结果格式
```json
{
    "knowledgeBaseId": 1,
    "totalQuestions": 100,
    "correctRecalls": 85,
    "recallRate": 0.85,
    "avgExecutionTime": 245.6,
    "testResults": [...]
}
```

## ⚙️ 配置说明

### 必要配置
- MinIO连接配置
- MySQL数据库配置
- Python向量化服务配置
- Elasticsearch配置

### 可选配置
- 批量处理大小
- 检索结果数量限制
- 超时时间设置

## 🔍 故障排查

### 常见问题

1. **数据导入失败**
   - 检查1doc_QA.json文件是否存在
   - 确认知识库ID是否有效
   - 查看MinIO连接状态

2. **向量化失败**
   - 检查Python向量化服务状态
   - 确认文件已正确上传到MinIO
   - 查看向量化服务日志

3. **评测结果异常**
   - 确认文档已完成向量化
   - 检查Elasticsearch索引状态
   - 验证ID映射关系

### 日志查看
```bash
# 查看应用日志
tail -f logs/knowledge-service.log | grep "evaluation"

# 查看特定组件日志
tail -f logs/knowledge-service.log | grep "EvaluationTestService"
```

## 📈 性能优化

### 建议配置
- 批量导入大小: 50-100条
- 检索超时时间: 30秒
- 数据库连接池: 10-20个连接

### 监控指标
- 导入成功率
- 向量化完成率
- 平均检索时间
- 系统资源使用率

## 🔄 版本更新

### v1.0.0 (当前版本)
- 基础评测功能
- 数据导入和检索测试
- 统计报告生成

### 后续计划
- 支持多种评测数据集
- 增加更多评测指标
- 可视化报告界面
- 自动化评测调度
