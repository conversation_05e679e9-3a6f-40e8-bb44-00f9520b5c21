<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>csf3-service</artifactId>
        <groupId>com.chinaunicom.csf</groupId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3-demo</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-demo-api</artifactId>
            <version>1.8.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-audit-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-desk-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-dict-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-plugins-datasecurity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-plugins-sms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cloopen</groupId>
            <artifactId>java-sms-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-plugins-xxljob</artifactId>
        </dependency>
    </dependencies>

</project>
