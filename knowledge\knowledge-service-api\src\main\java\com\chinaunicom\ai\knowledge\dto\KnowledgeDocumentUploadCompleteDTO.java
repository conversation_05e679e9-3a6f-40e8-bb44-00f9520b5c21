package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 文档上传完成请求DTO
 */
@Data
@Schema(description = "文档上传完成请求DTO")
public class KnowledgeDocumentUploadCompleteDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "上传ID不能为空")
    @Schema(description = "MinIO分片上传ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "someUploadId")
    private String uploadId;

    @NotNull(message = "文档ID不能为空")
    @Schema(description = "知识文档ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long documentId;
}