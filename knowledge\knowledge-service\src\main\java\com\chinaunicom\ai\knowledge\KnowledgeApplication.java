package com.chinaunicom.ai.knowledge;

import com.chinaunicom.csf.core.cloud.client.CsfCloudApplication;
import com.chinaunicom.csf.core.launch.CsfApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Demo启动器
 *
 * <AUTHOR>
 */
@EnableScheduling
@EnableFeignClients({"com.chinaunicom.csf","com.chinaunicom.ai.knowledge.feign"})
@CsfCloudApplication(scanBasePackages = {"com.chinaunicom.csf","com.chinaunicom.ai.knowledge"})
public class KnowledgeApplication {

    public static void main(String[] args) {
        SpringApplication.run(KnowledgeApplication.class, args);
    }

}

