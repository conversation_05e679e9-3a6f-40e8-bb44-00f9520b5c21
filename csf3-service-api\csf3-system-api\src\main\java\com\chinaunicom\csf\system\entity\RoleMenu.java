
package com.chinaunicom.csf.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinaunicom.csf.core.mp.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 实体类
 */
@Data
@TableName("csf_role_menu")
@Schema(description = "RoleMenu对象")
public class RoleMenu extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 菜单id
	 */
	@Schema(description = "菜单id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long menuId;

	/**
	 * 角色id
	 */
	@Schema(description = "角色id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long roleId;


}
