package com.chinaunicom.ai.knowledge.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * 向量搜索配置类
 * 用于配置向量搜索的相关参数
 * 支持Nacos动态配置刷新
 */
@Slf4j
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "vector.search")
public class VectorSearchConfig {

    /**
     * 相似度阈值，低于此阈值的结果将被过滤
     * 默认值：0.75
     * 取值范围：0.0 - 1.0
     * 
     * 建议值：
     * - 0.85+：高精度，可能召回结果较少
     * - 0.75-0.85：平衡精度和召回率
     * - 0.65-0.75：较宽松，召回更多结果
     */
    private double similarityThreshold = 0.75;

    /**
     * 是否启用相似度阈值过滤
     * 默认值：true
     */
    private boolean enableSimilarityFilter = true;

    /**
     * 是否启用调试日志
     * 默认值：false
     */
    private boolean enableDebugLog = false;

    /**
     * 最大召回结果数量
     * 默认值：10
     */
    private int maxResults = 10;

    /**
     * 配置初始化后的回调
     * 验证配置参数并记录配置信息
     */
    @PostConstruct
    public void init() {
        validate();
        logConfigInfo("配置初始化");
    }

    /**
     * 验证配置参数的有效性
     */
    public void validate() {
        if (similarityThreshold < 0.0 || similarityThreshold > 1.0) {
            throw new IllegalArgumentException("相似度阈值必须在0.0到1.0之间，当前值: " + similarityThreshold);
        }
        if (maxResults <= 0) {
            throw new IllegalArgumentException("最大结果数量必须大于0，当前值: " + maxResults);
        }
    }

    /**
     * 记录当前配置信息
     * 用于配置刷新时的日志输出
     */
    public void logConfigInfo(String action) {
        log.info("🔧 向量搜索配置{} - 相似度阈值: {}, 启用过滤: {}, 调试日志: {}, 最大结果数: {}",
            action, similarityThreshold, enableSimilarityFilter, enableDebugLog, maxResults);
    }
}
