package com.chinaunicom.ai.knowledge.evaluation.aspect;

import com.chinaunicom.ai.knowledge.evaluation.config.EvaluationSwitchConfig;
import com.chinaunicom.csf.core.tool.api.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 评测功能开关AOP切面
 * 
 * 拦截EvaluationController的所有方法调用
 * 当功能开关关闭时，统一返回错误响应并记录日志
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class EvaluationSwitchAspect {

    private final EvaluationSwitchConfig switchConfig;

    /**
     * 定义切点：拦截EvaluationController中的所有公共方法
     */
    @Pointcut("execution(public * com.chinaunicom.ai.knowledge.evaluation.controller.EvaluationController.*(..))")
    public void evaluationControllerMethods() {
    }

    /**
     * 环绕通知：在方法执行前检查功能开关状态
     * 
     * @param joinPoint 连接点
     * @return 方法执行结果或错误响应
     * @throws Throwable 方法执行异常
     */
    @Around("evaluationControllerMethods()")
    public Object checkEvaluationSwitch(ProceedingJoinPoint joinPoint) throws Throwable {
        
        // 检查功能开关状态
        if (!switchConfig.isEvaluationEnabled()) {
            return handleDisabledAccess(joinPoint);
        }

        // 功能开启，正常执行方法
        try {
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("评测接口执行异常: {}.{}", 
                    joinPoint.getTarget().getClass().getSimpleName(),
                    joinPoint.getSignature().getName(), e);
            throw e;
        }
    }

    /**
     * 处理功能关闭时的访问请求
     * 
     * @param joinPoint 连接点
     * @return 统一的错误响应
     */
    private Object handleDisabledAccess(ProceedingJoinPoint joinPoint) {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        // 记录访问日志
        if (switchConfig.isLogDisabledAccess()) {
            logDisabledAccess(className, methodName);
        }

        // 设置HTTP响应状态码
        setHttpResponseStatus();

        // 返回统一错误响应
        return createDisabledResponse();
    }

    /**
     * 记录功能关闭时的访问日志
     * 
     * @param className 类名
     * @param methodName 方法名
     */
    private void logDisabledAccess(String className, String methodName) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String requestUri = request.getRequestURI();
                String method = request.getMethod();
                String remoteAddr = getClientIpAddress(request);
                String userAgent = request.getHeader("User-Agent");
                
                log.warn("🚫 评测功能已关闭，拒绝访问 - 接口: {}.{}, 请求: {} {}, 客户端: {}, UA: {}", 
                        className, methodName, method, requestUri, remoteAddr, 
                        userAgent != null ? userAgent.substring(0, Math.min(userAgent.length(), 50)) : "Unknown");
            } else {
                log.warn("🚫 评测功能已关闭，拒绝访问 - 接口: {}.{}", className, methodName);
            }
        } catch (Exception e) {
            log.warn("记录访问日志失败: {}", e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP", "WL-Proxy-Client-IP"};
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 设置HTTP响应状态码
     */
    private void setHttpResponseStatus() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletResponse response = attributes.getResponse();
                if (response != null) {
                    response.setStatus(switchConfig.getDisabledHttpStatus());
                }
            }
        } catch (Exception e) {
            log.warn("设置HTTP状态码失败: {}", e.getMessage());
        }
    }

    /**
     * 创建功能关闭时的统一错误响应
     *
     * @return 错误响应对象
     */
    private R<Map<String, Object>> createDisabledResponse() {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("errorCode", switchConfig.getDisabledErrorCode());
        errorData.put("message", switchConfig.getFullDisabledMessage());
        errorData.put("httpStatus", switchConfig.getDisabledHttpStatus());
        errorData.put("timestamp", System.currentTimeMillis());
        errorData.put("service", "evaluation-service");

        // 添加建议信息
        Map<String, Object> suggestion = new HashMap<>();
        suggestion.put("action", "请稍后重试或联系系统管理员");
        suggestion.put("contact", "如需紧急使用评测功能，请联系技术支持");
        errorData.put("suggestion", suggestion);

        // 使用R.fail(String)方法，并将详细信息放在data中
        R<Map<String, Object>> response = R.fail(switchConfig.getDisabledMessage());
        response.setData(errorData);
        response.setCode(switchConfig.getDisabledHttpStatus());

        return response;
    }
}
