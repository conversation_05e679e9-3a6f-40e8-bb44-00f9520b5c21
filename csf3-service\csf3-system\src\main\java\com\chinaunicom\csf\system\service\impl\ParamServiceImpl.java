
package com.chinaunicom.csf.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.system.entity.Param;
import com.chinaunicom.csf.system.mapper.ParamMapper;
import com.chinaunicom.csf.system.service.IParamService;
import com.chinaunicom.csf.system.vo.ParamVO;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 */
@Service
public class ParamServiceImpl extends BaseServiceImpl<ParamMapper, Param> implements IParamService {

	@Override
	public IPage<ParamVO> selectParamPage(IPage<ParamVO> page, ParamVO param) {
		return page.setRecords(baseMapper.selectParamPage(page, param));
	}

}
