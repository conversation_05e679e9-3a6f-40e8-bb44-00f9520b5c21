package com.chinaunicom.ai.knowledge.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.ai.knowledge.entity.KnowledgeTestHistory;
import com.chinaunicom.ai.knowledge.vo.KnowledgeTestHistoryListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 知识库检索测试历史Mapper接口
 *
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Mapper
public interface KnowledgeTestHistoryMapper extends BaseMapper<KnowledgeTestHistory> {

    /**
     * 分页查询知识库检索测试历史列表
     * 包含关联查询知识库名称
     *
     * @param page 分页参数
     * @param knowledgeBaseId 知识库ID（可选）
     * @param questionKeyword 问题关键词（可选）
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<KnowledgeTestHistoryListVO> selectTestHistoryList(
            Page<KnowledgeTestHistoryListVO> page,
            @Param("knowledgeBaseId") Long knowledgeBaseId,
            @Param("questionKeyword") String questionKeyword,
            @Param("tenantId") String tenantId
    );
}
