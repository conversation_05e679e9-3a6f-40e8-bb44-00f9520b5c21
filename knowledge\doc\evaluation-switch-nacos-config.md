# 评测功能开关Nacos配置指南

## 概述

本文档说明如何在Nacos配置中心配置评测功能开关，实现对EvaluationController所有接口的统一开关控制。

## Nacos配置信息

### 配置位置
- **Nacos地址**: `10.81.9.154:8848`
- **命名空间**: `agent-factory-dev`
- **Data ID**: `knowledge-service.yml`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: `YAML`

### 配置内容

在Nacos配置中心的`knowledge-service.yml`配置文件中添加以下内容：

```yaml
# 评测功能开关配置
evaluation:
  controller:
    # 评测功能开关状态（true=开启，false=关闭）
    enabled: true
    # 功能关闭时的HTTP状态码
    disabled-http-status: 503
    # 功能关闭时的错误码
    disabled-error-code: "EVALUATION_DISABLED"
    # 功能关闭时的错误信息
    disabled-message: "评测功能暂时关闭，请稍后再试"
    # 是否记录功能关闭时的访问日志
    log-disabled-access: true
    # 功能关闭时的详细说明信息
    disabled-detail: "系统维护中，评测相关功能暂时不可用"
```

## 配置参数说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `evaluation.controller.enabled` | boolean | true | 评测功能开关状态 |
| `evaluation.controller.disabled-http-status` | int | 503 | 功能关闭时的HTTP状态码 |
| `evaluation.controller.disabled-error-code` | string | "EVALUATION_DISABLED" | 功能关闭时的错误码 |
| `evaluation.controller.disabled-message` | string | "评测功能暂时关闭，请稍后再试" | 功能关闭时的错误信息 |
| `evaluation.controller.log-disabled-access` | boolean | true | 是否记录功能关闭时的访问日志 |
| `evaluation.controller.disabled-detail` | string | "系统维护中，评测相关功能暂时不可用" | 功能关闭时的详细说明 |

## 使用场景

### 1. 系统维护
```yaml
evaluation:
  controller:
    enabled: false
    disabled-message: "系统正在维护中，评测功能暂时不可用"
    disabled-detail: "预计维护时间：2小时，请稍后再试"
```

### 2. 紧急关闭
```yaml
evaluation:
  controller:
    enabled: false
    disabled-http-status: 503
    disabled-message: "评测服务暂时不可用"
    disabled-detail: "服务异常，正在紧急修复中"
```

### 3. 功能测试
```yaml
evaluation:
  controller:
    enabled: false
    disabled-message: "评测功能正在测试中"
    disabled-detail: "功能升级测试，预计1小时后恢复"
    log-disabled-access: true
```

## 配置操作步骤

### 1. 登录Nacos控制台
访问：`http://10.81.9.154:8848/nacos`
- 用户名：`nacos`
- 密码：`Cyhlw@nacos2023`

### 2. 切换命名空间
选择命名空间：`agent-factory-dev`

### 3. 编辑配置
1. 在配置管理页面找到 `knowledge-service.yml`
2. 点击"编辑"按钮
3. 在配置内容中添加或修改评测开关配置
4. 点击"发布"按钮

### 4. 验证配置生效
配置发布后，由于使用了`@RefreshScope`注解，配置会自动刷新，无需重启服务。

可以通过以下方式验证：
1. 查看应用日志，确认配置刷新日志
2. 调用评测接口，验证开关效果

## 配置热更新

### 自动刷新
- 使用`@RefreshScope`注解，配置变更会自动生效
- 无需重启服务
- 配置变更后会自动记录日志

### 刷新确认
配置变更后，应用会输出类似日志：
```
INFO  - 评测功能开关配置 - 配置刷新: enabled=false, httpStatus=503, errorCode=EVALUATION_DISABLED, message=评测功能暂时关闭，请稍后再试
WARN  - ⚠️ 评测功能已被关闭！所有评测接口将拒绝访问
```

## 错误响应格式

当功能开关关闭时，所有评测接口将返回统一格式的错误响应：

```json
{
  "code": 503,
  "success": false,
  "data": {
    "errorCode": "EVALUATION_DISABLED",
    "message": "评测功能暂时关闭，请稍后再试。系统维护中，评测相关功能暂时不可用",
    "httpStatus": 503,
    "timestamp": 1704614400000,
    "service": "evaluation-service",
    "suggestion": {
      "action": "请稍后重试或联系系统管理员",
      "contact": "如需紧急使用评测功能，请联系技术支持"
    }
  },
  "msg": "评测功能暂时关闭，请稍后再试"
}
```

## 注意事项

1. **生产环境配置**：建议生产环境默认关闭评测功能，需要时手动开启
2. **配置验证**：配置参数会自动验证，无效值会使用默认值
3. **日志记录**：功能关闭时的访问会被记录，便于监控和分析
4. **权限控制**：Nacos配置修改需要相应权限，请确保操作人员有权限
5. **配置备份**：重要配置变更前建议备份原配置

## 监控建议

1. **配置变更监控**：监控Nacos配置变更记录
2. **访问日志监控**：关注功能关闭时的访问日志
3. **错误率监控**：监控503错误的增长情况
4. **业务影响评估**：评估功能关闭对业务的影响范围
