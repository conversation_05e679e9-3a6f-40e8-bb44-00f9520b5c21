package com.chinaunicom.csf.system.vo;

import com.chinaunicom.csf.system.entity.ApiScope;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "ApiScopeVO对象")
public class ApiScopeVO extends ApiScope {
	private static final long serialVersionUID = 1L;

	/**
	 * 规则类型名
	 */
	private String scopeTypeName;
}
