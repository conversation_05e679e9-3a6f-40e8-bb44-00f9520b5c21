<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.chinaunicom.csf</groupId>
        <artifactId>csf3-ops</artifactId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3-develop</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <!--Csf-->
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-plugins-develop</artifactId>
        </dependency>
        <!--Swagger-->

        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-dict-api</artifactId>
        </dependency>
    </dependencies>

</project>
