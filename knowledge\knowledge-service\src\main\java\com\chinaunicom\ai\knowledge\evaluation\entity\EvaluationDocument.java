package com.chinaunicom.ai.knowledge.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评测文档实体类
 * 用于存储从1doc_QA.json导入的文档信息
 */
@Data
@TableName("evaluation_document")
public class EvaluationDocument {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 原始文档ID（来自JSON中的id字段）
     */
    private String originalDocId;
    
    /**
     * 知识库文档ID（上传到知识库后的内部ID）
     */
    private Long knowledgeDocId;
    
    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;
    
    /**
     * 事件描述
     */
    private String event;
    
    /**
     * 新闻内容（用作文档内容）
     */
    private String news1;
    
    /**
     * 评测问题
     */
    private String questions;
    
    /**
     * 标准答案
     */
    private String answers;
    
    /**
     * 上传的文件名
     */
    private String fileName;
    
    /**
     * 文件对象ID（MinIO中的对象ID）
     */
    private String fileObjectId;
    
    /**
     * 文档状态：PENDING-待处理, UPLOADED-已上传, VECTORIZED-已向量化, FAILED-失败
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
