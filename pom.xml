<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.chinaunicom.csf</groupId>
        <artifactId>csf3-starter-parent</artifactId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3</artifactId>
    <packaging>pom</packaging>
    <version>1.8.0-SNAPSHOT</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.forceJavacCompilerUse>true</maven.compiler.forceJavacCompilerUse>
    </properties>

    <modules>
        <module>csf3-common</module>
        <module>csf3-ops</module>
        <module>csf3-auth</module>
        <module>csf3-gateway</module>
        <module>csf3-service</module>
        <module>csf3-service-api</module>
        <module>knowledge</module>
      <module>csf3-file</module>
  </modules>

    <dependencyManagement>
        <dependencies>
            <!--csf-sb3 api-->
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-system-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-desk-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-scope-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-user-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-audit-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-dict-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinaunicom.csf</groupId>
                <artifactId>csf3-common</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>
