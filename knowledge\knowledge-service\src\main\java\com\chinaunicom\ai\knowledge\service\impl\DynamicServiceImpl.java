package com.chinaunicom.ai.knowledge.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinaunicom.ai.knowledge.entity.Notice;
import com.chinaunicom.ai.knowledge.mapper.NoticeMapper;
import com.chinaunicom.ai.knowledge.service.DynamicService;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DynamicServiceImpl
 *
 * <AUTHOR>
 */
@Service
public class DynamicServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements DynamicService {

	@Override
	public List<Notice> masterList() {
		return this.list();
	}

	@Override
	@DS("slave")
	public List<Notice> slaveList() {
		return this.list();
	}
}
