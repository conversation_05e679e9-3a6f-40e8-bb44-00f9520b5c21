package com.chinaunicom.ai.knowledge.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Rerank接口请求DTO
 * 用于调用算法端的rerank接口
 */
@Data
@Schema(description = "Rerank接口请求DTO")
public class RerankRequestDTO {
    
    @Schema(description = "查询语句", required = true, example = "什么是机器学习？")
    private String query;
    
    @Schema(description = "被查询的文本列表", required = true)
    private List<String> texts;
    
    @Schema(description = "rerank的model id", required = true, example = "bce-reranker-base_v1")
    @JsonProperty("model_name")
    private String modelName;

    @Schema(description = "批处理参数，默认32", example = "32")
    @JsonProperty("batch_size")
    private Integer batchSize = 32;
}
