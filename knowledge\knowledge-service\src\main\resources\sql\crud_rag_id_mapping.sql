-- CRUD_RAG文档ID映射表
-- 用于建立CRUD_RAG原始文档ID与CSF3系统内部文档ID的映射关系

DROP TABLE IF EXISTS `crud_rag_id_mapping`;

CREATE TABLE `crud_rag_id_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `crud_rag_doc_id` varchar(100) NOT NULL COMMENT 'CRUD_RAG原始文档ID',
  `our_doc_id` bigint(20) NOT NULL COMMENT 'CSF3系统内部文档ID',
  `knowledge_base_id` bigint(20) NOT NULL DEFAULT 999 COMMENT '知识库ID',
  `document_name` varchar(255) DEFAULT NULL COMMENT '文档名称',
  `content_summary` varchar(200) DEFAULT NULL COMMENT '内容摘要',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint(20) DEFAULT 1 COMMENT '创建用户',
  `update_user` bigint(20) DEFAULT 1 COMMENT '更新用户',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crud_rag_doc_id` (`crud_rag_doc_id`),
  UNIQUE KEY `uk_our_doc_id` (`our_doc_id`),
  KEY `idx_kb_id` (`knowledge_base_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CRUD_RAG文档ID映射表';

-- 插入测试数据
INSERT INTO `crud_rag_id_mapping` (
  `crud_rag_doc_id`, `our_doc_id`, `knowledge_base_id`,
  `document_name`, `content_summary`, `remark`
) VALUES
('news_001', 12345, 999, 'news_001.txt', '人工智能发展新闻...', '测试数据'),
('news_002', 12346, 999, 'news_002.txt', '深度学习技术突破...', '测试数据'),
('news_003', 12347, 999, 'news_003.txt', '自然语言处理发展...', '测试数据');

-- 常用查询示例
-- 1. 查看映射统计
SELECT
    knowledge_base_id,
    COUNT(1) as total_count,
    SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count
FROM crud_rag_id_mapping
GROUP BY knowledge_base_id;

-- 2. 检查重复映射
SELECT crud_rag_doc_id, COUNT(1) as count
FROM crud_rag_id_mapping
WHERE status = 'ACTIVE'
GROUP BY crud_rag_doc_id
HAVING COUNT(1) > 1;
