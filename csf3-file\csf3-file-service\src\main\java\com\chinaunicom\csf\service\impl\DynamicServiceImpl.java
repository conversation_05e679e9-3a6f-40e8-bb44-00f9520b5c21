package com.chinaunicom.csf.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinaunicom.csf.entity.Notice;
import com.chinaunicom.csf.mapper.NoticeMapper;
import com.chinaunicom.csf.service.DynamicService;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * DynamicServiceImpl
 *
 * <AUTHOR>
 */
@Service
public class DynamicServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements DynamicService {

	@Override
	public List<Notice> masterList() {
		return this.list();
	}

	@Override
	@DS("slave")
	public List<Notice> slaveList() {
		return this.list();
	}
}
