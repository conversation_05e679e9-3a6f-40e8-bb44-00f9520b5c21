
package com.chinaunicom.csf.system.user.feign;

import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.ObjectUtil;
import com.chinaunicom.csf.system.user.entity.User;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.chinaunicom.csf.system.user.entity.UserOauth;
import com.chinaunicom.csf.system.user.service.IUserService;
import com.chinaunicom.csf.system.user.vo.UserVO;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 用户服务Feign实现类
 *
 */
@RestController
@AllArgsConstructor
@Hidden
public class UserClient implements IUserClient {

	private IUserService service;

	@Override
	public R<UserInfo> userInfo(Long userId) {
		return R.data(service.userInfo(userId));
	}

	@Override
	@GetMapping(API_PREFIX + "/user-info")
	public R<UserInfo> userInfo(String tenantId, String account, String password) {
		return R.data(service.userInfo(tenantId, account, password));
	}

	@Override
	@GetMapping(API_PREFIX + "/user-detail")
	public String getUserSalt(String tenantId, String account) {
		User user = new User();
		user.setTenantId(tenantId);
		user.setAccount(account);
		UserVO userVO = service.getDetail(user);
		String salt = ObjectUtil.isEmpty(userVO.getSalt()) ? "" : userVO.getSalt();
		return salt;
	}

	@Override
	@PostMapping(API_PREFIX + "/user-auth-info")
	public R<UserInfo> userAuthInfo(UserOauth userOauth) {
		return R.data(service.userInfo(userOauth));
	}

	@Override
	@PostMapping(API_PREFIX + "/save-user")
	public R<Boolean> saveUser(User user) {
		return R.data(service.save(user));
	}

}
