
package com.chinaunicom.csf.system.user.feign;

import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.system.user.entity.User;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.chinaunicom.csf.system.user.entity.UserOauth;
import com.chinaunicom.csf.system.user.vo.UserVO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Feign失败配置
 *
 */
@Component
public class IUserClientFallback implements IUserClient {

	@Override
	public R<UserInfo> userInfo(Long userId) {
		return R.fail("未获取到账号信息");
	}

	@Override
	public R<UserInfo> userInfo(String tenantId, String account, String password) {
		return R.fail("未获取到账号信息");
	}

	@GetMapping(API_PREFIX + "/user-detail")
	@Override
	public String getUserSalt(String tenantId, String account) {
		return "";
	}

	@Override
	public R<UserInfo> userAuthInfo(UserOauth userOauth) {
		return R.fail("未获取到账号信息");
	}

	@Override
	public R<Boolean> saveUser(User user) {
		return R.fail("创建用户失败");
	}
}
