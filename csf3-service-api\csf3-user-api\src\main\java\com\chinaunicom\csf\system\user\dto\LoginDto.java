package com.chinaunicom.csf.system.user.dto;

import com.chinaunicom.csf.plugins.datasecurity.annotations.FieldDecrypt;
import com.chinaunicom.csf.plugins.datasecurity.enums.AlgorithmEnum;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/6
 */
@Data
public class LoginDto implements Serializable {
	private static final long serialVersionUID = 1L;

	@Parameter(description = "授权类型", required = true)
	private String grantType;

	@Parameter(description = "刷新令牌")
	private String refreshToken;

	@Parameter(description = "租户ID", required = true)
	private String tenantId;

	@Parameter(description = "账号", required = true)
	private String account;

	@Parameter(description = "密码", required = true)
	@FieldDecrypt(algorithm = AlgorithmEnum.RSA)
	private String password;
}
