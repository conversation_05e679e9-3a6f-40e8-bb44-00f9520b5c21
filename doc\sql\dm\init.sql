# since v1.4.0

CREATE TABLE "CSF"."csf_attachment"
(
    "id"            BIGINT                                   NOT NULL,
    "tenant_id"     VARCHAR(12)  DEFAULT '000000',
    "name"          VARCHAR(64)  DEFAULT ''                  NOT NULL,
    "original_name" VARCHAR(64)  DEFAULT ''                  NOT NULL,
    "description"   VARCHAR(64)  DEFAULT ''                  NOT NULL,
    "host"          VARCHAR(512) DEFAULT ''                  NOT NULL,
    "url"           VARCHAR(512) DEFAULT ''                  NOT NULL,
    "link"          VARCHAR(512) DEFAULT ''                  NOT NULL,
    "status"        TINYINT      DEFAULT 0                   NOT NULL,
    "is_deleted"    TINYINT      DEFAULT 0                   NOT NULL,
    "create_time"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "create_user"   VARCHAR(32)  DEFAULT ''                  NOT NULL,
    "update_time"   TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP() NOT NULL,
    "update_user"   VARCHAR(32)  DEFAULT ''                  NOT NULL,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON COLUMN "CSF"."csf_attachment"."is_deleted" IS '是否已逻辑删除：0未删除、1已删除';
COMMENT ON COLUMN "CSF"."csf_attachment"."link" IS '文件地址';
COMMENT ON COLUMN "CSF"."csf_attachment"."tenant_id" IS '租户ID';

CREATE TABLE "CSF"."csf_client"
(
    "id"                      BIGINT       NOT NULL,
    "client_id"               VARCHAR(48)  NOT NULL,
    "client_secret"           VARCHAR(256) NOT NULL,
    "resource_ids"            VARCHAR(256),
    "scope"                   VARCHAR(256) NOT NULL,
    "authorized_grant_types"  VARCHAR(256) NOT NULL,
    "web_server_redirect_uri" VARCHAR(256),
    "authorities"             VARCHAR(256),
    "access_token_validity"   INT          NOT NULL,
    "refresh_token_validity"  INT          NOT NULL,
    "additional_information"  VARCHAR(4096),
    "autoapprove"             VARCHAR(256),
    "create_user"             BIGINT,
    "create_dept"             BIGINT,
    "create_time"             TIMESTAMP(0),
    "update_user"             BIGINT,
    "update_time"             TIMESTAMP(0),
    "status"                  INT          NOT NULL,
    "is_deleted"              INT          NOT NULL,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_client" IS '客户端表';
COMMENT ON COLUMN "CSF"."csf_client"."access_token_validity" IS '令牌过期秒数';
COMMENT ON COLUMN "CSF"."csf_client"."additional_information" IS '附加说明';
COMMENT ON COLUMN "CSF"."csf_client"."authorities" IS '权限';
COMMENT ON COLUMN "CSF"."csf_client"."authorized_grant_types" IS '授权类型';
COMMENT ON COLUMN "CSF"."csf_client"."autoapprove" IS '自动授权';
COMMENT ON COLUMN "CSF"."csf_client"."client_id" IS '客户端id';
COMMENT ON COLUMN "CSF"."csf_client"."client_secret" IS '客户端密钥';
COMMENT ON COLUMN "CSF"."csf_client"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_client"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_client"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_client"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_client"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_client"."refresh_token_validity" IS '刷新令牌过期秒数';
COMMENT ON COLUMN "CSF"."csf_client"."resource_ids" IS '资源集合';
COMMENT ON COLUMN "CSF"."csf_client"."scope" IS '授权范围';
COMMENT ON COLUMN "CSF"."csf_client"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_client"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_client"."update_user" IS '修改人';
COMMENT ON COLUMN "CSF"."csf_client"."web_server_redirect_uri" IS '回调地址';

insert into "CSF"."csf_client" ("id", "client_id", "client_secret", "resource_ids", "scope", "authorized_grant_types",
                                "web_server_redirect_uri", "authorities", "access_token_validity",
                                "refresh_token_validity", "additional_information", "autoapprove", "create_user",
                                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598811738675202, 'csf-v', 'csf-v_secret', null, 'all', 'refresh_token,password,authorization_code',
        'http://localhost:8080', null, 86400, 604800, null, null, 1, 1123598813738675201, '2019-03-24 10:42:29', 1,
        '2019-03-24 10:42:32', 1, 0);

CREATE TABLE "CSF"."csf_code"
(
    "id"            BIGINT NOT NULL,
    "datasource_id" BIGINT,
    "service_name"  VARCHAR(64),
    "code_name"     VARCHAR(64),
    "table_name"    VARCHAR(64),
    "table_prefix"  VARCHAR(64),
    "pk_name"       VARCHAR(32),
    "package_name"  VARCHAR(500),
    "base_mode"     INT,
    "wrap_mode"     INT,
    "api_path"      VARCHAR(2000),
    "web_path"      VARCHAR(2000),
    "is_deleted"    INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_code" IS '代码生成表';
COMMENT ON COLUMN "CSF"."csf_code"."api_path" IS '后端路径';
COMMENT ON COLUMN "CSF"."csf_code"."base_mode" IS '基础业务模式';
COMMENT ON COLUMN "CSF"."csf_code"."code_name" IS '模块名称';
COMMENT ON COLUMN "CSF"."csf_code"."datasource_id" IS '数据源主键';
COMMENT ON COLUMN "CSF"."csf_code"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_code"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_code"."package_name" IS '后端包名';
COMMENT ON COLUMN "CSF"."csf_code"."pk_name" IS '主键名';
COMMENT ON COLUMN "CSF"."csf_code"."service_name" IS '服务名称';
COMMENT ON COLUMN "CSF"."csf_code"."table_name" IS '表名';
COMMENT ON COLUMN "CSF"."csf_code"."table_prefix" IS '表前缀';
COMMENT ON COLUMN "CSF"."csf_code"."web_path" IS '前端路径';
COMMENT ON COLUMN "CSF"."csf_code"."wrap_mode" IS '包装器模式';

insert into "CSF"."csf_code" ("id", "datasource_id", "service_name", "code_name", "table_name", "table_prefix",
                              "pk_name", "package_name", "base_mode", "wrap_mode", "api_path", "web_path", "is_deleted")
values (1123598812738675201, 1123598812738675201, 'usf-demo', '通知公告', 'csf_notice', 'csf_', 'id',
        'com.chinaunicom.usf.desktop', 1, 1, 'F:\workspace\csf\csf-ops\csf-develop', 'F:\workspace\csf-v', 1);
insert into "CSF"."csf_code" ("id", "datasource_id", "service_name", "code_name", "table_name", "table_prefix",
                              "pk_name", "package_name", "base_mode", "wrap_mode", "api_path", "web_path", "is_deleted")
values (1607633614069628929, 1123598812738675201, 'csf-demo', '通知公告', 'csf_notice', 'csf_', 'id',
        'com.chinaunicom.csf.desktop', 1, 1, '/Users/<USER>/Downloads/csf-demo', '/Users/<USER>/Downloads/csf-v', 0);


CREATE TABLE "CSF"."csf_datasource"
(
    "id"           BIGINT NOT NULL,
    "name"         VARCHAR(100),
    "driver_class" VARCHAR(100),
    "url"          VARCHAR(500),
    "username"     VARCHAR(50),
    "password"     VARCHAR(50),
    "remark"       VARCHAR(255),
    "create_user"  BIGINT,
    "create_time"  TIMESTAMP(0),
    "update_user"  BIGINT,
    "update_time"  TIMESTAMP(0),
    "status"       INT,
    "is_deleted"   INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_datasource" IS '数据源配置表';
COMMENT ON COLUMN "CSF"."csf_datasource"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_datasource"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_datasource"."driver_class" IS '驱动类';
COMMENT ON COLUMN "CSF"."csf_datasource"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_datasource"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_datasource"."name" IS '名称';
COMMENT ON COLUMN "CSF"."csf_datasource"."password" IS '密码';
COMMENT ON COLUMN "CSF"."csf_datasource"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_datasource"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_datasource"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_datasource"."update_user" IS '修改人';
COMMENT ON COLUMN "CSF"."csf_datasource"."url" IS '连接地址';
COMMENT ON COLUMN "CSF"."csf_datasource"."username" IS '用户名';

insert into "CSF"."csf_datasource" ("id", "name", "driver_class", "url", "username", "password", "remark",
                                    "create_user", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598812738675201, 'mysql', 'com.mysql.cj.jdbc.Driver',
        '*************************************************************************************************************************************************************************************************************************************',
        'root', 'root', 'mysql', 1, '2019-08-14 11:43:06', 1, '2019-08-14 11:43:06', 1, 0);
insert into "CSF"."csf_datasource" ("id", "name", "driver_class", "url", "username", "password", "remark",
                                    "create_user", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598812738675202, 'postgresql', 'org.postgresql.Driver', '************************************', 'postgres',
        '123456', 'postgresql', 1, '2019-08-14 11:43:41', 1, '2019-08-14 11:43:41', 1, 0);
insert into "CSF"."csf_datasource" ("id", "name", "driver_class", "url", "username", "password", "remark",
                                    "create_user", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598812738675203, 'oracle', 'oracle.jdbc.OracleDriver', '**************************************', 'USF',
        'usf', 'oracle', 1, '2019-08-14 11:44:03', 1, '2019-08-14 11:44:03', 1, 0);
insert into "CSF"."csf_datasource" ("id", "name", "driver_class", "url", "username", "password", "remark",
                                    "create_user", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1607993853344284673, '达梦', 'dm.jdbc.driver.DmDriver', 'jdbc:dm://10.81.24.136:5237?keyWords=domain', 'csf',
        'csf', '达梦数据库', 1123598821738675201, '2022-12-28 14:56:24', 1123598821738675201, '2022-12-28 14:56:24', 1, 0);

CREATE TABLE "CSF"."csf_dept"
(
    "id"         BIGINT NOT NULL,
    "tenant_id"  VARCHAR(12) DEFAULT '000000',
    "parent_id"  BIGINT      DEFAULT 0,
    "ancestors"  VARCHAR(2000),
    "dept_name"  VARCHAR(45),
    "full_name"  VARCHAR(45),
    "sort"       INT,
    "remark"     VARCHAR(255),
    "is_deleted" INT         DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_dept" IS '部门表';
COMMENT ON COLUMN "CSF"."csf_dept"."ancestors" IS '祖级列表';
COMMENT ON COLUMN "CSF"."csf_dept"."dept_name" IS '部门名';
COMMENT ON COLUMN "CSF"."csf_dept"."full_name" IS '部门全称';
COMMENT ON COLUMN "CSF"."csf_dept"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_dept"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_dept"."parent_id" IS '父主键';
COMMENT ON COLUMN "CSF"."csf_dept"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_dept"."sort" IS '排序';
COMMENT ON COLUMN "CSF"."csf_dept"."tenant_id" IS '租户ID';

insert into "CSF"."csf_dept" ("id", "tenant_id", "parent_id", "ancestors", "dept_name", "full_name", "sort", "remark",
                              "is_deleted")
values (1123598813738675201, '000000', 0, '0', '软件研发部', '软件研发部', 1, null, 0);
insert into "CSF"."csf_dept" ("id", "tenant_id", "parent_id", "ancestors", "dept_name", "full_name", "sort", "remark",
                              "is_deleted")
values (1123598813738675202, '000000', 1123598813738675201, '0,1123598813738675201', '总架团队', '总架团队', 1, null, 0);

CREATE TABLE "CSF"."csf_dict"
(
    "id"         BIGINT NOT NULL,
    "parent_id"  BIGINT DEFAULT 0,
    "code"       VARCHAR(255),
    "dict_key"   INT,
    "dict_value" VARCHAR(255),
    "sort"       INT,
    "remark"     VARCHAR(255),
    "is_deleted" INT    DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_dict" IS '字典表';
COMMENT ON COLUMN "CSF"."csf_dict"."code" IS '字典码';
COMMENT ON COLUMN "CSF"."csf_dict"."dict_key" IS '字典值';
COMMENT ON COLUMN "CSF"."csf_dict"."dict_value" IS '字典名称';
COMMENT ON COLUMN "CSF"."csf_dict"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_dict"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_dict"."parent_id" IS '父主键';
COMMENT ON COLUMN "CSF"."csf_dict"."remark" IS '字典备注';
COMMENT ON COLUMN "CSF"."csf_dict"."sort" IS '排序';

insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675201, 0, 'sex', -1, '性别', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675202, 1123598814738675201, 'sex', 1, '男', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675203, 1123598814738675201, 'sex', 2, '女', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675204, 0, 'notice', -1, '通知类型', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675205, 1123598814738675204, 'notice', 1, '发布通知', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675206, 1123598814738675204, 'notice', 2, '批转通知', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675207, 1123598814738675204, 'notice', 3, '转发通知', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675208, 1123598814738675204, 'notice', 4, '指示通知', 4, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675209, 1123598814738675204, 'notice', 5, '任免通知', 5, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675210, 1123598814738675204, 'notice', 6, '事务通知', 6, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675211, 0, 'menu_category', -1, '菜单类型', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675212, 1123598814738675211, 'menu_category', 1, '菜单', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675213, 1123598814738675211, 'menu_category', 2, '按钮', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675214, 0, 'button_func', -1, '按钮功能', 4, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675215, 1123598814738675214, 'button_func', 1, '工具栏', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675216, 1123598814738675214, 'button_func', 2, '操作栏', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675217, 1123598814738675214, 'button_func', 3, '工具操作栏', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675218, 0, 'yes_no', -1, '是否', 5, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675219, 1123598814738675218, 'yes_no', 1, '否', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675220, 1123598814738675218, 'yes_no', 2, '是', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675231, 0, 'data_scope_type', -1, '数据权限', 8, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675232, 1123598814738675231, 'data_scope_type', 1, '全部可见', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675233, 1123598814738675231, 'data_scope_type', 2, '本人可见', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675234, 1123598814738675231, 'data_scope_type', 3, '所在机构可见', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675235, 1123598814738675231, 'data_scope_type', 4, '所在机构及子级可见', 4, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738675236, 1123598814738675231, 'data_scope_type', 5, '自定义', 5, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777220, 0, 'post_category', -1, '岗位类型', 12, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777221, 1123598814738777220, 'post_category', 1, '高层', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777222, 1123598814738777220, 'post_category', 2, '中层', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777223, 1123598814738777220, 'post_category', 3, '基层', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777224, 1123598814738777220, 'post_category', 4, '其他', 4, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777230, 0, 'region', -1, '行政区划', 13, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777231, 1123598814738777230, 'region', 0, '国家', 0, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777232, 1123598814738777230, 'region', 1, '省份/直辖市', 1, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777233, 1123598814738777230, 'region', 2, '地市', 2, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777234, 1123598814738777230, 'region', 3, '区县', 3, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777235, 1123598814738777230, 'region', 4, '乡镇', 4, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1123598814738777236, 1123598814738777230, 'region', 5, '村委', 5, null, 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1518771297010630658, 0, 'api_scope_type', -1, '接口权限', 15, '', 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1518771475000115202, 1518771297010630658, 'api_scope_type', 1, '系统接口', 1, '', 0);
insert into "CSF"."csf_dict" ("id", "parent_id", "code", "dict_key", "dict_value", "sort", "remark", "is_deleted")
values (1518771546202619905, 1518771297010630658, 'api_scope_type', 2, '业务接口', 2, '', 0);

CREATE TABLE "CSF"."csf_log_api"
(
    "id"           BIGINT NOT NULL,
    "tenant_id"    VARCHAR(12)  DEFAULT '000000',
    "service_id"   VARCHAR(32),
    "server_host"  VARCHAR(255),
    "server_ip"    VARCHAR(255),
    "env"          VARCHAR(255),
    "type"         CHAR(1)      DEFAULT '1',
    "title"        VARCHAR(255) DEFAULT '',
    "method"       VARCHAR(10),
    "request_uri"  VARCHAR(255),
    "user_agent"   VARCHAR(1000),
    "remote_ip"    VARCHAR(255),
    "method_class" VARCHAR(255),
    "method_name"  VARCHAR(255),
    "params"       TEXT,
    "time"         VARCHAR(64),
    "create_by"    VARCHAR(64),
    "create_time"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_log_api" IS '接口日志表';
COMMENT ON COLUMN "CSF"."csf_log_api"."create_by" IS '创建者';
COMMENT ON COLUMN "CSF"."csf_log_api"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_log_api"."env" IS '服务器环境';
COMMENT ON COLUMN "CSF"."csf_log_api"."id" IS '编号';
COMMENT ON COLUMN "CSF"."csf_log_api"."method" IS '操作方式';
COMMENT ON COLUMN "CSF"."csf_log_api"."method_class" IS '方法类';
COMMENT ON COLUMN "CSF"."csf_log_api"."method_name" IS '方法名';
COMMENT ON COLUMN "CSF"."csf_log_api"."params" IS '操作提交的数据';
COMMENT ON COLUMN "CSF"."csf_log_api"."remote_ip" IS '操作IP地址';
COMMENT ON COLUMN "CSF"."csf_log_api"."request_uri" IS '请求URI';
COMMENT ON COLUMN "CSF"."csf_log_api"."server_host" IS '服务器名';
COMMENT ON COLUMN "CSF"."csf_log_api"."server_ip" IS '服务器IP地址';
COMMENT ON COLUMN "CSF"."csf_log_api"."service_id" IS '服务ID';
COMMENT ON COLUMN "CSF"."csf_log_api"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_log_api"."time" IS '执行时间';
COMMENT ON COLUMN "CSF"."csf_log_api"."title" IS '日志标题';
COMMENT ON COLUMN "CSF"."csf_log_api"."type" IS '日志类型';
COMMENT ON COLUMN "CSF"."csf_log_api"."user_agent" IS '用户代理';


CREATE TABLE "CSF"."csf_log_error"
(
    "id"             BIGINT NOT NULL,
    "tenant_id"      VARCHAR(12)  DEFAULT '000000',
    "service_id"     VARCHAR(32),
    "server_host"    VARCHAR(255),
    "server_ip"      VARCHAR(255),
    "env"            VARCHAR(255),
    "method"         VARCHAR(10),
    "request_uri"    VARCHAR(255),
    "user_agent"     VARCHAR(1000),
    "stack_trace"    TEXT,
    "exception_name" VARCHAR(255),
    "message"        TEXT,
    "line_number"    INT,
    "remote_ip"      VARCHAR(255),
    "method_class"   VARCHAR(255),
    "file_name"      VARCHAR(1000),
    "method_name"    VARCHAR(255),
    "params"         TEXT,
    "time"           VARCHAR(64),
    "create_by"      VARCHAR(64),
    "create_time"    TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

CREATE TABLE "CSF"."csf_log_usual"
(
    "id"           BIGINT NOT NULL,
    "tenant_id"    VARCHAR(12)  DEFAULT '000000',
    "service_id"   VARCHAR(32),
    "server_host"  VARCHAR(255),
    "server_ip"    VARCHAR(255),
    "env"          VARCHAR(255),
    "log_level"    VARCHAR(10),
    "log_id"       VARCHAR(100),
    "log_data"     TEXT,
    "method"       VARCHAR(10),
    "request_uri"  VARCHAR(255),
    "remote_ip"    VARCHAR(255),
    "method_class" VARCHAR(255),
    "method_name"  VARCHAR(255),
    "user_agent"   VARCHAR(1000),
    "params"       TEXT,
    "time"         TIMESTAMP(0),
    "create_by"    VARCHAR(64),
    "create_time"  TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_log_usual" IS '通用日志表';
COMMENT ON COLUMN "CSF"."csf_log_usual"."create_by" IS '创建者';
COMMENT ON COLUMN "CSF"."csf_log_usual"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_log_usual"."env" IS '系统环境';
COMMENT ON COLUMN "CSF"."csf_log_usual"."id" IS '编号';
COMMENT ON COLUMN "CSF"."csf_log_usual"."log_data" IS '日志数据';
COMMENT ON COLUMN "CSF"."csf_log_usual"."log_id" IS '日志业务id';
COMMENT ON COLUMN "CSF"."csf_log_usual"."log_level" IS '日志级别';
COMMENT ON COLUMN "CSF"."csf_log_usual"."method" IS '操作方式';
COMMENT ON COLUMN "CSF"."csf_log_usual"."method_class" IS '方法类';
COMMENT ON COLUMN "CSF"."csf_log_usual"."method_name" IS '方法名';
COMMENT ON COLUMN "CSF"."csf_log_usual"."params" IS '操作提交的数据';
COMMENT ON COLUMN "CSF"."csf_log_usual"."remote_ip" IS '操作IP地址';
COMMENT ON COLUMN "CSF"."csf_log_usual"."request_uri" IS '请求URI';
COMMENT ON COLUMN "CSF"."csf_log_usual"."server_host" IS '服务器名';
COMMENT ON COLUMN "CSF"."csf_log_usual"."server_ip" IS '服务器IP地址';
COMMENT ON COLUMN "CSF"."csf_log_usual"."service_id" IS '服务ID';
COMMENT ON COLUMN "CSF"."csf_log_usual"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_log_usual"."time" IS '执行时间';
COMMENT ON COLUMN "CSF"."csf_log_usual"."user_agent" IS '用户代理';


CREATE TABLE "CSF"."csf_menu"
(
    "id"         BIGINT NOT NULL,
    "parent_id"  BIGINT DEFAULT 0,
    "code"       VARCHAR(255),
    "name"       VARCHAR(255),
    "alias"      VARCHAR(255),
    "path"       VARCHAR(255),
    "source"     VARCHAR(255),
    "sort"       INT,
    "category"   INT,
    "action"     INT    DEFAULT 0,
    "component"  VARCHAR(255),
    "is_open"    INT    DEFAULT 1,
    "remark"     VARCHAR(255),
    "is_deleted" INT    DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_menu" IS '菜单表';
COMMENT ON COLUMN "CSF"."csf_menu"."action" IS '操作按钮类型';
COMMENT ON COLUMN "CSF"."csf_menu"."alias" IS '菜单别名';
COMMENT ON COLUMN "CSF"."csf_menu"."category" IS '菜单类型';
COMMENT ON COLUMN "CSF"."csf_menu"."code" IS '菜单编号';
COMMENT ON COLUMN "CSF"."csf_menu"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_menu"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_menu"."is_open" IS '是否打开新页面';
COMMENT ON COLUMN "CSF"."csf_menu"."name" IS '菜单名称';
COMMENT ON COLUMN "CSF"."csf_menu"."parent_id" IS '父级菜单';
COMMENT ON COLUMN "CSF"."csf_menu"."path" IS '请求地址';
COMMENT ON COLUMN "CSF"."csf_menu"."component" IS '组件地址';
COMMENT ON COLUMN "CSF"."csf_menu"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_menu"."sort" IS '排序';
COMMENT ON COLUMN "CSF"."csf_menu"."source" IS '菜单资源';

insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675201, 0, 'desk', '工作台', 'menu', '/desk', 'iconfont iconicon_airplay', 1, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675202, 1123598815738675201, 'notice', '通知公告', 'menu', '/desk/notice', 'iconfont iconicon_sms', 1,
        1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675203, 0, 'system', '系统管理', 'menu', '/system', 'iconfont iconicon_setting', 99, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675204, 1123598815738675203, 'user', '用户管理', 'menu', '/system/user', 'iconfont iconicon_principal',
        1, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675205, 1123598815738675203, 'dept', '部门管理', 'menu', '/system/dept', 'iconfont iconicon_group', 2,
        1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675206, 1123598815738675203, 'dict', '字典管理', 'menu', '/system/dict',
        'iconfont iconicon_addresslist', 3, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675207, 1123598815738675203, 'menu', '菜单管理', 'menu', '/system/menu',
        'iconfont iconicon_subordinate', 4, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675209, 1123598815738675203, 'param', '参数管理', 'menu', '/system/param',
        'iconfont iconicon_community_line', 6, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675210, 0, 'monitor', '系统监控', 'menu', '/monitor', 'iconfont icon-yanzhengma', 3, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675211, 1123598815738675210, 'doc', '接口文档', 'menu', 'http://10.81.24.136:18000/doc.html',
        'iconfont iconicon_study', 1, 1, 0, 2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675212, 1123598815738675210, 'admin', '服务治理', 'menu', 'http://10.81.24.136:7002',
        'iconfont icon-canshu', 2, 1, 0, 2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675213, 1123598815738675210, 'log', '日志管理', 'menu', '/monitor/log', 'iconfont iconicon_doc', 3, 1,
        0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675214, 1123598815738675213, 'log_usual', '通用日志', 'menu', '/monitor/log/usual', null, 1, 1, 0, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675215, 1123598815738675213, 'log_api', '接口日志', 'menu', '/monitor/log/api', null, 2, 1, 0, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675216, 1123598815738675213, 'log_error', '错误日志', 'menu', '/monitor/log/error', null, 3, 1, 0, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675217, 0, 'tool', '研发工具', 'menu', '/tool', 'iconfont icon-wxbgongju', 4, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675218, 1123598815738675217, 'code', '代码生成', 'menu', '/tool/code', 'iconfont iconicon_savememo', 1,
        1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675219, 1123598815738675202, 'notice_add', '新增', 'add', '/desk/notice/add', 'iconfont icongithub',
        1, 2, 1, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675220, 1123598815738675202, 'notice_edit', '修改', 'edit', '/desk/notice/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675221, 1123598815738675202, 'notice_delete', '删除', 'delete', '/api/usf-desk/notice/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675222, 1123598815738675202, 'notice_view', '查看', 'view', '/desk/notice/view', 'file-text', 4, 2,
        2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675223, 1123598815738675204, 'user_add', '新增', 'add', '/system/user/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675224, 1123598815738675204, 'user_edit', '修改', 'edit', '/system/user/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675225, 1123598815738675204, 'user_delete', '删除', 'delete', '/api/usf-user/remove', 'delete', 3, 2,
        3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675226, 1123598815738675204, 'user_role', '角色配置', 'role', null, 'user-add', 4, 2, 1, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675227, 1123598815738675204, 'user_reset', '密码重置', 'reset-password',
        '/api/usf-user/reset-password', 'retweet', 5, 2, 1, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675228, 1123598815738675204, 'user_view', '查看', 'view', '/system/user/view', 'file-text', 6, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675229, 1123598815738675205, 'dept_add', '新增', 'add', '/system/dept/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675230, 1123598815738675205, 'dept_edit', '修改', 'edit', '/system/dept/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675231, 1123598815738675205, 'dept_delete', '删除', 'delete', '/api/usf-system/dept/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675232, 1123598815738675205, 'dept_view', '查看', 'view', '/system/dept/view', 'file-text', 4, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675233, 1123598815738675206, 'dict_add', '新增', 'add', '/system/dict/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675234, 1123598815738675206, 'dict_edit', '修改', 'edit', '/system/dict/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675235, 1123598815738675206, 'dict_delete', '删除', 'delete', '/api/usf-system/dict/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675236, 1123598815738675206, 'dict_view', '查看', 'view', '/system/dict/view', 'file-text', 4, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675237, 1123598815738675207, 'menu_add', '新增', 'add', '/system/menu/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675238, 1123598815738675207, 'menu_edit', '修改', 'edit', '/system/menu/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675239, 1123598815738675207, 'menu_delete', '删除', 'delete', '/api/usf-system/menu/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675240, 1123598815738675207, 'menu_view', '查看', 'view', '/system/menu/view', 'file-text', 4, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675241, 1123598815738675308, 'role_add', '新增', 'add', '/authority/role/add', 'plus', 1, 2, 1, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675242, 1123598815738675308, 'role_edit', '修改', 'edit', '/authority/role/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675243, 1123598815738675308, 'role_delete', '删除', 'delete', '/api/usf-system/role/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675244, 1123598815738675308, 'role_view', '查看', 'view', '/authority/role/view', 'file-text', 4, 2,
        2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675245, 1123598815738675209, 'param_add', '新增', 'add', '/system/param/add', 'plus', 1, 2, 1, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675246, 1123598815738675209, 'param_edit', '修改', 'edit', '/system/param/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675247, 1123598815738675209, 'param_delete', '删除', 'delete', '/api/usf-system/param/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675248, 1123598815738675209, 'param_view', '查看', 'view', '/system/param/view', 'file-text', 4, 2,
        2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675249, 1123598815738675214, 'log_usual_view', '查看', 'view', '/monitor/log/usual/view',
        'file-text', 4, 2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675250, 1123598815738675215, 'log_api_view', '查看', 'view', '/monitor/log/api/view', 'file-text', 4,
        2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675251, 1123598815738675216, 'log_error_view', '查看', 'view', '/monitor/log/error/view',
        'file-text', 4, 2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675252, 1123598815738675218, 'code_add', '新增', 'add', '/tool/code/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675253, 1123598815738675218, 'code_edit', '修改', 'edit', '/tool/code/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675254, 1123598815738675218, 'code_delete', '删除', 'delete', '/api/usf-system/code/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675255, 1123598815738675218, 'code_view', '查看', 'view', '/tool/code/view', 'file-text', 4, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675256, 1123598815738675203, 'tenant', '租户管理', 'menu', '/system/tenant', 'iconfont icon-quanxian',
        7, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675257, 1123598815738675256, 'tenant_add', '新增', 'add', '/system/tenant/add', 'plus', 1, 2, 1, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675258, 1123598815738675256, 'tenant_edit', '修改', 'edit', '/system/tenant/edit', 'form', 2, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675259, 1123598815738675256, 'tenant_delete', '删除', 'delete', '/api/usf-system/tenant/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675260, 1123598815738675256, 'tenant_view', '查看', 'view', '/system/tenant/view', 'file-text', 4, 2,
        2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675261, 1123598815738675203, 'client', '应用管理', 'menu', '/system/client',
        'iconfont iconicon_mobilephone', 8, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675262, 1123598815738675261, 'client_add', '新增', 'add', '/system/client/add', 'plus', 1, 2, 1, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675263, 1123598815738675261, 'client_edit', '修改', 'edit', '/system/client/edit', 'form', 2, 2, 2,
        2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675264, 1123598815738675261, 'client_delete', '删除', 'delete', '/api/usf-system/client/remove',
        'delete', 3, 2, 3, 3, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675265, 1123598815738675261, 'client_view', '查看', 'view', '/system/client/view', 'file-text', 4, 2,
        2, 2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675266, 1123598815738675217, 'datasource', '数据源管理', 'menu', '/tool/datasource',
        'iconfont icon-caidanguanli', 2, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675267, 1123598815738675266, 'datasource_add', '新增', 'add', '/tool/datasource/add', 'plus', 1, 2,
        1, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675268, 1123598815738675266, 'datasource_edit', '修改', 'edit', '/tool/datasource/edit', 'form', 2,
        2, 2, 2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675269, 1123598815738675266, 'datasource_delete', '删除', 'delete',
        '/api/usf-develop/datasource/remove', 'delete', 3, 2, 3, 3, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675270, 1123598815738675266, 'datasource_view', '查看', 'view', '/tool/datasource/view', 'file-text',
        4, 2, 2, 2, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675307, 0, 'authority', '权限管理', 'menu', '/authority', 'iconfont icon-bofangqi-suoping', 98, 1, 0,
        1, '', 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675308, 1123598815738675307, 'role', '角色管理', 'menu', '/authority/role', 'iconfont iconicon_boss',
        1, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675309, 1123598815738675307, 'data_scope', '数据权限', 'menu', '/authority/datascope',
        'iconfont icon-shujuzhanshi2', 2, 1, 0, 1, '', 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1123598815738675310, 1123598815738675309, 'data_scope_setting', '权限配置', 'setting', null, 'setting', 1, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733389668962251, 1123598815738675203, 'post', '岗位管理', 'menu', '/system/post', 'iconfont iconicon_message',
        2, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733389668962252, 1164733389668962251, 'post_add', '新增', 'add', '/system/post/add', 'plus', 1, 2, 1, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733389668962253, 1164733389668962251, 'post_edit', '修改', 'edit', '/system/post/edit', 'form', 2, 2, 2, 1,
        null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733389668962254, 1164733389668962251, 'post_delete', '删除', 'delete', '/api/usf-system/post/remove',
        'delete', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733389668962255, 1164733389668962251, 'post_view', '查看', 'view', '/system/post/view', 'file-text', 4, 2, 2,
        1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962201, 0, 'base', '基础配置', 'menu', '/base', 'iconfont iconicon_affiliations_li', 97, 1, 0, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962202, 1164733399668962201, 'region', '行政区划', 'menu', '/base/region', 'iconfont icon-iframe', 1,
        1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962203, 1164733399668962202, 'region_add', '新增下级', 'add', '', '', 1, 2, 1, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962204, 1164733399668962202, 'region_delete', '删除', 'delete', '/api/usf-system/region/remove', '',
        2, 2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962205, 1164733399668962202, 'region_import', '导入', 'import', '', '', 3, 2, 3, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962206, 1164733399668962202, 'region_export', '导出', 'export', '', '', 4, 2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399668962207, 1164733399668962202, 'region_debug', '调试', 'debug', '', '', 5, 2, 2, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399669962301, 0, 'report', '报表管理', 'menu', '/report', 'iconfont icon-shujuzhanshi2', 5, 1, 0, 1, null,
        0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399669962302, 1164733399669962301, 'report_setting', '报表配置', 'menu',
        'http://10.81.24.136:8108/ureport/designer', 'iconfont icon-rizhi', 1, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1164733399669962303, 1164733399669962301, 'report_list', '报表列表', 'menu', '/report/reportlist',
        'iconfont icon-biaodan', 2, 1, 0, 1, null, 0);
insert into "CSF"."csf_menu" ("id", "parent_id", "code", "name", "alias", "path", "source", "sort", "category",
                              "action", "is_open", "remark", "is_deleted")
values (1518489311171887106, 1123598815738675307, 'interface', '接口权限', 'menu', '/authority/interface',
        'iconfont iconicon_exchange', 3, 1, 1, 1, null, 0);

CREATE TABLE "CSF"."csf_notice"
(
    "id"           BIGINT NOT NULL,
    "tenant_id"    VARCHAR(12) DEFAULT '000000',
    "title"        VARCHAR(255),
    "category"     INT,
    "release_time" TIMESTAMP(0),
    "content"      VARCHAR(255),
    "create_user"  BIGINT,
    "create_dept"  BIGINT,
    "create_time"  TIMESTAMP(0),
    "update_user"  BIGINT,
    "update_time"  TIMESTAMP(0),
    "status"       INT,
    "is_deleted"   INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_notice" IS '通知公告表';
COMMENT ON COLUMN "CSF"."csf_notice"."category" IS '类型';
COMMENT ON COLUMN "CSF"."csf_notice"."content" IS '内容';
COMMENT ON COLUMN "CSF"."csf_notice"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_notice"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_notice"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_notice"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_notice"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_notice"."release_time" IS '发布时间';
COMMENT ON COLUMN "CSF"."csf_notice"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_notice"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_notice"."title" IS '标题';
COMMENT ON COLUMN "CSF"."csf_notice"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_notice"."update_user" IS '修改人';

insert into "CSF"."csf_notice" ("id", "tenant_id", "title", "category", "release_time", "content", "create_user",
                                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598818738675223, '000000', '测试公告', 3, '2018-12-31 20:03:31', '222', 1123598821738675201,
        1123598813738675201, '2018-12-05 20:03:31', 1123598821738675201, '2018-12-28 11:10:51', 1, 0);
insert into "CSF"."csf_notice" ("id", "tenant_id", "title", "category", "release_time", "content", "create_user",
                                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598818738675224, '000000', '测试公告2', 1, '2018-12-05 20:03:31', '333', 1123598821738675201,
        1123598813738675201, '2018-12-28 10:32:26', 1123598821738675201, '2018-12-28 11:10:34', 1, 0);
insert into "CSF"."csf_notice" ("id", "tenant_id", "title", "category", "release_time", "content", "create_user",
                                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598818738675225, '000000', '测试公告3', 6, '2018-12-29', '11111', 1123598821738675201, 1123598813738675201,
        '2018-12-28 11:03:44', 1123598821738675201, '2018-12-28 11:10:28', 1, 1);
insert into "CSF"."csf_notice" ("id", "tenant_id", "title", "category", "release_time", "content", "create_user",
                                "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1607574335052644353, '000000', '达梦适配测试1', 2, '2022-12-28 12:00:00', '达梦适配测试达梦适配测试达梦适配测试达梦适配测试aca', -1, null,
        '2022-12-27 11:09:23', -1, '2022-12-27 11:09:47', 1, 0);

CREATE TABLE "CSF"."csf_param"
(
    "id"          BIGINT NOT NULL,
    "param_name"  VARCHAR(255),
    "param_key"   VARCHAR(255),
    "param_value" VARCHAR(255),
    "remark"      VARCHAR(255),
    "create_user" BIGINT,
    "create_dept" BIGINT,
    "create_time" TIMESTAMP(0),
    "update_user" BIGINT,
    "update_time" TIMESTAMP(0),
    "status"      INT,
    "is_deleted"  INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_param" IS '参数表';
COMMENT ON COLUMN "CSF"."csf_param"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_param"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_param"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_param"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_param"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_param"."param_key" IS '参数键';
COMMENT ON COLUMN "CSF"."csf_param"."param_name" IS '参数名';
COMMENT ON COLUMN "CSF"."csf_param"."param_value" IS '参数值';
COMMENT ON COLUMN "CSF"."csf_param"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_param"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_param"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_param"."update_user" IS '修改人';

insert into "CSF"."csf_param" ("id", "param_name", "param_key", "param_value", "remark", "create_user", "create_dept",
                               "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598819738675201, '是否开启注册功能', 'account.registerUser', 'true', '开启注册', 1123598821738675201,
        1123598813738675201, '2018-12-28 12:19:01', 1123598821738675201, '2018-12-28 12:19:01', 1, 0);
insert into "CSF"."csf_param" ("id", "param_name", "param_key", "param_value", "remark", "create_user", "create_dept",
                               "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598819738675202, '账号初始密码', 'account.initPassword', '123456', '初始密码', 1123598821738675201,
        1123598813738675201, '2018-12-28 12:19:01', 1123598821738675201, '2018-12-28 12:19:01', 1, 0);

CREATE TABLE "CSF"."csf_post"
(
    "id"          BIGINT NOT NULL,
    "tenant_id"   VARCHAR(12) DEFAULT '000000',
    "category"    INT,
    "post_code"   VARCHAR(12),
    "post_name"   VARCHAR(64),
    "sort"        INT,
    "remark"      VARCHAR(255),
    "create_user" BIGINT,
    "create_dept" BIGINT,
    "create_time" TIMESTAMP(0),
    "update_user" BIGINT,
    "update_time" TIMESTAMP(0),
    "status"      INT,
    "is_deleted"  INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_post" IS '岗位表';
COMMENT ON COLUMN "CSF"."csf_post"."category" IS '岗位类型';
COMMENT ON COLUMN "CSF"."csf_post"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_post"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_post"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_post"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_post"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_post"."post_code" IS '岗位编号';
COMMENT ON COLUMN "CSF"."csf_post"."post_name" IS '岗位名称';
COMMENT ON COLUMN "CSF"."csf_post"."remark" IS '岗位描述';
COMMENT ON COLUMN "CSF"."csf_post"."sort" IS '岗位排序';
COMMENT ON COLUMN "CSF"."csf_post"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_post"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_post"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_post"."update_user" IS '修改人';

insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675201, '000000', 1, 'ceo', '首席执行官', 1, '总经理', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675202, '000000', 1, 'coo', '首席运营官', 2, '常务总经理', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675203, '000000', 1, 'cfo', '首席财务官', 3, '财务总经理', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675204, '000000', 1, 'cto', '首席技术官', 4, '技术总监', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675205, '000000', 1, 'cio', '首席信息官', 5, '信息总监', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675206, '000000', 2, 'pm', '技术经理', 6, '研发和产品是永远的朋友', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675207, '000000', 2, 'hrm', '人力经理', 7, '人力资源部门工作管理者', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);
insert into "CSF"."csf_post" ("id", "tenant_id", "category", "post_code", "post_name", "sort", "remark", "create_user",
                              "create_dept", "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598817738675208, '000000', 3, 'staff', '普通员工', 8, '普通员工', 1123598821738675201, 1123598813738675201,
        '2020-04-01', 1123598821738675201, '2020-04-01', 1, 0);

CREATE TABLE "CSF"."csf_region"
(
    "code"          VARCHAR(12) NOT NULL,
    "parent_code"   VARCHAR(12),
    "ancestors"     VARCHAR(255),
    "name"          VARCHAR(32),
    "province_code" VARCHAR(12),
    "province_name" VARCHAR(32),
    "city_code"     VARCHAR(12),
    "city_name"     VARCHAR(32),
    "district_code" VARCHAR(12),
    "district_name" VARCHAR(32),
    "town_code"     VARCHAR(12),
    "town_name"     VARCHAR(32),
    "village_code"  VARCHAR(12),
    "village_name"  VARCHAR(32),
    "level"         INT,
    "sort"          INT,
    "remark"        VARCHAR(255),
    NOT CLUSTER PRIMARY KEY ("code")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_region" IS '行政区划表';
COMMENT ON COLUMN "CSF"."csf_region"."ancestors" IS '祖区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."city_code" IS '市级区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."city_name" IS '市级名称';
COMMENT ON COLUMN "CSF"."csf_region"."code" IS '区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."district_code" IS '区级区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."district_name" IS '区级名称';
COMMENT ON COLUMN "CSF"."csf_region"."level" IS '层级';
COMMENT ON COLUMN "CSF"."csf_region"."name" IS '区划名称';
COMMENT ON COLUMN "CSF"."csf_region"."parent_code" IS '父区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."province_code" IS '省级区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."province_name" IS '省级名称';
COMMENT ON COLUMN "CSF"."csf_region"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_region"."sort" IS '排序';
COMMENT ON COLUMN "CSF"."csf_region"."town_code" IS '镇级区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."town_name" IS '镇级名称';
COMMENT ON COLUMN "CSF"."csf_region"."village_code" IS '村级区划编号';
COMMENT ON COLUMN "CSF"."csf_region"."village_name" IS '村级名称';

insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('00', '0', '0', '中华人民共和国', '', '', '', '', '', '', '', '', '', '', 0, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('11', '00', '00', '北京市', '11', '北京市', '', '', '', '', '', '', '', '', 1, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1101', '11', '00,11', '北京市', '11', '北京市', '1101', '北京市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110101', '1101', '00,11,1101', '东城区', '11', '北京市', '1101', '北京市', '110101', '东城区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110102', '1101', '00,11,1101', '西城区', '11', '北京市', '1101', '北京市', '110102', '西城区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110105', '1101', '00,11,1101', '朝阳区', '11', '北京市', '1101', '北京市', '110105', '朝阳区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110106', '1101', '00,11,1101', '丰台区', '11', '北京市', '1101', '北京市', '110106', '丰台区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110107', '1101', '00,11,1101', '石景山区', '11', '北京市', '1101', '北京市', '110107', '石景山区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110108', '1101', '00,11,1101', '海淀区', '11', '北京市', '1101', '北京市', '110108', '海淀区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110109', '1101', '00,11,1101', '门头沟区', '11', '北京市', '1101', '北京市', '110109', '门头沟区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110111', '1101', '00,11,1101', '房山区', '11', '北京市', '1101', '北京市', '110111', '房山区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110112', '1101', '00,11,1101', '通州区', '11', '北京市', '1101', '北京市', '110112', '通州区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110113', '1101', '00,11,1101', '顺义区', '11', '北京市', '1101', '北京市', '110113', '顺义区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110114', '1101', '00,11,1101', '昌平区', '11', '北京市', '1101', '北京市', '110114', '昌平区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110115', '1101', '00,11,1101', '大兴区', '11', '北京市', '1101', '北京市', '110115', '大兴区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110116', '1101', '00,11,1101', '怀柔区', '11', '北京市', '1101', '北京市', '110116', '怀柔区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110117', '1101', '00,11,1101', '平谷区', '11', '北京市', '1101', '北京市', '110117', '平谷区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110118', '1101', '00,11,1101', '密云区', '11', '北京市', '1101', '北京市', '110118', '密云区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('110119', '1101', '00,11,1101', '延庆区', '11', '北京市', '1101', '北京市', '110119', '延庆区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('12', '00', '00', '天津市', '12', '天津市', '', '', '', '', '', '', '', '', 1, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1201', '12', '00,12', '天津市', '12', '天津市', '1201', '天津市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120101', '1201', '00,12,1201', '和平区', '12', '天津市', '1201', '天津市', '120101', '和平区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120102', '1201', '00,12,1201', '河东区', '12', '天津市', '1201', '天津市', '120102', '河东区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120103', '1201', '00,12,1201', '河西区', '12', '天津市', '1201', '天津市', '120103', '河西区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120104', '1201', '00,12,1201', '南开区', '12', '天津市', '1201', '天津市', '120104', '南开区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120105', '1201', '00,12,1201', '河北区', '12', '天津市', '1201', '天津市', '120105', '河北区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120106', '1201', '00,12,1201', '红桥区', '12', '天津市', '1201', '天津市', '120106', '红桥区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120110', '1201', '00,12,1201', '东丽区', '12', '天津市', '1201', '天津市', '120110', '东丽区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120111', '1201', '00,12,1201', '西青区', '12', '天津市', '1201', '天津市', '120111', '西青区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120112', '1201', '00,12,1201', '津南区', '12', '天津市', '1201', '天津市', '120112', '津南区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120113', '1201', '00,12,1201', '北辰区', '12', '天津市', '1201', '天津市', '120113', '北辰区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120114', '1201', '00,12,1201', '武清区', '12', '天津市', '1201', '天津市', '120114', '武清区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120115', '1201', '00,12,1201', '宝坻区', '12', '天津市', '1201', '天津市', '120115', '宝坻区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120116', '1201', '00,12,1201', '滨海新区', '12', '天津市', '1201', '天津市', '120116', '滨海新区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120117', '1201', '00,12,1201', '宁河区', '12', '天津市', '1201', '天津市', '120117', '宁河区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120118', '1201', '00,12,1201', '静海区', '12', '天津市', '1201', '天津市', '120118', '静海区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('120119', '1201', '00,12,1201', '蓟州区', '12', '天津市', '1201', '天津市', '120119', '蓟州区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('13', '00', '00', '河北省', '13', '河北省', '', '', '', '', '', '', '', '', 1, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1301', '13', '00,13', '石家庄市', '13', '河北省', '1301', '石家庄市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130102', '1301', '00,13,1301', '长安区', '13', '河北省', '1301', '石家庄市', '130102', '长安区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130104', '1301', '00,13,1301', '桥西区', '13', '河北省', '1301', '石家庄市', '130104', '桥西区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130105', '1301', '00,13,1301', '新华区', '13', '河北省', '1301', '石家庄市', '130105', '新华区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130107', '1301', '00,13,1301', '井陉矿区', '13', '河北省', '1301', '石家庄市', '130107', '井陉矿区', '', '', '', '', 3, 1,
        '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130108', '1301', '00,13,1301', '裕华区', '13', '河北省', '1301', '石家庄市', '130108', '裕华区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130109', '1301', '00,13,1301', '藁城区', '13', '河北省', '1301', '石家庄市', '130109', '藁城区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130110', '1301', '00,13,1301', '鹿泉区', '13', '河北省', '1301', '石家庄市', '130110', '鹿泉区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130111', '1301', '00,13,1301', '栾城区', '13', '河北省', '1301', '石家庄市', '130111', '栾城区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130121', '1301', '00,13,1301', '井陉县', '13', '河北省', '1301', '石家庄市', '130121', '井陉县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130123', '1301', '00,13,1301', '正定县', '13', '河北省', '1301', '石家庄市', '130123', '正定县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130125', '1301', '00,13,1301', '行唐县', '13', '河北省', '1301', '石家庄市', '130125', '行唐县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130126', '1301', '00,13,1301', '灵寿县', '13', '河北省', '1301', '石家庄市', '130126', '灵寿县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130127', '1301', '00,13,1301', '高邑县', '13', '河北省', '1301', '石家庄市', '130127', '高邑县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130128', '1301', '00,13,1301', '深泽县', '13', '河北省', '1301', '石家庄市', '130128', '深泽县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130129', '1301', '00,13,1301', '赞皇县', '13', '河北省', '1301', '石家庄市', '130129', '赞皇县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130130', '1301', '00,13,1301', '无极县', '13', '河北省', '1301', '石家庄市', '130130', '无极县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130131', '1301', '00,13,1301', '平山县', '13', '河北省', '1301', '石家庄市', '130131', '平山县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130132', '1301', '00,13,1301', '元氏县', '13', '河北省', '1301', '石家庄市', '130132', '元氏县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130133', '1301', '00,13,1301', '赵县', '13', '河北省', '1301', '石家庄市', '130133', '赵县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130181', '1301', '00,13,1301', '辛集市', '13', '河北省', '1301', '石家庄市', '130181', '辛集市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130183', '1301', '00,13,1301', '晋州市', '13', '河北省', '1301', '石家庄市', '130183', '晋州市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130184', '1301', '00,13,1301', '新乐市', '13', '河北省', '1301', '石家庄市', '130184', '新乐市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1302', '13', '00,13', '唐山市', '13', '河北省', '1302', '唐山市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130202', '1302', '00,13,1302', '路南区', '13', '河北省', '1302', '唐山市', '130202', '路南区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130203', '1302', '00,13,1302', '路北区', '13', '河北省', '1302', '唐山市', '130203', '路北区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130204', '1302', '00,13,1302', '古冶区', '13', '河北省', '1302', '唐山市', '130204', '古冶区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130205', '1302', '00,13,1302', '开平区', '13', '河北省', '1302', '唐山市', '130205', '开平区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130207', '1302', '00,13,1302', '丰南区', '13', '河北省', '1302', '唐山市', '130207', '丰南区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130208', '1302', '00,13,1302', '丰润区', '13', '河北省', '1302', '唐山市', '130208', '丰润区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130209', '1302', '00,13,1302', '曹妃甸区', '13', '河北省', '1302', '唐山市', '130209', '曹妃甸区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130224', '1302', '00,13,1302', '滦南县', '13', '河北省', '1302', '唐山市', '130224', '滦南县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130225', '1302', '00,13,1302', '乐亭县', '13', '河北省', '1302', '唐山市', '130225', '乐亭县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130227', '1302', '00,13,1302', '迁西县', '13', '河北省', '1302', '唐山市', '130227', '迁西县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130229', '1302', '00,13,1302', '玉田县', '13', '河北省', '1302', '唐山市', '130229', '玉田县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130281', '1302', '00,13,1302', '遵化市', '13', '河北省', '1302', '唐山市', '130281', '遵化市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130283', '1302', '00,13,1302', '迁安市', '13', '河北省', '1302', '唐山市', '130283', '迁安市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130284', '1302', '00,13,1302', '滦州市', '13', '河北省', '1302', '唐山市', '130284', '滦州市', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1303', '13', '00,13', '秦皇岛市', '13', '河北省', '1303', '秦皇岛市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130302', '1303', '00,13,1303', '海港区', '13', '河北省', '1303', '秦皇岛市', '130302', '海港区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130303', '1303', '00,13,1303', '山海关区', '13', '河北省', '1303', '秦皇岛市', '130303', '山海关区', '', '', '', '', 3, 1,
        '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130304', '1303', '00,13,1303', '北戴河区', '13', '河北省', '1303', '秦皇岛市', '130304', '北戴河区', '', '', '', '', 3, 1,
        '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130306', '1303', '00,13,1303', '抚宁区', '13', '河北省', '1303', '秦皇岛市', '130306', '抚宁区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130321', '1303', '00,13,1303', '青龙满族自治县', '13', '河北省', '1303', '秦皇岛市', '130321', '青龙满族自治县', '', '', '', '', 3,
        1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130322', '1303', '00,13,1303', '昌黎县', '13', '河北省', '1303', '秦皇岛市', '130322', '昌黎县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130324', '1303', '00,13,1303', '卢龙县', '13', '河北省', '1303', '秦皇岛市', '130324', '卢龙县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('1304', '13', '00,13', '邯郸市', '13', '河北省', '1304', '邯郸市', '', '', '', '', '', '', 2, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130402', '1304', '00,13,1304', '邯山区', '13', '河北省', '1304', '邯郸市', '130402', '邯山区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130403', '1304', '00,13,1304', '丛台区', '13', '河北省', '1304', '邯郸市', '130403', '丛台区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130404', '1304', '00,13,1304', '复兴区', '13', '河北省', '1304', '邯郸市', '130404', '复兴区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130406', '1304', '00,13,1304', '峰峰矿区', '13', '河北省', '1304', '邯郸市', '130406', '峰峰矿区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130407', '1304', '00,13,1304', '肥乡区', '13', '河北省', '1304', '邯郸市', '130407', '肥乡区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130408', '1304', '00,13,1304', '永年区', '13', '河北省', '1304', '邯郸市', '130408', '永年区', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130423', '1304', '00,13,1304', '临漳县', '13', '河北省', '1304', '邯郸市', '130423', '临漳县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130424', '1304', '00,13,1304', '成安县', '13', '河北省', '1304', '邯郸市', '130424', '成安县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130425', '1304', '00,13,1304', '大名县', '13', '河北省', '1304', '邯郸市', '130425', '大名县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130426', '1304', '00,13,1304', '涉县', '13', '河北省', '1304', '邯郸市', '130426', '涉县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130427', '1304', '00,13,1304', '磁县', '13', '河北省', '1304', '邯郸市', '130427', '磁县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130430', '1304', '00,13,1304', '邱县', '13', '河北省', '1304', '邯郸市', '130430', '邱县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130431', '1304', '00,13,1304', '鸡泽县', '13', '河北省', '1304', '邯郸市', '130431', '鸡泽县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130432', '1304', '00,13,1304', '广平县', '13', '河北省', '1304', '邯郸市', '130432', '广平县', '', '', '', '', 3, 1, '');
insert into "CSF"."csf_region" ("code", "parent_code", "ancestors", "name", "province_code", "province_name",
                                "city_code", "city_name", "district_code", "district_name", "town_code", "town_name",
                                "village_code", "village_name", "level", "sort", "remark")
values ('130433', '1304', '00,13,1304', '馆陶县', '13', '河北省', '1304', '邯郸市', '130433', '馆陶县', '', '', '', '', 3, 1, '');

CREATE TABLE "CSF"."csf_report_file"
(
    "id"          BIGINT       NOT NULL,
    "name"        VARCHAR(100) NOT NULL,
    "content"     BLOB,
    "create_time" TIMESTAMP(0),
    "update_time" TIMESTAMP(0),
    "is_deleted"  INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_report_file" IS '报表文件表';
COMMENT ON COLUMN "CSF"."csf_report_file"."content" IS '文件内容';
COMMENT ON COLUMN "CSF"."csf_report_file"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_report_file"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_report_file"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_report_file"."name" IS '文件名';
COMMENT ON COLUMN "CSF"."csf_report_file"."update_time" IS '更新时间';


CREATE TABLE "CSF"."csf_role"
(
    "id"         BIGINT NOT NULL,
    "tenant_id"  VARCHAR(12) DEFAULT '000000',
    "parent_id"  BIGINT      DEFAULT 0,
    "role_name"  VARCHAR(255),
    "sort"       INT,
    "role_alias" VARCHAR(255),
    "is_deleted" INT         DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_role" IS '角色表';
COMMENT ON COLUMN "CSF"."csf_role"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_role"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_role"."parent_id" IS '父主键';
COMMENT ON COLUMN "CSF"."csf_role"."role_alias" IS '角色别名';
COMMENT ON COLUMN "CSF"."csf_role"."role_name" IS '角色名';
COMMENT ON COLUMN "CSF"."csf_role"."sort" IS '排序';
COMMENT ON COLUMN "CSF"."csf_role"."tenant_id" IS '租户ID';

insert into "CSF"."csf_role" ("id", "tenant_id", "parent_id", "role_name", "sort", "role_alias", "is_deleted")
values (1123598816738675201, '000000', 0, '超级管理员', 1, 'administrator', 0);
insert into "CSF"."csf_role" ("id", "tenant_id", "parent_id", "role_name", "sort", "role_alias", "is_deleted")
values (1123598816738675202, '000000', 0, '用户', 2, 'user', 0);
insert into "CSF"."csf_role" ("id", "tenant_id", "parent_id", "role_name", "sort", "role_alias", "is_deleted")
values (1590292041994137601, '000000', 0, '演示', 3, 'demo', 0);
insert into "CSF"."csf_role" ("id", "tenant_id", "parent_id", "role_name", "sort", "role_alias", "is_deleted")
values (1608338360015323137, '000000', 0, '达梦测试', 4, 'dmtr', 0);

CREATE TABLE "CSF"."csf_role_menu"
(
    "id"      BIGINT NOT NULL,
    "menu_id" BIGINT,
    "role_id" BIGINT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_role_menu" IS '角色菜单表';
COMMENT ON COLUMN "CSF"."csf_role_menu"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_role_menu"."menu_id" IS '菜单id';
COMMENT ON COLUMN "CSF"."csf_role_menu"."role_id" IS '角色id';

insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148763107330, 1123598815738675201, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148779884546, 1123598815738675202, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148788273154, 1123598815738675219, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148800856066, 1123598815738675220, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148809244674, 1123598815738675221, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148817633281, 1123598815738675222, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148826021889, 1123598815738675203, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148834410497, 1123598815738675204, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148842799105, 1123598815738675223, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148851187713, 1123598815738675224, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148872159233, 1123598815738675225, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148901519362, 1123598815738675226, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148918296578, 1123598815738675227, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148922490881, 1123598815738675228, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148926685186, 1123598815738675205, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148935073793, 1123598815738675229, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148939268098, 1123598815738675230, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148947656706, 1123598815738675231, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148951851010, 1123598815738675232, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148956045313, 1123598815738675206, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148964433922, 1123598815738675233, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148968628226, 1123598815738675234, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148972822530, 1123598815738675235, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148981211138, 1123598815738675236, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878148997988354, 1123598815738675207, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149002182658, 1123598815738675237, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149010571266, 1123598815738675238, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149018959873, 1123598815738675239, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149023154178, 1123598815738675240, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149031542786, 1123598815738675209, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149039931393, 1123598815738675245, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149044125698, 1123598815738675246, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149052514305, 1123598815738675247, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149056708610, 1123598815738675248, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149065097217, 1123598815738675256, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149069291522, 1123598815738675257, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149077680130, 1123598815738675258, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149081874434, 1123598815738675259, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149090263041, 1123598815738675260, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149094457345, 1123598815738675261, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149111234562, 1123598815738675262, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149128011777, 1123598815738675263, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149132206081, 1123598815738675264, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149140594689, 1123598815738675265, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149144788993, 1164733389668962251, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149153177602, 1164733389668962252, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149161566209, 1164733389668962253, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149165760514, 1164733389668962254, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149174149121, 1164733389668962255, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149178343426, 1123598815738675210, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149182537730, 1123598815738675211, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149190926337, 1123598815738675212, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149195120642, 1123598815738675213, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149203509249, 1123598815738675214, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149207703553, 1123598815738675249, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149211897857, 1123598815738675215, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149220286466, 1123598815738675250, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149224480770, 1123598815738675216, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149232869378, 1123598815738675251, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149237063681, 1123598815738675217, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149241257986, 1123598815738675218, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149249646593, 1123598815738675252, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149253840898, 1123598815738675253, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149262229506, 1123598815738675254, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149270618114, 1123598815738675255, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149279006721, 1123598815738675266, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149287395329, 1123598815738675267, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149299978242, 1123598815738675268, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149308366850, 1123598815738675269, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149312561154, 1123598815738675270, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149316755458, 1123598815738675307, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149325144066, 1123598815738675308, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149329338369, 1123598815738675241, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149337726977, 1123598815738675242, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149341921281, 1123598815738675243, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149350309889, 1123598815738675244, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149354504193, 1123598815738675309, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149362892802, 1123598815738675310, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149367087106, 1518489311171887106, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149379670018, 1164733399668962201, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149388058625, 1164733399668962202, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149396447234, 1164733399668962203, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149400641538, 1164733399668962204, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149409030146, 1164733399668962205, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149413224449, 1164733399668962206, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149417418754, 1164733399668962207, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149425807362, 1164733399669962301, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149430001666, 1164733399669962302, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1518878149438390273, 1164733399669962303, 1123598816738675201);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661232807938, 1123598815738675201, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661249585153, 1123598815738675202, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661262168065, 1123598815738675219, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661270556674, 1123598815738675220, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661283139585, 1123598815738675221, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1573200661291528193, 1123598815738675222, 1123598816738675202);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1608337596790407170, 1123598815738675201, 1590292041994137601);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1608337596815572993, 1123598815738675202, 1590292041994137601);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1608337596832350210, 1123598815738675219, 1590292041994137601);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1608337596844933121, 1123598815738675220, 1590292041994137601);
insert into "CSF"."csf_role_menu" ("id", "menu_id", "role_id")
values (1608337596853321730, 1123598815738675221, 1590292041994137601);

CREATE TABLE "CSF"."csf_role_scope"
(
    "id"             BIGINT NOT NULL,
    "scope_id"       BIGINT,
    "role_id"        BIGINT,
    "scope_category" INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON COLUMN "CSF"."csf_role_scope"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_role_scope"."role_id" IS '角色id';
COMMENT ON COLUMN "CSF"."csf_role_scope"."scope_category" IS '权限类型(1:数据权限、2:接口权限)';
COMMENT ON COLUMN "CSF"."csf_role_scope"."scope_id" IS '数据权限id';


CREATE TABLE "CSF"."csf_scope_api"
(
    "id"            BIGINT NOT NULL,
    "menu_id"       BIGINT,
    "resource_code" VARCHAR(255),
    "scope_name"    VARCHAR(255),
    "scope_path"    VARCHAR(255),
    "scope_type"    INT,
    "remark"        VARCHAR(255),
    "create_user"   BIGINT,
    "create_dept"   BIGINT,
    "create_time"   TIMESTAMP(0),
    "update_user"   BIGINT,
    "update_time"   TIMESTAMP(0),
    "status"        INT,
    "is_deleted"    INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_scope_api" IS '接口权限表';
COMMENT ON COLUMN "CSF"."csf_scope_api"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_scope_api"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_scope_api"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_scope_api"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_scope_api"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_scope_api"."menu_id" IS '菜单主键';
COMMENT ON COLUMN "CSF"."csf_scope_api"."remark" IS '接口权限备注';
COMMENT ON COLUMN "CSF"."csf_scope_api"."resource_code" IS '资源编号';
COMMENT ON COLUMN "CSF"."csf_scope_api"."scope_name" IS '接口权限名';
COMMENT ON COLUMN "CSF"."csf_scope_api"."scope_path" IS '接口权限地址';
COMMENT ON COLUMN "CSF"."csf_scope_api"."scope_type" IS '接口权限类型';
COMMENT ON COLUMN "CSF"."csf_scope_api"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_scope_api"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_scope_api"."update_user" IS '修改人';


CREATE TABLE "CSF"."csf_scope_data"
(
    "id"            BIGINT NOT NULL,
    "menu_id"       BIGINT,
    "resource_code" VARCHAR(255),
    "scope_name"    VARCHAR(255),
    "scope_field"   VARCHAR(255),
    "scope_class"   VARCHAR(500),
    "scope_column"  VARCHAR(255),
    "scope_type"    INT,
    "scope_value"   VARCHAR(2000),
    "remark"        VARCHAR(255),
    "create_user"   BIGINT,
    "create_dept"   BIGINT,
    "create_time"   TIMESTAMP(0),
    "update_user"   BIGINT,
    "update_time"   TIMESTAMP(0),
    "status"        INT,
    "is_deleted"    INT,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_scope_data" IS '数据权限表';
COMMENT ON COLUMN "CSF"."csf_scope_data"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_scope_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_scope_data"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_scope_data"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_scope_data"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_scope_data"."menu_id" IS '菜单主键';
COMMENT ON COLUMN "CSF"."csf_scope_data"."remark" IS '数据权限备注';
COMMENT ON COLUMN "CSF"."csf_scope_data"."resource_code" IS '资源编号';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_class" IS '数据权限类名';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_column" IS '数据权限字段';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_field" IS '数据权限字段';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_name" IS '数据权限名称';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_type" IS '数据权限类型';
COMMENT ON COLUMN "CSF"."csf_scope_data"."scope_value" IS '数据权限值域';
COMMENT ON COLUMN "CSF"."csf_scope_data"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_scope_data"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_scope_data"."update_user" IS '修改人';


CREATE TABLE "CSF"."csf_tenant"
(
    "id"             BIGINT      NOT NULL,
    "tenant_id"      VARCHAR(12) NOT NULL,
    "tenant_name"    VARCHAR(50) NOT NULL,
    "domain"         VARCHAR(255),
    "linkman"        VARCHAR(20),
    "contact_number" VARCHAR(20),
    "address"        VARCHAR(255),
    "create_user"    BIGINT,
    "create_dept"    BIGINT,
    "create_time"    TIMESTAMP(0),
    "update_user"    BIGINT,
    "update_time"    TIMESTAMP(0),
    "status"         INT,
    "is_deleted"     INT DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_tenant" IS '租户表';
COMMENT ON COLUMN "CSF"."csf_tenant"."address" IS '联系地址';
COMMENT ON COLUMN "CSF"."csf_tenant"."contact_number" IS '联系电话';
COMMENT ON COLUMN "CSF"."csf_tenant"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_tenant"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_tenant"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_tenant"."domain" IS '域名地址';
COMMENT ON COLUMN "CSF"."csf_tenant"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_tenant"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_tenant"."linkman" IS '联系人';
COMMENT ON COLUMN "CSF"."csf_tenant"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_tenant"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_tenant"."tenant_name" IS '租户名称';
COMMENT ON COLUMN "CSF"."csf_tenant"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_tenant"."update_user" IS '修改人';

insert into "CSF"."csf_tenant" ("id", "tenant_id", "tenant_name", "domain", "linkman", "contact_number", "address",
                                "create_user", "create_dept", "create_time", "update_user", "update_time", "status",
                                "is_deleted")
values (1123598820738675201, '000000', '管理组', '', 'admin', '666666', '管理组', 1123598821738675201, 1123598813738675201,
        '2019-01-01 00:00:39', 1123598821738675201, '2019-01-01 00:00:39', 1, 0);

CREATE TABLE "CSF"."csf_user"
(
    "id"          BIGINT NOT NULL,
    "tenant_id"   VARCHAR(12) DEFAULT '000000',
    "code"        VARCHAR(12),
    "account"     VARCHAR(45),
    "password"    VARCHAR(45),
    "name"        VARCHAR(20),
    "real_name"   VARCHAR(10),
    "avatar"      VARCHAR(2000),
    "email"       VARCHAR(45),
    "phone"       VARCHAR(45),
    "birthday"    TIMESTAMP(0),
    "sex"         SMALLINT,
    "role_id"     VARCHAR(1000),
    "dept_id"     VARCHAR(1000),
    "post_id"     VARCHAR(1000),
    "create_user" BIGINT,
    "create_dept" BIGINT,
    "create_time" TIMESTAMP(0),
    "update_user" BIGINT,
    "update_time" TIMESTAMP(0),
    "status"      INT,
    "is_deleted"  INT         DEFAULT 0,
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_user" IS '用户表';
COMMENT ON COLUMN "CSF"."csf_user"."account" IS '账号';
COMMENT ON COLUMN "CSF"."csf_user"."avatar" IS '头像';
COMMENT ON COLUMN "CSF"."csf_user"."birthday" IS '生日';
COMMENT ON COLUMN "CSF"."csf_user"."code" IS '用户编号';
COMMENT ON COLUMN "CSF"."csf_user"."create_dept" IS '创建部门';
COMMENT ON COLUMN "CSF"."csf_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "CSF"."csf_user"."create_user" IS '创建人';
COMMENT ON COLUMN "CSF"."csf_user"."dept_id" IS '部门id';
COMMENT ON COLUMN "CSF"."csf_user"."email" IS '邮箱';
COMMENT ON COLUMN "CSF"."csf_user"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_user"."is_deleted" IS '是否已删除';
COMMENT ON COLUMN "CSF"."csf_user"."name" IS '昵称';
COMMENT ON COLUMN "CSF"."csf_user"."password" IS '密码';
COMMENT ON COLUMN "CSF"."csf_user"."phone" IS '手机';
COMMENT ON COLUMN "CSF"."csf_user"."post_id" IS '岗位id';
COMMENT ON COLUMN "CSF"."csf_user"."real_name" IS '真名';
COMMENT ON COLUMN "CSF"."csf_user"."role_id" IS '角色id';
COMMENT ON COLUMN "CSF"."csf_user"."sex" IS '性别';
COMMENT ON COLUMN "CSF"."csf_user"."status" IS '状态';
COMMENT ON COLUMN "CSF"."csf_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_user"."update_time" IS '修改时间';
COMMENT ON COLUMN "CSF"."csf_user"."update_user" IS '修改人';

insert into "CSF"."csf_user" ("id", "tenant_id", "code", "account", "password", "name", "real_name", "avatar", "email",
                              "phone", "birthday", "sex", "role_id", "dept_id", "post_id", "create_user", "create_dept",
                              "create_time", "update_user", "update_time", "status", "is_deleted")
values (1123598821738675201, '000000', null, 'admin', 'fd71186c42933093f50d0a702230e6fb6082da0d', '管理员', '管理员', '',
        '<EMAIL>', '********', '2018-08-08', 1, '1123598816738675201', '1123598813738675201',
        '1123598817738675201', 1123598821738675201, 1123598813738675201, '2018-08-08', 1123598821738675201,
        '2022-06-13 10:58:47', 1, 0);
insert into "CSF"."csf_user" ("id", "tenant_id", "code", "account", "password", "name", "real_name", "avatar", "email",
                              "phone", "birthday", "sex", "role_id", "dept_id", "post_id", "create_user", "create_dept",
                              "create_time", "update_user", "update_time", "status", "is_deleted")
values (1590292495971450881, '000000', null, 'demo', 'f0ddb0eb7b5a03c9f94030eab13477a6bb9fa728', 'demo', 'demo', null,
        null, null, null, null, '1590292041994137601', '1123598813738675201', '1123598817738675206',
        1123598821738675201, null, '2022-11-09 18:37:32', 1123598821738675201, '2022-11-09 18:37:32', 1, 0);
insert into "CSF"."csf_user" ("id", "tenant_id", "code", "account", "password", "name", "real_name", "avatar", "email",
                              "phone", "birthday", "sex", "role_id", "dept_id", "post_id", "create_user", "create_dept",
                              "create_time", "update_user", "update_time", "status", "is_deleted")
values (1608338208152158209, '000000', null, 'dmtest', '352db31a62fb53311758de8544a40fa74169108c', 'dm', '达梦', null,
        null, null, null, -1, '1608338360015323137', '1123598813738675201', '1123598817738675208', 1123598821738675201,
        null, '2022-12-29 13:44:45', 1123598821738675201, '2022-12-29 13:45:40', 1, 0);

CREATE TABLE "CSF"."csf_user_oauth"
(
    "id"        BIGINT NOT NULL,
    "tenant_id" VARCHAR(12),
    "uuid"      VARCHAR(64),
    "user_id"   BIGINT,
    "username"  VARCHAR(32),
    "nickname"  VARCHAR(64),
    "avatar"    VARCHAR(1000),
    "blog"      VARCHAR(50),
    "company"   VARCHAR(255),
    "location"  VARCHAR(255),
    "email"     VARCHAR(255),
    "remark"    VARCHAR(255),
    "gender"    VARCHAR(16),
    "source"    VARCHAR(16),
    NOT CLUSTER PRIMARY KEY ("id")
) STORAGE(ON "TAB_CSF", CLUSTERBTR);

COMMENT ON TABLE "CSF"."csf_user_oauth" IS '用户第三方认证表';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."avatar" IS '头像';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."blog" IS '应用主页';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."company" IS '公司名';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."email" IS '邮件';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."gender" IS '性别';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."id" IS '主键';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."location" IS '地址';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."nickname" IS '用户名';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."remark" IS '备注';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."source" IS '来源';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."user_id" IS '用户ID';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."username" IS '账号';
COMMENT ON COLUMN "CSF"."csf_user_oauth"."uuid" IS '第三方系统用户ID';
