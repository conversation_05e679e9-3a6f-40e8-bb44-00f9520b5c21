<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.mapper.KnowledgeTestHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.entity.KnowledgeTestHistory">
        <id column="id" property="id" />
        <result column="question" property="question" />
        <result column="knowledge_base_id" property="knowledgeBaseId" />
        <result column="execution_time_ms" property="executionTimeMs" />
        <result column="result_count" property="resultCount" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 测试历史列表查询结果映射 -->
    <resultMap id="TestHistoryListResultMap" type="com.chinaunicom.ai.knowledge.vo.KnowledgeTestHistoryListVO">
        <id column="id" property="id" />
        <result column="question" property="question" />
        <result column="second" property="second" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 分页查询知识库检索测试历史列表 -->
    <select id="selectTestHistoryList" resultMap="TestHistoryListResultMap">
        SELECT
            h.id,
            h.question,
            ROUND(h.execution_time_ms / 1000.0, 3) AS second,
            DATE_FORMAT(h.create_time, '%Y-%m-%dT%H:%i:%s') AS create_time
        FROM knowledge_test_history h
        WHERE h.is_deleted = 0
          AND h.tenant_id = #{tenantId}
          AND h.knowledge_base_id = #{knowledgeBaseId}
        <if test="questionKeyword != null and questionKeyword != ''">
            AND h.question LIKE CONCAT('%', #{questionKeyword}, '%')
        </if>
        ORDER BY h.create_time DESC
    </select>

</mapper>
