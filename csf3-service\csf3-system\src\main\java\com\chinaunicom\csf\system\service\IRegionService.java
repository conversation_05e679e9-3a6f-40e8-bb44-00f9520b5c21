
package com.chinaunicom.csf.system.service;

import com.chinaunicom.csf.core.mp.base.BaseService;
import com.chinaunicom.csf.core.tool.node.INode;
import com.chinaunicom.csf.system.entity.Region;

import java.util.List;
import java.util.Map;

/**
 * 行政区划表 服务类
 *
 */
public interface IRegionService extends BaseService<Region> {

	/**
	 * 提交
	 *
	 * @param region
	 * @return
	 */
	boolean submit(Region region);

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	boolean removeRegion(String id);

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<INode> lazyList(String parentCode, Map<String, Object> param);

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<INode> lazyTree(String parentCode, Map<String, Object> param);

}
