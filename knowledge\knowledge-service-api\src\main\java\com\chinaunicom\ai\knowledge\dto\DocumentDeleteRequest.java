package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 文档批量删除请求DTO
 */
@Data
@Schema(description = "文档批量删除请求DTO")
public class DocumentDeleteRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "文档ID列表不能为空")
    @Schema(description = "要删除的文档ID列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "[101, 102]")
    private List<Long> documentIds;
}
