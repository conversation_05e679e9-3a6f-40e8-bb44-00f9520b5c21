
package com.chinaunicom.csf.resource.endpoint;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.plugins.oss.OssTemplate;
import com.chinaunicom.csf.plugins.oss.model.CsfFile;
import com.chinaunicom.csf.plugins.oss.model.OssFile;
import com.chinaunicom.csf.resource.entity.Attachment;
import com.chinaunicom.csf.resource.service.IAttachmentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 对象存储端点
 *
 */
@RestController
@AllArgsConstructor
@RequestMapping("/oss/endpoint")
@Tag(name = "对象存储端点", description = "对象存储端点")
public class OssEndpoint {

	@Autowired
	private OssTemplate ossTemplate;

	private IAttachmentService attachmentService;

	/**
	 * 创建存储桶
	 *
	 * @param bucketName 存储桶名称
	 * @return Bucket
	 */
	@SneakyThrows
	@PostMapping("/make-bucket")
	public R makeBucket(@RequestParam String bucketName) {
		ossTemplate.makeBucket(bucketName);
		return R.success("创建成功");
	}

	/**
	 * 创建存储桶
	 *
	 * @param bucketName 存储桶名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-bucket")
	public R removeBucket(@RequestParam String bucketName) {
		ossTemplate.removeBucket(bucketName);
		return R.success("删除成功");
	}

	/**
	 * 拷贝文件
	 *
	 * @param fileName       存储桶对象名称
	 * @param destBucketName 目标存储桶名称
	 * @param destFileName   目标存储桶对象名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/copy-file")
	public R copyFile(@RequestParam String fileName, @RequestParam String destBucketName, String destFileName) {
		ossTemplate.copyFile(fileName, destBucketName, destFileName);
		return R.success("操作成功");
	}

	/**
	 * 获取文件信息
	 *
	 * @param fileName 存储桶对象名称
	 * @return InputStream
	 */
	@SneakyThrows
	@GetMapping("/stat-file")
	public R<OssFile> statFile(@RequestParam String fileName) {
		return R.data(ossTemplate.statFile(fileName));
	}

	/**
	 * 获取文件相对路径
	 *
	 * @param fileName 存储桶对象名称
	 * @return String
	 */
	@SneakyThrows
	@GetMapping("/file-path")
	public R<String> filePath(@RequestParam String fileName) {
		return R.data(ossTemplate.filePath(fileName));
	}


	/**
	 * 获取文件外链
	 *
	 * @param fileName 存储桶对象名称
	 * @return String
	 */
	@SneakyThrows
	@GetMapping("/file-link")
	public R<String> fileLink(@RequestParam String fileName) {
		return R.data(ossTemplate.fileLink(fileName));
	}

	/**
	 * 上传文件
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file")
	public R<CsfFile> putFile(@RequestParam MultipartFile file) {
		return this.putFile(file.getOriginalFilename(), file);
	}

	/**
	 * 上传文件
	 *
	 * @param fileName 存储桶对象名称
	 * @param file     文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-by-name")
	public R<CsfFile> putFile(@RequestParam String fileName, @RequestParam MultipartFile file) {
		CsfFile csfFile = ossTemplate.putFile(fileName, file.getInputStream());
		Attachment attachment = new Attachment();
		attachment.setName(csfFile.getName());
		attachment.setOriginalName(csfFile.getOriginalName());
		attachment.setLink(csfFile.getLink());
		attachmentService.save(attachment);
		return R.data(csfFile);
	}

	/**
	 * 删除文件
	 *
	 * @param fileName 存储桶对象名称
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-file")
	public R removeFile(@RequestParam String fileName) {
		LambdaQueryWrapper<Attachment> wrapper = new QueryWrapper<Attachment>().lambda().eq(Attachment::getName, fileName);
		attachmentService.remove(wrapper);
		ossTemplate.removeFile(fileName);
		return R.success("操作成功");
	}

	/**
	 * 批量删除文件
	 *
	 * @param fileNames 存储桶对象名称集合
	 * @return R
	 */
	@SneakyThrows
	@PostMapping("/remove-files")
	public R removeFiles(@RequestParam String fileNames) {
		LambdaQueryWrapper<Attachment> wrapper = new QueryWrapper<Attachment>().lambda().in(Attachment::getName, fileNames);
		attachmentService.remove(wrapper);
		ossTemplate.removeFiles(Func.toStrList(fileNames));
		return R.success("操作成功");
	}

}
