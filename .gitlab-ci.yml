include:
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1909.yml"
  local: .gitlab-ci-template/1909.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1911.yml"
  local: .gitlab-ci-template/1911.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1912.yml"
  local: .gitlab-ci-template/1912.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1913.yml"
  local: .gitlab-ci-template/1913.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1914.yml"
  local: .gitlab-ci-template/1914.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1915.yml"
  local: .gitlab-ci-template/1915.yml
- rules:
  - if: $DEVOPS_PIPELINE_ID_FILE == "1934.yml"
  local: .gitlab-ci-template/1934.yml
variables:
  MAVEN_OPTS: -Dmaven.repo.local=.m2/repository
  TEST_PLAN_WEBHOOK_URL: $CI_SERVICE_URL/webhook/gitlabProjects/$CI_PROJECT_ID/pipelines/$CI_PIPELINE_ID/test/plans/exec
  APPROVE_WEBHOOK_URL: $CI_SERVICE_URL/webhook/approvePending
  MAVEN_CLI_OPTS: -s .m2/settings.xml --batch-mode
  DOCKER_TLS_CERTDIR: ''
  imageTag: $CI_PROJECT_NAME-$DEPLOY_TIME_TAG-$CI_PIPELINE_ID
  GIT_SUBMODULE_STRATEGY: recursive
  COVERAGE_WEBHOOK_URL: $COVERAGE_WEBHOOK_URI?branch=$CI_COMMIT_REF_NAME&gitlabPipelineId=$CI_PIPELINE_ID&gitlabProjectId=$CI_PROJECT_ID
  IMAGE_WEBHOOK_URL: $CI_SERVICE_URL/webhook/gitlabProjects/$CI_PROJECT_ID/pipelines/$CI_PIPELINE_ID/images
stages:
- start
- code_check
- push_images
- deploy_approve
- deploy
- test
start:
  only:
    variables:
    - $START == "1"
  stage: start
  image: harbor.uniin.cn/devops-ci/maven:3.8.1-openjdk-17-ry
  script:
  - echo "start"
  - echo $DEVOPS_PIPELINE_ID_FILE
