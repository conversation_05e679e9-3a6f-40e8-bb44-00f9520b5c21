import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import com.chinaunicom.csf.core.test.CsfBootTest;
import com.chinaunicom.csf.core.test.CsfSpringExtension;
import com.chinaunicom.csf.desk.DeskApplication;
import com.chinaunicom.csf.desk.service.INoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Csf单元测试
 *
 */
@ExtendWith(CsfSpringExtension.class)
@SpringBootTest(classes = DeskApplication.class)
@CsfBootTest(appName = "csf-desk", profile = "test", enableLoader = true)
public class CsfDemoTest {

	@Autowired
	private INoticeService noticeService;

	@Test
	public void contextLoads() {
		Long count = noticeService.count();
		System.out.println("notice数量：[" + count + "] 个");
	}

}
