package com.chinaunicom.csf.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 存储大模型表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent_llms")
@Schema(description="存储大模型表")
public class AgentLlms implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模型 ID，自增主键，唯一标识大模型记录")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "模型名称，例如：GPT-4、Claude-2")
    @TableField("name")
    private String name;

    @Schema(description = "模型提供商，例如：OpenAI、HuggingFace、百度文心")
    @TableField("provider")
    private String provider;

    @Schema(description = "访问模型的 API 密钥（敏感信息，建议加密存储）")
    @TableField("api_key")
    private String apiKey;

    @Schema(description = "模型 API 的访问端点 URL，例如：https://api.openai.com/v1")
    @TableField("endpoint")
    private String endpoint;

    @Schema(description = "模型类型，例如：chat（对话模型）、embedding（向量化模型）")
    @TableField("model_type")
    private String modelType;

    @Schema(description = "模型创建时间，自动填充当前时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @Schema(description = "模型信息更新时间，修改时自动更新")
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    @Schema(description = "是否公开模型：0=私有（仅创建者可见），1=公开（系统内共享）")
    @TableField("is_public")
    private Boolean isPublic;

    @Schema(description = "创建模型的用户 ID，关联用户表（csf_user.id）")
    @TableField("create_user")
    private Integer createUser;


}
