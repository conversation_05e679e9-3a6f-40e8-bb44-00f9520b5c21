server:
  port: 8899
spring:
  cloud:
    stream:
      # 默认的绑定器
      default-binder: kafka
      # kafka配置
      kafka:
        binder:
          # Kafka服务器地址
          brokers:
            - 192.168.56.103:9092
          # 是否自动创建消息主题
          auto-create-topics: true
      bindings:
        # 定义一个名为 csfMsgTest-out-0 的通道，命名规则 <functionName> + -in\out- + <index>
        # out表示生产者；0表示是输入输出的结合索引，在具有多个输入输出的函数里可能为非0，单个就始终为0
        csfMsgTest-in-0:
          # 通道目的地: csfMsgTest-topic 主题
          destination: csfMsgTest-topic

