
package com.chinaunicom.csf.auth.controller;

import com.chinaunicom.csf.auth.enums.LogoutEnum;
import com.chinaunicom.csf.auth.granter.ITokenGranter;
import com.chinaunicom.csf.auth.granter.TokenGranterBuilder;
import com.chinaunicom.csf.auth.granter.TokenParameter;
import com.chinaunicom.csf.auth.utils.TokenUtil;
import com.chinaunicom.csf.common.cache.CacheNames;
import com.chinaunicom.csf.core.jwt.JwtUtil;
import com.chinaunicom.csf.core.launch.constant.TokenConstant;
import com.chinaunicom.csf.core.secure.AuthInfo;
import com.chinaunicom.csf.core.secure.CsfUser;
import com.chinaunicom.csf.core.secure.annotation.PreAuth;
import com.chinaunicom.csf.core.secure.exception.SecureException;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.api.ResultCode;
import com.chinaunicom.csf.core.tool.support.Kv;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.core.tool.utils.RedisUtil;
import com.chinaunicom.csf.core.tool.utils.StringUtil;
import com.chinaunicom.csf.core.tool.utils.WebUtil;
import com.chinaunicom.csf.system.user.dto.LoginDto;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.wf.captcha.SpecCaptcha;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 认证模块
 */
@RestController
@Tag(description = "用户授权认证", name = "授权接口")
public class AuthController {

	@Autowired
	private RedisUtil redisUtil;
	@Value("${csf.auth.max-login-fail-count:5}")
	private Integer maxLoginFailCount;
	@Value("${csf.auth.unlock-min:10}")
	private Integer unlockMin;
	@Value("${csf.auth.enable-account-lock:true}")
	private boolean enableAccountLock;


	@Value("${csf.auth.enable-password-modified:false}")
	private boolean enablePasswordModified;

	@Value("${csf.auth.password-effective-days:30}")
	private Integer passwordEffectiveDays;

	@Value("${csf.jwt.state:false}")
	private boolean state;

	@Value("${csf.data-security.decryptor.public-key:undefined}")
	private String rsaPublicKey;

	@PostMapping("token")
	@Operation(summary = "获取认证token(旧版)",description = "传入租户ID:tenantId,账号:account,密码:password")
	@Deprecated
	public R<AuthInfo> token(@Parameter(description = "授权类型", required = true) @RequestParam(defaultValue = "captcha", required = false) String grantType,
							 @Parameter(description = "刷新令牌") @RequestParam(required = false) String refreshToken,
							 @Parameter(description = "租户ID", required = true) @RequestParam(defaultValue = "000000", required = false) String tenantId,
							 @Parameter(description = "账号") @RequestParam(required = false) String account,
							 @Parameter(description = "密码") @RequestParam(required = false) String password) {

		if (StringUtil.isBlank(grantType)) {
			throw new SecureException("grantType param can not empty");
		}
		String accountLockedKey = tenantId + "-" + account + "-" + "LOGIN_FAIL_COUNT";
		Integer loginFailCount = null;
		if (enableAccountLock) {
			Object res = redisUtil.get(accountLockedKey);
			if (res != null) {
				loginFailCount = (Integer) res;
				if (loginFailCount >= maxLoginFailCount) {
					return R.fail(TokenUtil.USER_LOCKED + ",请" + unlockMin + "分钟后再试");
				}
			}
		}

		String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);

		TokenParameter tokenParameter = new TokenParameter();
		tokenParameter.getArgs().set("tenantId", tenantId)
			.set("account", account)
			.set("password", password)
			.set("grantType", grantType)
			.set("refreshToken", refreshToken)
			.set("userType", userType);

		ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
		UserInfo userInfo = granter.grant(tokenParameter);
		userInfo.setUserType(userType);
		if (userInfo == null || userInfo.getUser() == null || userInfo.getUser().getId() == null) {
			if (enableAccountLock) {
				if (loginFailCount == null) {
					redisUtil.set(accountLockedKey, 1, unlockMin, TimeUnit.MINUTES);
				} else {
					redisUtil.set(accountLockedKey, ++loginFailCount, unlockMin, TimeUnit.MINUTES);
					if (loginFailCount >= maxLoginFailCount) {
						return R.fail(TokenUtil.USER_LOCKED + ",请" + unlockMin + "分钟后再试");
					}
				}
			}
			return R.fail(TokenUtil.USER_NOT_FOUND);
		}

		if(isPasswordExpired(userInfo)) {
			return R.fail(403, "用户密码已超过"+ passwordEffectiveDays +"天未修改，请修改密码再登录");
		}

		if (enableAccountLock) {
			redisUtil.del(accountLockedKey);
		}

		return R.data(TokenUtil.createAuthInfo(userInfo));
	}

	@PostMapping("token2")
	@Operation(summary = "获取认证token(新版)",description = "传入租户ID:tenantId,账号:account,密码:password")
	public R<AuthInfo> token2(@RequestBody LoginDto dto)  {
		System.out.println(dto.toString());
		if (StringUtil.isBlank(dto.getGrantType())) {
			throw new SecureException("grantType param can not empty");
		}
		String accountLockedKey = dto.getTenantId() + "-" + dto.getAccount() + "-" + "LOGIN_FAIL_COUNT";
		Integer loginFailCount = null;
		if (enableAccountLock) {
			Object res = redisUtil.get(accountLockedKey);
			if (res != null) {
				loginFailCount = (Integer) res;
				if (loginFailCount >= maxLoginFailCount) {
					return R.fail(TokenUtil.USER_LOCKED + ",请" + unlockMin + "分钟后再试");
				}
			}
		}

		String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);

		TokenParameter tokenParameter = new TokenParameter();
		tokenParameter.getArgs().set("tenantId", dto.getTenantId())
			.set("account", dto.getAccount())
			.set("password", dto.getPassword())
			.set("grantType", dto.getGrantType())
			.set("refreshToken", dto.getRefreshToken())
			.set("userType", userType);

		ITokenGranter granter = TokenGranterBuilder.getGranter(dto.getGrantType());
		UserInfo userInfo = granter.grant(tokenParameter);
		userInfo.setUserType(userType);
		if (userInfo == null || userInfo.getUser() == null || userInfo.getUser().getId() == null) {
			if (enableAccountLock) {
				if (loginFailCount == null) {
					redisUtil.set(accountLockedKey, 1, unlockMin, TimeUnit.MINUTES);
				} else {
					redisUtil.set(accountLockedKey, ++loginFailCount, unlockMin, TimeUnit.MINUTES);
					if (loginFailCount >= maxLoginFailCount) {
						return R.fail(TokenUtil.USER_LOCKED + ",请" + unlockMin + "分钟后再试");
					}
				}
			}
			return R.fail(TokenUtil.USER_NOT_FOUND);
		}

		if(isPasswordExpired(userInfo)) {
			return R.fail(403, "用户密码已超过"+ passwordEffectiveDays +"天未修改，请修改密码再登录");
		}

		if (enableAccountLock) {
			redisUtil.del(accountLockedKey);
		}

		return R.data(TokenUtil.createAuthInfo(userInfo));
	}

	private boolean isPasswordExpired(UserInfo userInfo) {
		// 判断密码是否过期
		if (enablePasswordModified && Objects.nonNull(userInfo.getUser().getPasswordLastModifiedTime())) {
			Date givenDate = userInfo.getUser().getPasswordLastModifiedTime();
			Instant now = Instant.now();
			Instant givenInstant = givenDate.toInstant();
			long daysBetween = ChronoUnit.DAYS.between(givenInstant, now);
			if (daysBetween > passwordEffectiveDays) {
				return true;
			}
		}
		return false;
	}

	@GetMapping("/captcha")
	@Operation(summary = "获取验证码")
	public R<Kv> captcha() {
		String captchaKey = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), "");
		SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
		String verCode = specCaptcha.text().toLowerCase();
		String key = UUID.randomUUID().toString();
		// 存入redis并设置过期时间为2分钟
		redisUtil.set(CacheNames.CAPTCHA_KEY + key, verCode, 2L, TimeUnit.MINUTES);
		// 刷新验证码，删除旧的验证
		redisUtil.del(CacheNames.CAPTCHA_KEY + captchaKey);
		// 将key和base64返回给前端
		return R.data(Kv.init().set("key", key).set("image", specCaptcha.toBase64()));
	}

	@GetMapping("/getRsaCode")
	@Operation(summary = "获取rsa公钥")
	public R<String> getRsaCode() {
		return R.data(rsaPublicKey);
	}

	@PreAuth("hasRole('administrator')")
	@PutMapping("/unlock")
	@Operation(summary = "解锁用户")
	public R unlock(
		@Parameter(description = "租户ID", required = true) @RequestParam(defaultValue = "000000", required = false) String tenantId,
		@Parameter(description = "账号") @RequestParam(required = false) String account) {
		String accountLockedKey = tenantId + "-" + account + "-" + "LOGIN_FAIL_COUNT";
		redisUtil.del(accountLockedKey);
		return R.success(ResultCode.SUCCESS);
	}

	@GetMapping("/token/logout")
	@Operation(summary = "登出")
	public R logout(@Parameter(description = "退出类型，默认退出token") @RequestParam(required = false) LogoutEnum logoutType) {
		// 用户登出功能，当csf.jwt.state为true时，表示用户登录生成的Token是有状态的，才需要登出
		if(this.state) {
			Claims claims = SecureUtil.getClaims(Objects.requireNonNull(WebUtil.getRequest()));
			CsfUser csfUser = SecureUtil.getUser(claims);
			Assert.notNull(csfUser, "用户数据为空");

			String tenantId = csfUser.getTenantId();
			String userId = String.valueOf(csfUser.getUserId());
			String userType = claims.get(TokenConstant.USER_TYPE) == null ? TokenUtil.DEFAULT_USER_TYPE : claims.get(TokenConstant.USER_TYPE).toString();
			if (LogoutEnum.USER == logoutType) {
				JwtUtil.removeAccessToken(tenantId, userId);
			} else if (LogoutEnum.SOURCE == logoutType) {
				JwtUtil.removeAccessToken(tenantId, userId, userType);
			} else {
				JwtUtil.removeAccessToken(tenantId, userId, userType, claims.getId());
			}
		}
		return R.success(ResultCode.SUCCESS);
	}

}
