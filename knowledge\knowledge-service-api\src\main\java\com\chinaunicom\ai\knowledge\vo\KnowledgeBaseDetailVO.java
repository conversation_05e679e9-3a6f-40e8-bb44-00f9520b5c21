package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 知识库详情VO
 */
@Data
@Schema(description = "知识库详情VO")
public class KnowledgeBaseDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "知识库描述", example = "这是一个关于人工智能的知识库。")
    private String descrip; // 将 'desc' 更改为 'descrip'

    @Schema(description = "关联模型ID")
    private Long vecModel;

    @Schema(description = "关联模型名称")
    private String vecModelName;
}