package com.chinaunicom.csf.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 文件上传请求DTO
 */
@Data
public class FileUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型 (例如: 'avatar', 'document')
     */
    private String bizType;

    /**
     * 上传的文件
     */
    private MultipartFile file;
} 