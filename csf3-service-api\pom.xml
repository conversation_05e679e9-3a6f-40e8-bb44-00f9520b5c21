<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>csf3</artifactId>
        <groupId>com.chinaunicom.csf</groupId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3-service-api</artifactId>
    <name>${project.artifactId}</name>
    <version>1.8.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <description>csf 微服务API集合</description>

    <modules>
        <module>csf3-desk-api</module>
        <module>csf3-dict-api</module>
        <module>csf3-system-api</module>
        <module>csf3-user-api</module>
        <module>csf3-scope-api</module>
        <module>csf3-audit-api</module>
        <module>csf3-demo-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>

</project>
