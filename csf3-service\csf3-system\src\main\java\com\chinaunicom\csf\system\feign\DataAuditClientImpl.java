package com.chinaunicom.csf.system.feign;

import com.chinaunicom.csf.plugins.datasecurity.entity.AuditLog;
import com.chinaunicom.csf.system.service.DataAuditService;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Hidden
public class DataAuditClientImpl implements DataAuditClient {

	@Autowired
	private DataAuditService dataAuditService;

	@Override
	@PostMapping("/data-audit/logs")
	public int log(@RequestBody AuditLog log) {
		return dataAuditService.log(log);
	}
}
