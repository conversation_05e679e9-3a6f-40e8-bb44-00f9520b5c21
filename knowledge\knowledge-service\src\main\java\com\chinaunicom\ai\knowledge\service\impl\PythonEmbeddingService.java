package com.chinaunicom.ai.knowledge.service.impl;

import com.chinaunicom.csf.core.log.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Python嵌入（向量化）服务调用
 * 负责将文本转换为向量
 */
@Slf4j
@Service
public class PythonEmbeddingService {

    @Value("${python.embedding.service.url}") // Nacos配置的Python向量化服务URL
    private String pythonEmbeddingServiceUrl;

    @Autowired
    private WebClient.Builder webClientBuilder;

    /**
     * 调用Python接口将问题文本向量化
     * @param queryText 问题文本
     * @param embedModelName 嵌入模型名称
     * @return 向量数组 (float[])
     * @throws ServiceException 如果Python服务调用失败或返回非预期结果
     */
    public float[] vectorizeText(String queryText, String embedModelName) { // 增加 embedModelName 参数
        // 构建请求体
        Map<String, Object> queryTar = new HashMap<>();
        queryTar.put("query_text", queryText);
        queryTar.put("query_embed_model", embedModelName); // 添加嵌入模型名称

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("queryTar", queryTar);

        log.info("调用Python向量化服务：{}", pythonEmbeddingServiceUrl + "/v1/query/query_parser");
        log.info("请求体：{}", requestBody);

        try {
            Map<String, Object> response = webClientBuilder.build()
                    .post()
                    .uri(pythonEmbeddingServiceUrl + "/v1/query/query_parser")
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(Map.class) // 假设Python服务成功返回JSON Map
                    .block(); // 阻塞式调用，如果希望非阻塞，需要调整业务逻辑

            if (response == null) {
                throw new ServiceException("Python向量化服务返回空响应");
            }
            log.info("Python向量化服务返回响应：{}", response);

            // 解析Python服务响应
            if (response.containsKey("queryRes") && response.get("queryRes") instanceof Map) {
                Map<String, Object> queryRes = (Map<String, Object>) response.get("queryRes");
                if (queryRes.containsKey("query_embed") && queryRes.get("query_embed") instanceof List) {
                    List<Double> embedList = (List<Double>) queryRes.get("query_embed");
                    float[] embedding = new float[embedList.size()];
                    for (int i = 0; i < embedList.size(); i++) {
                        embedding[i] = embedList.get(i).floatValue();
                    }
                    log.info("Python向量化服务返回向量成功，维度：{}", embedding.length);
                    return embedding;
                } else {
                    throw new ServiceException("Python向量化服务响应中缺少'query_embed'或格式不正确");
                }
            } else {
                throw new ServiceException("Python向量化服务响应中缺少'queryRes'或格式不正确");
            }
        } catch (Exception e) {
            log.error("调用Python向量化服务失败：{}", e.getMessage(), e);
            throw new ServiceException("文本向量化失败: " + e.getMessage());
        }
    }
}