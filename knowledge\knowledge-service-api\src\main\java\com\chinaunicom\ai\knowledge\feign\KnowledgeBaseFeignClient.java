package com.chinaunicom.ai.knowledge.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseQueryDTO;
import com.chinaunicom.ai.knowledge.dto.RecallDocumentsRequest;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseListVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import com.chinaunicom.csf.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 知识库服务 Feign Client
 * 用于调用知识库模块的远程接口
 */
@FeignClient(
    value = "knowledge-service", // 服务名称，对应 Spring Cloud Eureka 或 Nacos 中的服务ID
    path = "/knowledge-base"     // 控制器的 @RequestMapping 路径
)
public interface KnowledgeBaseFeignClient {

    /**
     * 调用知识库文档召回接口
     * @param request 召回请求体，包含知识库ID列表和问题向量
     * @return 召回的文档片段列表
     */
    @PostMapping("/recall-documents")
    List<RecallDocSegment> recallDocuments(@RequestBody RecallDocumentsRequest request);

    /**
     * 查询知识库列表
     * @param queryDTO
     * @return 是否重命名成功
     */
    @GetMapping("/list")
    R<IPage<KnowledgeBaseListVO>> getKnowledgeBaseList(@Validated KnowledgeBaseQueryDTO queryDTO);
}
