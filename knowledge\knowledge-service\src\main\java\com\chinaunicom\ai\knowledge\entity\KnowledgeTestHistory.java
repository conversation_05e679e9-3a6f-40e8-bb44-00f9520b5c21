package com.chinaunicom.ai.knowledge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库检索测试历史实体类
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("knowledge_test_history")
public class KnowledgeTestHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测试记录ID，32位不含横线的UUID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 检索测试的问题文本
     */
    private String question;

    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;

    /**
     * 查询执行时间（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 返回结果数量
     */
    private Integer resultCount;

    /**
     * 创建用户ID
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新用户ID
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 是否已删除：0-未删除，1-已删除
     */
    private Integer isDeleted;

    /**
     * 租户ID
     */
    private String tenantId;
}
