package com.chinaunicom.ai.knowledge.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseListVO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识库 Mapper 接口
 * @date 2025/6/3 17:14
 */
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {
    /**
     * 查询知识库列表，包含文档数量和最近更新时间
     * @param page 分页对象
     * @param name 知识库名称（模糊查询）
     * @param tenantId 租户ID
     * @return 知识库列表分页数据
     */
    IPage<KnowledgeBaseListVO> selectKnowledgeBaseList(Page<KnowledgeBaseListVO> page, @Param("name") String name, @Param("tenantId") String tenantId);
}
