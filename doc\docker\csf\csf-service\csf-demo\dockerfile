# 基础镜像
FROM  10.19.64.203:8080/library/openjdk:17-slim
# author
MAINTAINER csf

# 挂载目录
VOLUME /home/<USER>
# 创建目录
RUN mkdir -p /home/<USER>
# 指定路径
WORKDIR /home/<USER>
# 复制jar文件到路径
COPY ./jar/csf3-demo.jar /home/<USER>/csf3-demo.jar
# 启动系统服务
ENTRYPOINT ["java","-jar","-Xms256m","-Xmx1g","--add-opens","java.base/java.lang=ALL-UNNAMED","--add-opens","java.base/java.lang.reflect=ALL-UNNAMED","csf3-demo.jar"]
