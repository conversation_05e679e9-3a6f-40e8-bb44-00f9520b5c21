
package com.chinaunicom.csf.develop.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.boot.ctrl.CsfController;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.secure.annotation.PreAuth;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.constant.RoleConstant;
import com.chinaunicom.csf.core.tool.utils.FileUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.develop.entity.Code;
import com.chinaunicom.csf.develop.entity.Datasource;
import com.chinaunicom.csf.develop.service.ICodeService;
import com.chinaunicom.csf.develop.service.IDatasourceService;
import com.chinaunicom.csf.plugins.develop.support.CsfCodeGenerator;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
/**
 * 控制器
 *
 */
//@ApiIgnore
@RestController
@AllArgsConstructor
@RequestMapping("/code")
@Tag(description = "代码生成", name = "代码生成")
@PreAuth(RoleConstant.HAS_ROLE_ADMIN)
public class CodeController extends CsfController {

	private ICodeService codeService;
	private IDatasourceService datasourceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情",description = "传入code")
	public R<Code> detail(Code code) {
		Code detail = codeService.getOne(Condition.getQueryWrapper(code));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@Parameters({
		@Parameter(name = "codeName", description = "模块名",  in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "tableName", description = "表名",  in = ParameterIn.QUERY, schema = @Schema(type = "string")),
		@Parameter(name = "modelName", description = "实体名", in = ParameterIn.QUERY, schema = @Schema(type = "string"))
	})
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页",description = "传入code")
	public R<IPage<Code>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> code, Query query) {
		QueryWrapper<Code> queryWrapper = Condition.getQueryWrapper(code, Code.class);
		queryWrapper.eq("is_deleted",0);
		IPage<Code> pages = codeService.page(Condition.getPage(query), queryWrapper);
		return R.data(pages);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "新增或修改",description = "传入code")
	public R submit(@Valid @RequestBody Code code) {
		return R.status(codeService.submit(code));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "删除",description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(codeService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 复制
	 */
	@PostMapping("/copy")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "复制",description = "传入id")
	public R copy(@Parameter(description = "主键", required = true) @RequestParam Long id) {
		Code code = codeService.getById(id);
		code.setId(null);
		code.setCodeName(code.getCodeName() + "-copy");
		return R.status(codeService.save(code));
	}

	/**
	 * 代码生成
	 */
	@PostMapping("/gen-code")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "代码生成",description = "传入ids")
	public R genCode(@Parameter(description = "主键集合", required = true) @RequestParam String ids, @RequestParam(defaultValue = "csf-v") String system,
					 @RequestParam(defaultValue = "2.0") String version) {
		Collection<Code> codes = codeService.listByIds(Func.toLongList(ids));
		codes.forEach(code -> {
			codeGenerate(system, code, version);
		});
		return R.success("代码生成成功");
	}

	private CsfCodeGenerator codeGenerate(String system, Code code, String version) {
		CsfCodeGenerator generator = new CsfCodeGenerator();
		// 设置数据源
		Datasource datasource = datasourceService.getById(code.getDatasourceId());
		generator.setDriverName(datasource.getDriverClass());
		generator.setUrl(datasource.getUrl());
		generator.setUsername(datasource.getUsername());
		generator.setPassword(datasource.getPassword());
		// 设置基础配置
		generator.setSystemName(system);
		generator.setServiceName(code.getServiceName());
		generator.setPackageName(code.getPackageName());
		generator.setPackageDir(code.getApiPath());
		generator.setPackageWebDir(code.getWebPath());
		generator.setTablePrefix(Func.toStrArray(code.getTablePrefix()));
		generator.setIncludeTables(Func.toStrArray(code.getTableName()));
		// 设置是否继承基础业务字段
		generator.setHasSuperEntity(code.getBaseMode() == 2);
		// 设置是否开启包装器模式
		generator.setHasWrapper(code.getWrapMode() == 2);
		// vue默认2.0，可以改为3.0
		generator.setVueVersion(version);
		generator.run();
		return generator;
	}

	@GetMapping("/downloadCode")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "代码下载",description = "传入id")
	public ResponseEntity<Resource> downloadCode(@RequestParam String id, @RequestParam(defaultValue = "csf-v") String system,
												 @RequestParam(defaultValue = "2.0") String version) throws IOException {
		Code code = codeService.getById(id);
		CsfCodeGenerator csfCodeGenerator = codeGenerate(system, code, version);
		// 创建临时子目录
		String tmpPath = FileUtil.getTempDirPath();

		List<String> sourcesPaths = new ArrayList<>();
		sourcesPaths.add(csfCodeGenerator.getPackageWebDir());
		sourcesPaths.add(csfCodeGenerator.getPackageDir());

		String zipFilePath = tmpPath + "code-" + id + ".zip";
		FileUtil.zipFolder(sourcesPaths, zipFilePath);

		Path file = Paths.get(zipFilePath);
		Resource resource = new UrlResource(file.toUri());

		if (resource.exists() && resource.isReadable()) {
			HttpHeaders headers = new HttpHeaders();
			headers.setCacheControl("no-cache");
			headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
			headers.setContentDispositionFormData("attachment", "code-" + id + ".zip");
			headers.setContentLength(resource.contentLength());

			return ResponseEntity.ok()
				.headers(headers)
				.body(resource);
		} else {
			return ResponseEntity.notFound().build();
		}
	}
}
