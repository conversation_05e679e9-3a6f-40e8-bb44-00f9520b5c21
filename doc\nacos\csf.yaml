#服务器配置
server:
  undertow:
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    # 线程配置
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400

#spring配置
spring:
  cloud:
    sentinel:
      eager: true
  devtools:
    restart:
      log-condition-evaluation-delta: false
    livereload:
      port: 23333

#feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

#对外暴露端口
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: csf3
    password: csf3
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2021 csf3 All Rights Reserved

#swagger配置信息
swagger:
  title: csf3 接口文档系统
  description: csf3 接口文档系统
  version: 3.3.0
  license: Powered By csf3
  licenseUrl: https://csf.com
  terms-of-service-url: https://csf.com
  contact:
    name: csf3
    email: <EMAIL>
    url: https://csf.com

#csf配置
csf:
  xss:
    enabled: true
    skip-url:
      - /weixin
  secure:
    skip-url:
      - /test/**
      - /getRsaCode
    client:
      - client-id: csf3-v
        path-patterns:
          - /csf3-v/**
  tenant:
    column: tenant_id
    tables:
      - csf_notice

