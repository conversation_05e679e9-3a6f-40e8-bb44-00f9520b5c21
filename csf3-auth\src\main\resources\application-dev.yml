#服务器端口
server:
  port: 8100

#数据源配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${csf.datasource.dev.url:**********************************************************************************************************************************************************************************************************************}
    username: ${csf.datasource.dev.username:root}
    password: ${csf.datasource.dev.password:root}
  data:
    redis:
      host: ${CSF_DEV_REDIS_HOST:localhost}
      port: ${CSF_DEV_REDIS_PORT:6379}
      ssl:
        enabled: false
#第三方登陆
social:
  enabled: true
  domain: http://127.0.0.1:1888

