package com.chinaunicom.ai.knowledge.mapper;

import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识文档 Mapper 接口
 * @date 2025/6/3 17:14
 */
public interface KnowledgeDocumentMapper extends BaseMapper<KnowledgeDocument> {
    /**
     * 逻辑删除知识库下的文档记录
     * @param baseId 知识库ID
     * @param tenantId 租户ID
     * @param deletedStatusCode 删除状态码
     * @param updateTime 更新时间
     * @param updateUser 更新用户ID
     * @return 影响行数
     */
    int logicalDeleteByBaseId(@Param("baseId") Long baseId,
                              @Param("tenantId") String tenantId,
                              @Param("deletedStatusCode") Integer deletedStatusCode,
                              @Param("updateTime") LocalDateTime updateTime,
                              @Param("updateUser") Long updateUser);

}
