<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.csf.system.mapper.TenantMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="tenantResultMap" type="com.chinaunicom.csf.system.entity.Tenant">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="tenant_name" property="tenantName"/>
        <result column="linkman" property="linkman"/>
        <result column="contact_number" property="contactNumber"/>
        <result column="address" property="address"/>
    </resultMap>


    <select id="selectTenantPage" resultMap="tenantResultMap">
        select * from csf_tenant where is_deleted = 0
    </select>

</mapper>
