package com.chinaunicom.ai.knowledge.evaluation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 端到端评测结果DTO
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Schema(description = "端到端评测结果")
public class FullEvaluationResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 临时知识库信息
     */
    @Schema(description = "临时知识库信息")
    private KnowledgeBaseInfo knowledgeBaseInfo;

    /**
     * 文档导入统计
     */
    @Schema(description = "文档导入统计")
    private DocumentImportStats documentImportStats;

    /**
     * 向量化处理统计
     */
    @Schema(description = "向量化处理统计")
    private VectorizationStats vectorizationStats;

    /**
     * 评测结果
     */
    @Schema(description = "评测结果")
    private EvaluationResult evaluationResult;

    /**
     * 资源清理状态
     */
    @Schema(description = "资源清理状态")
    private CleanupStatus cleanupStatus;

    /**
     * 整个流程的总耗时（毫秒）
     */
    @Schema(description = "整个流程的总耗时（毫秒）")
    private Long totalDurationMs;

    /**
     * 流程开始时间
     */
    @Schema(description = "流程开始时间")
    private String startTime;

    /**
     * 流程结束时间
     */
    @Schema(description = "流程结束时间")
    private String endTime;

    /**
     * 是否成功完成
     */
    @Schema(description = "是否成功完成")
    private Boolean success;

    /**
     * 错误信息（如果失败）
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 详细的执行步骤日志
     */
    @Schema(description = "详细的执行步骤日志")
    private List<ExecutionStep> executionSteps;

    /**
     * 召回率计算公式说明
     */
    @Schema(description = "召回率计算公式说明")
    private String recallRateFormula;

    /**
     * 平均准确率计算公式说明
     */
    @Schema(description = "平均准确率计算公式说明")
    private String averageAccuracyFormula;

    /**
     * 临时知识库信息
     */
    @Data
    @Schema(description = "临时知识库信息")
    public static class KnowledgeBaseInfo {
        @Schema(description = "知识库ID")
        private Long id;
        
        @Schema(description = "知识库名称")
        private String name;
        
        @Schema(description = "ES索引名称")
        private String indexName;
        
        @Schema(description = "创建时间")
        private String createTime;
        
        @Schema(description = "向量模型ID")
        private Long vectorModelId;
        
        @Schema(description = "向量模型名称")
        private String vectorModelName;
    }

    /**
     * 文档导入统计
     */
    @Data
    @Schema(description = "文档导入统计")
    public static class DocumentImportStats {
        @Schema(description = "总文档数量")
        private Integer totalDocuments;
        
        @Schema(description = "成功导入数量")
        private Integer successCount;
        
        @Schema(description = "失败数量")
        private Integer failureCount;
        
        @Schema(description = "导入耗时（毫秒）")
        private Long importDurationMs;
        
        @Schema(description = "失败原因列表")
        private List<String> failureReasons;
    }

    /**
     * 向量化处理统计
     */
    @Data
    @Schema(description = "向量化处理统计")
    public static class VectorizationStats {
        @Schema(description = "总片段数量")
        private Long totalSegments;
        
        @Schema(description = "向量化成功数量")
        private Integer vectorizedCount;
        
        @Schema(description = "向量化失败数量")
        private Integer vectorizationFailureCount;
        
        @Schema(description = "向量化处理时间（毫秒）")
        private Long vectorizationDurationMs;
        
        @Schema(description = "平均每个文档的向量化时间（毫秒）")
        private Double avgVectorizationTimePerDoc;
        
        @Schema(description = "向量化状态检查次数")
        private Integer statusCheckCount;
    }

    /**
     * 资源清理状态
     */
    @Data
    @Schema(description = "资源清理状态")
    public static class CleanupStatus {
        @Schema(description = "是否启用清理")
        private Boolean enabled;
        
        @Schema(description = "知识库删除状态")
        private Boolean knowledgeBaseDeleted;
        
        @Schema(description = "ES索引删除状态")
        private Boolean esIndexDeleted;
        
        @Schema(description = "文档删除状态")
        private Boolean documentsDeleted;
        
        @Schema(description = "清理耗时（毫秒）")
        private Long cleanupDurationMs;
        
        @Schema(description = "清理错误信息")
        private String cleanupError;
    }

    /**
     * 执行步骤
     */
    @Data
    @Schema(description = "执行步骤")
    public static class ExecutionStep {
        @Schema(description = "步骤名称")
        private String stepName;
        
        @Schema(description = "步骤状态")
        private String status; // SUCCESS, FAILURE, IN_PROGRESS
        
        @Schema(description = "开始时间")
        private String startTime;
        
        @Schema(description = "结束时间")
        private String endTime;
        
        @Schema(description = "耗时（毫秒）")
        private Long durationMs;
        
        @Schema(description = "步骤描述")
        private String description;
        
        @Schema(description = "错误信息")
        private String errorMessage;
        
        @Schema(description = "额外数据")
        private Map<String, Object> additionalData;
    }

    /**
     * 添加执行步骤
     */
    public void addExecutionStep(String stepName, String status, String description) {
        if (executionSteps == null) {
            executionSteps = new java.util.ArrayList<>();
        }
        
        ExecutionStep step = new ExecutionStep();
        step.setStepName(stepName);
        step.setStatus(status);
        step.setDescription(description);
        step.setStartTime(LocalDateTime.now().toString());
        
        executionSteps.add(step);
    }

    /**
     * 更新最后一个执行步骤的状态
     */
    public void updateLastStepStatus(String status, String errorMessage) {
        if (executionSteps != null && !executionSteps.isEmpty()) {
            ExecutionStep lastStep = executionSteps.get(executionSteps.size() - 1);
            lastStep.setStatus(status);
            lastStep.setEndTime(LocalDateTime.now().toString());
            if (errorMessage != null) {
                lastStep.setErrorMessage(errorMessage);
            }
            
            // 计算耗时
            if (lastStep.getStartTime() != null && lastStep.getEndTime() != null) {
                try {
                    LocalDateTime start = LocalDateTime.parse(lastStep.getStartTime());
                    LocalDateTime end = LocalDateTime.parse(lastStep.getEndTime());
                    lastStep.setDurationMs(java.time.Duration.between(start, end).toMillis());
                } catch (Exception e) {
                    // 忽略时间解析错误
                }
            }
        }
    }

    /**
     * 计算总体成功率
     * 优先使用评测召回率，如果没有则使用文档导入成功率
     */
    public double getOverallSuccessRate() {
        // 优先使用评测结果的召回率
        if (evaluationResult != null && evaluationResult.getRecallRate() != null) {
            return evaluationResult.getRecallRate();
        }

        // 如果没有评测结果，使用文档导入成功率作为替代指标
        if (documentImportStats != null &&
            documentImportStats.getTotalDocuments() != null &&
            documentImportStats.getSuccessCount() != null &&
            documentImportStats.getTotalDocuments() > 0) {
            return (double) documentImportStats.getSuccessCount() / documentImportStats.getTotalDocuments();
        }

        return 0.0;
    }

    /**
     * 获取性能摘要
     */
    public String getPerformanceSummary() {
        StringBuilder summary = new StringBuilder();

        // 文档导入统计
        if (documentImportStats != null) {
            double importSuccessRate = documentImportStats.getTotalDocuments() > 0 ?
                (double) documentImportStats.getSuccessCount() / documentImportStats.getTotalDocuments() * 100 : 0.0;
            summary.append(String.format("导入: %d/%d 成功(%.1f%%), ",
                documentImportStats.getSuccessCount(),
                documentImportStats.getTotalDocuments(),
                importSuccessRate));
        }

        // 向量化统计
        if (vectorizationStats != null && vectorizationStats.getTotalSegments() != null) {
            summary.append(String.format("向量化: %d 片段, ",
                vectorizationStats.getTotalSegments()));
        }

        // 评测结果统计
        if (evaluationResult != null && evaluationResult.getRecallRate() != null) {
            summary.append(String.format("召回率: %.2f%%, ",
                evaluationResult.getRecallRate() * 100));

            if (evaluationResult.getAverageAccuracy() != null) {
                summary.append(String.format("平均准确率: %.2f%%, ",
                    evaluationResult.getAverageAccuracy() * 100));
            }

            if (evaluationResult.getTotalQuestions() != null) {
                summary.append(String.format("评测问题数: %d, ",
                    evaluationResult.getTotalQuestions()));
            }
        } else {
            // 即使没有评测结果，也要显示当前状态
            summary.append("评测未完成, ");
        }

        // 总耗时
        if (totalDurationMs != null) {
            summary.append(String.format("总耗时: %.2f秒", totalDurationMs / 1000.0));
        }

        return summary.toString();
    }
}
