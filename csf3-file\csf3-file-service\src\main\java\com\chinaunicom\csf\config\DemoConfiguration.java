
package com.chinaunicom.csf.config;


import com.chinaunicom.csf.props.DemoProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * 配置feign、mybatis包名、properties
 *
 * <AUTHOR>
 */
@Configuration
@EnableFeignClients({"com.chinaunicom.csf", "com.chinaunicom.csf"})
@MapperScan({"com.chinaunicom.csf.**.mapper.**", "com.chinaunicom.csf.**.mapper.**"})
@EnableConfigurationProperties(DemoProperties.class)
public class DemoConfiguration {

}
