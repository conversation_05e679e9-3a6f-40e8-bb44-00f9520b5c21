#服务器端口
server:
  port: 8106

#数据源配置
spring:
  datasource:
    url: ${csf.datasource.dev.url}
    username: ${csf.datasource.dev.username}
    password: ${csf.datasource.dev.password}


logging:
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(%5p [${spring.application.name},%X{traceId},%X{spanId},%X{X-Span-Export:-}]) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"

