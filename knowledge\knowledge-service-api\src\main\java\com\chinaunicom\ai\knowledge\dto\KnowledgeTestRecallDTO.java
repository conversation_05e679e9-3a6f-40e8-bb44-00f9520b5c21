package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 知识库检索测试请求DTO
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@Schema(description = "知识库检索测试请求DTO")
public class KnowledgeTestRecallDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true, example = "1")
    private Long knowledgeBaseId;

    /**
     * 问题文本
     */
    @NotBlank(message = "问题文本不能为空")
    @Schema(description = "问题文本", requiredMode = Schema.RequiredMode.REQUIRED, example = "什么是向量数据库？")
    private String queryText;
}
