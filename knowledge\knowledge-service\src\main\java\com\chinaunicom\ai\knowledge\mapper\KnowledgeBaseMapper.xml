<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.entity.KnowledgeBase">
        <id column="id" property="id" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="name" property="name" />
        <result column="descrip" property="descrip" />
        <result column="vec_model" property="vecModel" />
        <result column="index_name" property="indexName" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <select id="selectKnowledgeBaseList" resultType="com.chinaunicom.ai.knowledge.vo.KnowledgeBaseListVO">
        SELECT
        kb.id,
        kb.name,
        kb.descrip,
        kb.vec_model AS vecModelId,
        COUNT(kd.id) AS documentCount,
        MAX(kd.update_time) AS lastUpdateTime
        FROM
        knowledge_base kb
        LEFT JOIN
        knowledge_document kd ON kb.id = kd.base_id AND kd.is_deleted = 0 AND kd.tenant_id = #{tenantId}
        WHERE
        kb.is_deleted = 0
        AND kb.tenant_id = #{tenantId}
        <if test="name != null and name != ''">
            AND kb.name LIKE CONCAT('%', #{name}, '%')
        </if>
        GROUP BY
        kb.id, kb.name, kb.descrip,kb.vec_model
        ORDER BY
        kb.update_time DESC
    </select>

</mapper>
