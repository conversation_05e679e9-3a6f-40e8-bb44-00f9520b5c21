package com.chinaunicom.ai.knowledge.service.impl;

import com.chinaunicom.ai.knowledge.dto.RerankRequestDTO;
import com.chinaunicom.ai.knowledge.service.RerankService;
import com.chinaunicom.ai.knowledge.vo.RerankResponseVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Rerank服务实现类
 * 调用算法端rerank接口，对召回文档进行重排序以提升RAG效果
 */
@Slf4j
@Service
public class RerankServiceImpl implements RerankService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${rerank.api.url:http://10.16.1.4:10211/v1/Rerank/similarity}")
    private String rerankApiUrl;
    
    @Value("${rerank.api.model:bce-reranker-base_v1}")
    private String defaultRerankModel;
    
    @Value("${rerank.api.enabled:true}")
    private boolean rerankEnabled;

    @Value("${rerank.api.batch.size:32}")
    private int batchSize;
    
    @Override
    public RerankResponseVO callRerankApi(RerankRequestDTO request) {
        // 参数验证
        if (request == null) {
            log.warn("Rerank请求参数为空");
            return null;
        }
        if (!StringUtils.hasText(request.getQuery())) {
            log.warn("Rerank请求查询文本为空");
            return null;
        }
        if (CollectionUtils.isEmpty(request.getTexts())) {
            log.warn("Rerank请求文本列表为空");
            return null;
        }

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 创建请求实体
            HttpEntity<RerankRequestDTO> requestEntity = new HttpEntity<>(request, headers);
            
            log.info("调用Rerank API - URL: {}, Query: {}, Texts数量: {}, Model: {}", 
                    rerankApiUrl, request.getQuery(), request.getTexts().size(), request.getModelName());
            
            // 发送POST请求
            ResponseEntity<RerankResponseVO> response = restTemplate.postForEntity(
                    rerankApiUrl, requestEntity, RerankResponseVO.class);
            
            RerankResponseVO result = response.getBody();
            if (result != null && "200".equals(result.getCode())) {
                int scoreCount = 0;
                if (result.getData() != null && result.getData().getRank() != null) {
                    scoreCount = result.getData().getRank().size();
                }
                log.info("Rerank API调用成功 - 返回分数数量: {}", scoreCount);
                return result;
            } else {
                log.warn("Rerank API调用失败 - Code: {}, Msg: {}", 
                        result != null ? result.getCode() : "null", 
                        result != null ? result.getMsg() : "null");
                return null;
            }
        } catch (Exception e) {
            log.error("调用Rerank API异常 - URL: {}, Error: {}", rerankApiUrl, e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public List<RecallDocSegment> rerankDocuments(String queryText, List<RecallDocSegment> segments, String modelName) {
        // 参数验证
        if (!StringUtils.hasText(queryText)) {
            log.warn("查询文本为空，返回原始结果");
            return segments != null ? segments : new ArrayList<>();
        }

        // 如果rerank功能未启用，直接返回原始结果
        if (!rerankEnabled) {
            log.debug("Rerank功能未启用，返回原始召回结果");
            return segments != null ? segments : new ArrayList<>();
        }
        
        // 如果没有文档片段或只有一个片段，无需重排序
        if (CollectionUtils.isEmpty(segments) || segments.size() <= 1) {
            log.debug("文档片段数量不足，无需重排序 - 数量: {}", segments != null ? segments.size() : 0);
            return segments != null ? segments : new ArrayList<>();
        }
        
        // 直接使用所有片段进行重排序（已移除友好提示功能）
        List<RecallDocSegment> validSegments = new ArrayList<>(segments);
        
        try {
            // 提取文档内容用于rerank
            List<String> texts = validSegments.stream()
                    .map(RecallDocSegment::getContent)
                    .collect(Collectors.toList());
            
            // 构建rerank请求
            RerankRequestDTO request = new RerankRequestDTO();
            request.setQuery(queryText);
            request.setTexts(texts);
            request.setModelName(StringUtils.hasText(modelName) ? modelName : defaultRerankModel);
            request.setBatchSize(batchSize);
            
            // 调用rerank API
            RerankResponseVO response = callRerankApi(request);
            
            if (response != null && response.getData() != null &&
                !CollectionUtils.isEmpty(response.getData().getRank())) {

                List<Float> scores = response.getData().getRank();
                
                // 验证分数数量与文档数量是否匹配
                if (scores.size() != validSegments.size()) {
                    log.warn("Rerank返回的分数数量与文档数量不匹配 - 分数数量: {}, 文档数量: {}",
                            scores.size(), validSegments.size());
                    return segments != null ? segments : new ArrayList<>();
                }
                
                // 为文档片段设置rerank分数
                IntStream.range(0, validSegments.size())
                        .forEach(i -> validSegments.get(i).setScore(scores.get(i)));
                
                // 按分数从高到低排序
                validSegments.sort(Comparator.comparing(RecallDocSegment::getScore, 
                        Comparator.nullsLast(Comparator.reverseOrder())));
                
                log.info("Rerank重排序完成 - 查询: '{}', 重排序文档数: {}", queryText, validSegments.size());

                return validSegments;
                
            } else {
                log.warn("Rerank API返回结果无效，使用原始排序");
                return segments != null ? segments : new ArrayList<>();
            }

        } catch (Exception e) {
            log.error("Rerank重排序异常 - 查询: '{}', Error: {}", queryText, e.getMessage(), e);
            return segments != null ? segments : new ArrayList<>();
        }
    }
}
