package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.evaluation.config.VectorizationConcurrencyConfig;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 向量化超时监控服务
 * 
 * 负责监控长时间处于VECTORIZING状态的文档，自动标记为失败状态
 * 防止向量化服务异常导致的数据库锁等待问题
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class VectorizationTimeoutMonitor {

    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    private final VectorizationConcurrencyConfig concurrencyConfig;
    
    /**
     * 定时检查向量化超时的文档
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300,000毫秒
    public void checkVectorizationTimeout() {
        try {
            log.debug("开始检查向量化超时文档...");
            
            // 查找超过配置时间仍在向量化的文档
            LocalDateTime timeoutThreshold = concurrencyConfig.getTimeoutThreshold();
            
            LambdaQueryWrapper<EvaluationDocument> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EvaluationDocument::getStatus, "VECTORIZING")
                       .lt(EvaluationDocument::getUpdateTime, timeoutThreshold);
            
            List<EvaluationDocument> timeoutDocuments = evaluationDocumentMapper.selectList(queryWrapper);
            
            if (timeoutDocuments.isEmpty()) {
                log.debug("未发现向量化超时文档");
                return;
            }
            
            log.warn("发现 {} 个疑似向量化超时文档，开始验证实际状态", timeoutDocuments.size());

            int updatedCount = 0;
            int syncedCount = 0;
            for (EvaluationDocument document : timeoutDocuments) {
                try {
                    // 先检查对应的knowledge_document实际状态
                    if (document.getKnowledgeDocId() != null) {
                        KnowledgeDocument knowledgeDoc = knowledgeDocumentMapper.selectById(document.getKnowledgeDocId());
                        if (knowledgeDoc != null) {
                            // 如果knowledge_document已经是VECTORIZED状态，说明向量化实际已完成
                            if (knowledgeDoc.getStatus().equals(DocumentStatusEnum.VECTORIZED.getCode())) {
                                // 同步状态为VECTORIZED
                                document.setStatus("VECTORIZED");
                                document.setUpdateTime(LocalDateTime.now());

                                int result = evaluationDocumentMapper.updateById(document);
                                if (result > 0) {
                                    syncedCount++;
                                    log.info("同步向量化完成状态: {} (知识库: {}, 实际已完成但evaluation表状态未同步)",
                                            document.getOriginalDocId(),
                                            document.getKnowledgeBaseId());
                                }
                                continue; // 跳过标记为失败的逻辑
                            }
                            // 如果knowledge_document也是VECTORIZING状态，才真正标记为超时失败
                            else if (knowledgeDoc.getStatus().equals(DocumentStatusEnum.VECTORIZING.getCode())) {
                                // 真正的超时情况，标记为失败
                                document.setStatus("VECTORIZE_FAILED");
                                document.setUpdateTime(LocalDateTime.now());

                                int result = evaluationDocumentMapper.updateById(document);
                                if (result > 0) {
                                    updatedCount++;
                                    log.warn("向量化真正超时，标记为失败: {} (知识库: {}, 超时阈值: {}分钟)",
                                            document.getOriginalDocId(),
                                            document.getKnowledgeBaseId(),
                                            concurrencyConfig.getTimeoutMinutes());
                                }
                            }
                        }
                    } else {
                        // 没有关联的knowledge_document，直接标记为失败
                        document.setStatus("VECTORIZE_FAILED");
                        document.setUpdateTime(LocalDateTime.now());

                        int result = evaluationDocumentMapper.updateById(document);
                        if (result > 0) {
                            updatedCount++;
                            log.warn("向量化超时文档已标记为失败: {} (知识库: {}, 无关联知识文档)",
                                    document.getOriginalDocId(),
                                    document.getKnowledgeBaseId());
                        }
                    }
                } catch (Exception e) {
                    log.error("处理超时文档状态失败: {}", document.getOriginalDocId(), e);
                }
            }
            
            if (updatedCount > 0 || syncedCount > 0) {
                log.info("向量化超时检查完成 - 同步状态: {} 个, 标记失败: {} 个", syncedCount, updatedCount);
            }
            
        } catch (Exception e) {
            log.error("检查向量化超时文档时发生异常", e);
        }
    }
    
    /**
     * 手动检查指定知识库的向量化超时文档
     * 
     * @param knowledgeBaseId 知识库ID
     * @return 处理的超时文档数量
     */
    public int checkKnowledgeBaseVectorizationTimeout(Long knowledgeBaseId) {
        try {
            log.info("手动检查知识库 {} 的向量化超时文档", knowledgeBaseId);
            
            LocalDateTime timeoutThreshold = concurrencyConfig.getTimeoutThreshold();
            
            LambdaQueryWrapper<EvaluationDocument> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EvaluationDocument::getKnowledgeBaseId, knowledgeBaseId)
                       .eq(EvaluationDocument::getStatus, "VECTORIZING")
                       .lt(EvaluationDocument::getUpdateTime, timeoutThreshold);
            
            List<EvaluationDocument> timeoutDocuments = evaluationDocumentMapper.selectList(queryWrapper);
            
            if (timeoutDocuments.isEmpty()) {
                log.info("知识库 {} 未发现向量化超时文档", knowledgeBaseId);
                return 0;
            }
            
            log.warn("知识库 {} 发现 {} 个疑似向量化超时文档，开始验证实际状态", knowledgeBaseId, timeoutDocuments.size());

            int updatedCount = 0;
            int syncedCount = 0;
            for (EvaluationDocument document : timeoutDocuments) {
                try {
                    // 先检查对应的knowledge_document实际状态
                    if (document.getKnowledgeDocId() != null) {
                        KnowledgeDocument knowledgeDoc = knowledgeDocumentMapper.selectById(document.getKnowledgeDocId());
                        if (knowledgeDoc != null && knowledgeDoc.getStatus().equals(DocumentStatusEnum.VECTORIZED.getCode())) {
                            // 同步状态为VECTORIZED
                            document.setStatus("VECTORIZED");
                            document.setUpdateTime(LocalDateTime.now());

                            int result = evaluationDocumentMapper.updateById(document);
                            if (result > 0) {
                                syncedCount++;
                            }
                            continue;
                        }
                    }

                    // 真正的超时情况，标记为失败
                    document.setStatus("VECTORIZE_FAILED");
                    document.setUpdateTime(LocalDateTime.now());

                    int result = evaluationDocumentMapper.updateById(document);
                    if (result > 0) {
                        updatedCount++;
                    }
                } catch (Exception e) {
                    log.error("更新知识库 {} 超时文档状态失败: {}", knowledgeBaseId, document.getOriginalDocId(), e);
                }
            }

            log.info("知识库 {} 向量化超时检查完成 - 同步状态: {} 个, 标记失败: {} 个", knowledgeBaseId, syncedCount, updatedCount);
            return updatedCount;
            
        } catch (Exception e) {
            log.error("检查知识库 {} 向量化超时文档时发生异常", knowledgeBaseId, e);
            return 0;
        }
    }
    
    /**
     * 获取当前向量化状态统计
     * 
     * @return 向量化状态统计信息
     */
    public VectorizationStatusStats getVectorizationStatusStats() {
        try {
            // 统计各种状态的文档数量
            LambdaQueryWrapper<EvaluationDocument> vectorizingQuery = new LambdaQueryWrapper<>();
            vectorizingQuery.eq(EvaluationDocument::getStatus, "VECTORIZING");
            int vectorizingCount = Math.toIntExact(evaluationDocumentMapper.selectCount(vectorizingQuery));
            
            LambdaQueryWrapper<EvaluationDocument> vectorizedQuery = new LambdaQueryWrapper<>();
            vectorizedQuery.eq(EvaluationDocument::getStatus, "VECTORIZED");
            int vectorizedCount = Math.toIntExact(evaluationDocumentMapper.selectCount(vectorizedQuery));
            
            LambdaQueryWrapper<EvaluationDocument> failedQuery = new LambdaQueryWrapper<>();
            failedQuery.eq(EvaluationDocument::getStatus, "VECTORIZE_FAILED");
            int failedCount = Math.toIntExact(evaluationDocumentMapper.selectCount(failedQuery));
            
            // 统计超时文档数量
            LocalDateTime timeoutThreshold = concurrencyConfig.getTimeoutThreshold();
            LambdaQueryWrapper<EvaluationDocument> timeoutQuery = new LambdaQueryWrapper<>();
            timeoutQuery.eq(EvaluationDocument::getStatus, "VECTORIZING")
                       .lt(EvaluationDocument::getUpdateTime, timeoutThreshold);
            int timeoutCount = Math.toIntExact(evaluationDocumentMapper.selectCount(timeoutQuery));
            
            VectorizationStatusStats stats = new VectorizationStatusStats();
            stats.setVectorizingCount(vectorizingCount);
            stats.setVectorizedCount(vectorizedCount);
            stats.setFailedCount(failedCount);
            stats.setTimeoutCount(timeoutCount);
            stats.setTimeoutThresholdMinutes(concurrencyConfig.getTimeoutMinutes());
            
            return stats;
            
        } catch (Exception e) {
            log.error("获取向量化状态统计时发生异常", e);
            return new VectorizationStatusStats();
        }
    }
    
    /**
     * 向量化状态统计信息
     */
    @lombok.Data
    public static class VectorizationStatusStats {
        private int vectorizingCount;      // 正在向量化的文档数量
        private int vectorizedCount;       // 已完成向量化的文档数量
        private int failedCount;           // 向量化失败的文档数量
        private int timeoutCount;          // 超时的文档数量
        private int timeoutThresholdMinutes; // 超时阈值（分钟）
    }
}
