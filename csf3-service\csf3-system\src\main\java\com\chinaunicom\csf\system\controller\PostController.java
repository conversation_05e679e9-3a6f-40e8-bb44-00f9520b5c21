
package com.chinaunicom.csf.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinaunicom.csf.core.boot.ctrl.CsfController;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.secure.CsfUser;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.system.entity.Post;
import com.chinaunicom.csf.system.service.IPostService;
import com.chinaunicom.csf.system.vo.PostVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 岗位表 控制器
 *
 */
@RestController
@AllArgsConstructor
@RequestMapping("/post")
@Tag(description = "岗位表", name = "岗位表接口")
public class PostController extends CsfController {

	private IPostService postService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情",description = "传入post")
	public R<PostVO> detail(Post post) {
		return R.data(postService.getDetail(post));
	}

	/**
	 * 分页 岗位表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页",description = "传入post")
	public R<IPage<PostVO>> list(Post post, Query query) {
		return R.data(postService.getList(post, query));
	}


	/**
	 * 自定义分页 岗位表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页",description = "传入post")
	public R<IPage<PostVO>> page(PostVO post, Query query) {
		IPage<PostVO> pages = postService.selectPostPage(Condition.getPage(query), post);
		return R.data(pages);
	}

	/**
	 * 新增 岗位表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增",description = "传入post")
	public R save(@Valid @RequestBody Post post) {
		return R.status(postService.save(post));
	}

	/**
	 * 修改 岗位表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改",description = "传入post")
	public R update(@Valid @RequestBody Post post) {
		return R.status(postService.updateById(post));
	}

	/**
	 * 新增或修改 岗位表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改",description = "传入post")
	public R submit(@Valid @RequestBody Post post) {
		post.setTenantId(SecureUtil.getTenantId());
		return R.status(postService.saveOrUpdate(post));
	}


	/**
	 * 删除 岗位表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除",description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(postService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 下拉数据源
	 */
	@GetMapping("/select")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "下拉数据源",description = "传入post")
	public R<List<Post>> select(String tenantId, CsfUser csfUser) {
		List<Post> list = postService.list(Wrappers.<Post>query().lambda().eq(Post::getTenantId, Func.toStr(tenantId, csfUser.getTenantId())));
		return R.data(list);
	}

}
