package com.chinaunicom.ai.knowledge.service;

import com.chinaunicom.ai.knowledge.dto.RerankRequestDTO;
import com.chinaunicom.ai.knowledge.vo.RerankResponseVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;

import java.util.List;

/**
 * Rerank服务接口
 * 用于调用算法端的rerank接口，提升RAG效果
 */
public interface RerankService {
    
    /**
     * 调用算法端rerank接口
     * @param request rerank请求参数
     * @return rerank响应结果
     */
    RerankResponseVO callRerankApi(RerankRequestDTO request);
    
    /**
     * 对召回的文档片段进行rerank重排序
     * @param queryText 查询文本
     * @param segments 原始召回的文档片段列表
     * @param modelName rerank模型名称
     * @return 重排序后的文档片段列表（按分数从高到低排序）
     */
    List<RecallDocSegment> rerankDocuments(String queryText, List<RecallDocSegment> segments, String modelName);
}
