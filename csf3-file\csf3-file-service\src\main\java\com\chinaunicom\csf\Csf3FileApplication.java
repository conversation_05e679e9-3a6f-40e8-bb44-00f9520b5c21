package com.chinaunicom.csf;

import com.chinaunicom.csf.core.cloud.client.CsfCloudApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * CSF3文件服务器启动器
 *
 * <AUTHOR>
 */
@EnableFeignClients({"com.chinaunicom.csf.feign"})
@CsfCloudApplication(scanBasePackages = {"com.chinaunicom.csf"})
public class Csf3FileApplication {
    public static void main(String[] args) {
        SpringApplication.run(Csf3FileApplication.class, args);
    }
}

