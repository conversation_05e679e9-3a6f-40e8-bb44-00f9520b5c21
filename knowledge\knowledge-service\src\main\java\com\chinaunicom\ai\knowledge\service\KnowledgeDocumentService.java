package com.chinaunicom.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.ai.knowledge.dto.DocumentDeleteRequest;
import com.chinaunicom.ai.knowledge.dto.DocumentRenameDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeDocumentQueryDTO;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.ai.knowledge.vo.KnowledgeDocumentVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识文档 服务类
 * @date 2025/6/3 17:14
 */
public interface KnowledgeDocumentService extends IService<KnowledgeDocument> {

    /**
     * 根据知识库ID查询知识库下的文档列表，支持文件名模糊查询和分页
     * 文档列表需要展示文档名称、文档大小（单位为字节）、上传时间、状态、状态的中文名。
     * @param queryDTO 包含知识库ID、文件名关键词、分页信息的查询DTO
     * @return 文档列表分页数据VO
     */
    IPage<KnowledgeDocumentVO> getKnowledgeDocumentsByBaseId(KnowledgeDocumentQueryDTO queryDTO);

    /**
     * 根据文档ID重命名文档，保留原文档后缀名
     * @param renameDTO 包含文档ID和新的文件名前缀的DTO
     * @return 是否重命名成功
     */
    boolean renameDocument(DocumentRenameDTO renameDTO);

    /**
     * 根据文档ID列表删除知识库文档，支持单个或批量删除。
     * 执行逻辑删除数据库记录，并物理删除MinIO文件。
     * @param deleteRequest 包含要删除的文档ID列表的请求DTO
     * @return 成功删除的数量
     */
    int deleteDocuments(DocumentDeleteRequest deleteRequest);
}
