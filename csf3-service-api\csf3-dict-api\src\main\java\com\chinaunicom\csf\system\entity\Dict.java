
package com.chinaunicom.csf.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinaunicom.csf.core.mp.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 实体类
 *
 * @since 2018-12-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("csf_dict")
@Schema(description = "Dict对象")
public class Dict extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 父主键
	 */
	@Schema(description = "父主键")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long parentId;

	/**
	 * 字典码
	 */
	@Schema(description = "字典码")
	private String code;

	/**
	 * 字典值
	 */
	@Schema(description = "字典值")
	private Integer dictKey;

	/**
	 * 字典名称
	 */
	@Schema(description = "字典名称")
	private String dictValue;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sort;

	/**
	 * 字典备注
	 */
	@Schema(description = "字典备注")
	private String remark;

	/**
	 * 是否已删除
	 */
	@TableLogic
	@Schema(description = "是否已删除")
	private Integer isDeleted;


}
