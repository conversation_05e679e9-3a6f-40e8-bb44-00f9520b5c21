#spring配置
spring:
  redis:
    ##redis 单机环境配置
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    ssl: false
    ##redis 集群环境配置
    #cluster:
    #  nodes: 127.0.0.1:7001,127.0.0.1:7002,127.0.0.1:7003
    #  commandTimeout: 5000

#项目模块集中配置
csf:
  #通用开发生产环境数据库地址(特殊情况可在对应的子工程里配置覆盖)
  datasource:
    dev:
      url: **********************************************************************************************************************************************************************************************************************
      username: root
      password: root
