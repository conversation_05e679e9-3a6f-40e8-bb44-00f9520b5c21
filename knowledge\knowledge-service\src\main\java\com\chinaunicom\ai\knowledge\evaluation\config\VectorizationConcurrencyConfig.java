package com.chinaunicom.ai.knowledge.evaluation.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 向量化并发控制配置
 * 
 * 用于配置向量化服务的并发限制和超时参数
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-09
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "vectorization.concurrency")
public class VectorizationConcurrencyConfig {

    /**
     * Python向量化服务的并发限制
     * 默认值：20（基于Python服务的实际并发能力）
     */
    private int pythonServiceLimit = 20;

    /**
     * 安全阈值，实际使用时的并发限制
     * 默认值：15（留一些余量，避免服务过载）
     */
    private int safeThreshold = 15;

    /**
     * 分批处理的批次大小
     * 默认值：20（每批处理的文档数量）
     */
    private int batchSize = 20;

    /**
     * 批次之间的等待时间（秒）
     * 默认值：30秒（让向量化服务有时间处理当前批次）
     */
    private int batchWaitSeconds = 30;

    /**
     * 向量化超时时间（分钟）
     * 默认值：30分钟（超过此时间的向量化任务将被标记为失败）
     */
    private int timeoutMinutes = 30;

    /**
     * 超时检查间隔（毫秒）
     * 默认值：5分钟（定时任务检查超时文档的间隔）
     */
    private long timeoutCheckIntervalMs = 300000; // 5分钟

    /**
     * 最大文档数量限制
     * 默认值：100（单次评测允许的最大文档数量）
     */
    private int maxDocumentsLimit = 100;

    /**
     * 超时文档自动清理阈值
     * 默认值：10（当超时文档数量超过此值时自动触发清理）
     */
    private int timeoutCleanupThreshold = 10;

    /**
     * 是否启用并发控制
     * 默认值：true（启用并发控制机制）
     */
    private boolean enabled = true;

    /**
     * 是否启用自动超时清理
     * 默认值：true（启用定时清理超时文档）
     */
    private boolean autoTimeoutCleanup = true;

    /**
     * 获取有效的批次大小
     * 确保批次大小不超过安全阈值
     */
    public int getEffectiveBatchSize() {
        return Math.min(batchSize, safeThreshold);
    }

    /**
     * 检查文档数量是否超出限制
     */
    public boolean isDocumentCountExceeded(int documentCount) {
        return documentCount > maxDocumentsLimit;
    }

    /**
     * 检查当前向量化数量是否超出安全阈值
     */
    public boolean isVectorizationOverloaded(int currentVectorizingCount) {
        return currentVectorizingCount > safeThreshold;
    }

    /**
     * 获取超时阈值时间戳
     */
    public java.time.LocalDateTime getTimeoutThreshold() {
        return java.time.LocalDateTime.now().minusMinutes(timeoutMinutes);
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "向量化并发控制配置: 启用=%s, Python服务限制=%d, 安全阈值=%d, 批次大小=%d, " +
            "批次等待=%d秒, 超时时间=%d分钟, 最大文档数=%d",
            enabled, pythonServiceLimit, safeThreshold, batchSize,
            batchWaitSeconds, timeoutMinutes, maxDocumentsLimit
        );
    }
}
