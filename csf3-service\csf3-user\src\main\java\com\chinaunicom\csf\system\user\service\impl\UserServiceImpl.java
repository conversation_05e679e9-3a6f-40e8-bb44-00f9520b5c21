
package com.chinaunicom.csf.system.user.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.secure.CsfUser;
import com.chinaunicom.csf.core.tool.constant.CsfConstant;
import com.chinaunicom.csf.system.user.vo.UserVO;
import com.chinaunicom.csf.system.user.wrapper.UserWrapper;
import lombok.AllArgsConstructor;
import com.chinaunicom.csf.common.constant.CommonConstant;
import com.chinaunicom.csf.core.log.exception.ServiceException;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.*;
import com.chinaunicom.csf.system.entity.Tenant;
import com.chinaunicom.csf.system.feign.ISysClient;
import com.chinaunicom.csf.system.user.entity.User;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.chinaunicom.csf.system.user.entity.UserOauth;
import com.chinaunicom.csf.system.user.excel.UserExcel;
import com.chinaunicom.csf.system.user.mapper.UserMapper;
import com.chinaunicom.csf.system.user.service.IUserOauthService;
import com.chinaunicom.csf.system.user.service.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 服务实现类
 *
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserServiceImpl extends BaseServiceImpl<UserMapper, User> implements IUserService {
	private static final String GUEST_NAME = "guest";
	private static final String MINUS_ONE = "-1";

	private ISysClient sysClient;
	private IUserOauthService userOauthService;

	@Override
	public boolean submit(User user) {
		if (Func.isNotEmpty(user.getPassword())) {
			// 生成随机盐字段，密码 + 随机盐生成md5再登录
			String salt = StringUtil.random(10);
			user.setSalt(salt);
			user.setPassword(DigestUtil.encrypt(user.getPassword() + salt));
		}
		Long cnt = baseMapper.selectCount(Wrappers.<User>query().lambda().eq(User::getTenantId, user.getTenantId()).eq(User::getAccount, user.getAccount()));
		if (cnt > 0) {
			throw new ServiceException("当前用户已存在!");
		}
		return saveOrUpdate(user);
	}

	@Override
	public IPage<User> selectUserPage(IPage<User> page, User user) {
		return page.setRecords(baseMapper.selectUserPage(page, user));
	}

	@Override
	public UserInfo userInfo(Long userId) {
		UserInfo userInfo = new UserInfo();
		User user = baseMapper.selectById(userId);
		userInfo.setUser(user);
		if (Func.isNotEmpty(user)) {
			List<String> roleAlias = baseMapper.getRoleAlias(Func.toStrArray(user.getRoleId()));
			userInfo.setRoles(roleAlias);
		}
		return userInfo;
	}

	@Override
	public UserInfo userInfo(String tenantId, String account, String password) {
		UserInfo userInfo = new UserInfo();
		User user = baseMapper.getUser(tenantId, account, password);
		userInfo.setUser(user);
		if (Func.isNotEmpty(user)) {
			List<String> roleAlias = baseMapper.getRoleAlias(Func.toStrArray(user.getRoleId()));
			userInfo.setRoles(roleAlias);
		}
		return userInfo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public UserInfo userInfo(UserOauth userOauth) {
		UserOauth uo = userOauthService.getOne(Wrappers.<UserOauth>query().lambda().eq(UserOauth::getUuid, userOauth.getUuid()).eq(UserOauth::getSource, userOauth.getSource()));
		UserInfo userInfo;
		if (Func.isNotEmpty(uo) && Func.isNotEmpty(uo.getUserId())) {
			userInfo = this.userInfo(uo.getUserId());
			userInfo.setOauthId(Func.toStr(uo.getId()));
		} else {
			userInfo = new UserInfo();
			if (Func.isEmpty(uo)) {
				userOauthService.save(userOauth);
				userInfo.setOauthId(Func.toStr(userOauth.getId()));
			} else {
				userInfo.setOauthId(Func.toStr(uo.getId()));
			}
			User user = new User();
			user.setAccount(userOauth.getUsername());
			userInfo.setUser(user);
			userInfo.setRoles(Collections.singletonList(GUEST_NAME));
		}
		return userInfo;
	}

	@Override
	public boolean grant(String userIds, String roleIds) {
		User user = new User();
		user.setRoleId(roleIds);
		return this.update(user, Wrappers.<User>update().lambda().in(User::getId, Func.toLongList(userIds)));
	}

	@Override
	public boolean resetPassword(String userIds) {
		List<String>  userIdList = Func.toStrList(userIds);
		List<User> userList = new ArrayList<>();
		userIdList.forEach(userId -> {
			User user = new User();
			String salt = StringUtil.random(10);
			user.setId(Long.valueOf(userId));
			user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD + salt));
			user.setUpdateTime(DateUtil.now());
			user.setSalt(salt);
			userList.add(user);
		});

		return this.updateBatchById(userList);
	}

	@Override
	public boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1) {
		User user = getById(userId);
		if (!newPassword.equals(newPassword1)) {
			throw new ServiceException("请输入正确的确认密码!");
		}
		if (!user.getPassword().equals(DigestUtil.encrypt(oldPassword + user.getSalt()))) {
			throw new ServiceException("原密码不正确!");
		}
		String salt = StringUtil.random(10);
		return this.update(Wrappers.<User>update().lambda().set(User::getPassword, DigestUtil.encrypt(newPassword + salt))
				.set(User::getSalt, salt).eq(User::getId, userId));
	}

	@Override
	public List<String> getRoleName(String roleIds) {
		return baseMapper.getRoleName(Func.toStrArray(roleIds));
	}

	@Override
	public List<String> getDeptName(String deptIds) {
		return baseMapper.getDeptName(Func.toStrArray(deptIds));
	}

	@Override
	public void importUser(List<UserExcel> data) {
		data.forEach(userExcel -> {
			String salt = StringUtil.random(10);
			User user = Objects.requireNonNull(BeanUtil.copy(userExcel, User.class));
			// 设置部门ID
			user.setDeptId(sysClient.getDeptIds(userExcel.getTenantId(), userExcel.getDeptName()));
			// 设置岗位ID
			user.setPostId(sysClient.getPostIds(userExcel.getTenantId(), userExcel.getPostName()));
			// 设置角色ID
			user.setRoleId(sysClient.getRoleIds(userExcel.getTenantId(), userExcel.getRoleName()));
			// 设置默认密码
			user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD + salt));
			this.submit(user);
		});
	}

	@Override
	public List<UserExcel> exportUser(Wrapper<User> queryWrapper) {
		List<UserExcel> userList = baseMapper.exportUser(queryWrapper);
		userList.forEach(user -> {
			user.setRoleName(StringUtil.join(sysClient.getRoleNames(user.getRoleId())));
			user.setDeptName(StringUtil.join(sysClient.getDeptNames(user.getDeptId())));
			user.setPostName(StringUtil.join(sysClient.getPostNames(user.getPostId())));
		});
		return userList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean registerGuest(User user, Long oauthId) {
		R<Tenant> result = sysClient.getTenant(user.getTenantId());
		Tenant tenant = result.getData();
		if (!result.isSuccess() || tenant == null || tenant.getId() == null) {
			throw new ServiceException("租户信息错误!");
		}
		UserOauth userOauth = userOauthService.getById(oauthId);
		if (userOauth == null || userOauth.getId() == null) {
			throw new ServiceException("第三方登陆信息错误!");
		}
		user.setRealName(user.getName());
		user.setAvatar(userOauth.getAvatar());
		user.setRoleId(MINUS_ONE);
		user.setDeptId(MINUS_ONE);
		user.setPostId(MINUS_ONE);
		boolean userTemp = this.submit(user);
		userOauth.setUserId(user.getId());
		userOauth.setTenantId(user.getTenantId());
		boolean oauthTemp = userOauthService.updateById(userOauth);
		return (userTemp && oauthTemp);
	}

	@Override
	public IPage<UserVO> getList(Map<String, Object> user, Query query, CsfUser csfUser) {
		QueryWrapper<User> queryWrapper = Condition.getQueryWrapper(user, User.class);
		if(!csfUser.getTenantId().equals(CsfConstant.ADMIN_TENANT_ID)) {
			queryWrapper.lambda().eq(User::getTenantId, csfUser.getTenantId());
		}
		IPage<User> pages = this.page(Condition.getPage(query),queryWrapper);
		IPage<UserVO> userVOIPage = UserWrapper.build().pageVO(pages);

		if(pages.getRecords().size() > 0) {
			List<String> tenantIds = new ArrayList<>();
			for (User curUser: pages.getRecords()) {
				tenantIds.add(curUser.getTenantId());
			}
			List<Tenant> tenants = sysClient.getTenantList(StringUtil.join(tenantIds,","));
			Map<String, Tenant> tenantMap = new HashMap<>();
			for (Tenant tenant: tenants) {
				tenantMap.put(tenant.getTenantId(),tenant);
			}

			List<UserVO> userVOList = new ArrayList<>();
			for (UserVO userVO: userVOIPage.getRecords()) {
				Tenant tenant = tenantMap.get(userVO.getTenantId());
				if(null != tenant) {
					userVO.setTenantName(tenant.getTenantName());
				}
				UserVO curVO = new UserVO();
				BeanUtil.copy(userVO, curVO);
				userVOList.add(curVO);
			}
			userVOIPage.setRecords(userVOList);
		}
		return userVOIPage;
	}

	@Override
	public UserVO getDetail(User user) {
		User detail = this.getOne(Condition.getQueryWrapper(user));
		UserVO userVO = UserWrapper.build().entityVO(detail);

		Tenant tenant = sysClient.getTenant(detail.getTenantId()).getData();
		if(Func.isNotEmpty(tenant)) {
			userVO.setTenantName(tenant.getTenantName());
		}

		return userVO;
	}

}
