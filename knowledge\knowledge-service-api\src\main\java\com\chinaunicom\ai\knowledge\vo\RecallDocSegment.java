package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 召回的文档片段
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "召回的文档片段")
public class RecallDocSegment {
    @Schema(description = "所属文档ID", example = "123")
    private Long docId;

    @Schema(description = "所属文档标题", example = "向量数据库简介")
    private String docTitle;

    @Schema(description = "匹配到的文档片段内容", example = "向量数据库是一种用于存储和检索高维向量的数据库...")
    private String content;

    @Schema(description = "匹配分数", example = "0.85")
    private Float score;

}
