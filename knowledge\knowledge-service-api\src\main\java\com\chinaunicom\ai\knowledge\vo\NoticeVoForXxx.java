package com.chinaunicom.ai.knowledge.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 通知公告视图类
 *
 * <AUTHOR>
 */
@Data
public class NoticeVoForXxx implements Serializable {

	private static final long serialVersionUID = 3725189710878260069L;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 通知类型
	 */
	private Integer category;

	/**
	 * 发布日期
	 */
	private Date releaseTime;

	/**
	 * 内容
	 */
	private String content;

}
