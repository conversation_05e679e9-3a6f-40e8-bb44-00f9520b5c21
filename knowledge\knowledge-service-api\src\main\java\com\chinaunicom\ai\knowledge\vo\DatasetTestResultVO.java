package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据集测试结果VO
 * 对应前端的 DatasetTestResult 类型
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@Schema(description = "数据集测试结果VO")
public class DatasetTestResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @Schema(description = "结果ID", example = "def456ghi789jkl012mno345pqrst678")
    private String id;

    /**
     * 匹配到的文档分片内容
     */
    @Schema(description = "匹配到的文档分片内容", example = "向量数据库是一种专门用于存储和检索高维向量的数据库系统...")
    private String content;

    /**
     * 该分片匹配的分数
     */
    @Schema(description = "该分片匹配的分数", example = "0.85")
    private Double score;

    /**
     * 该分片所在的原始文档的ID
     */
    @Schema(description = "该分片所在的原始文档的ID", example = "123")
    private String documentId;

    /**
     * 该分片所在的原始文档的名称
     */
    @Schema(description = "该分片所在的原始文档的名称", example = "向量数据库技术白皮书.pdf")
    private String documentName;
}
