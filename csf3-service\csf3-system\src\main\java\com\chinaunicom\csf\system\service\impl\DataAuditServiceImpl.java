package com.chinaunicom.csf.system.service.impl;

import com.chinaunicom.csf.plugins.datasecurity.entity.AuditLog;
import com.chinaunicom.csf.system.mapper.AuditLogMapper;
import com.chinaunicom.csf.system.service.DataAuditService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DataAuditServiceImpl implements DataAuditService {

	private AuditLogMapper mapper;

	@Autowired
	public DataAuditServiceImpl(AuditLogMapper mapper) {
		this.mapper = mapper;
	}

	@Override
	public int log(AuditLog log) {
		return mapper.insert(log);
	}
}
