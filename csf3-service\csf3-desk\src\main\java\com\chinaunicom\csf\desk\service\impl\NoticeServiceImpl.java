
package com.chinaunicom.csf.desk.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.desk.mapper.NoticeMapper;
import com.chinaunicom.csf.desk.entity.Notice;
import com.chinaunicom.csf.desk.service.INoticeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * @since 2018-09-29
 */
@Service
public class NoticeServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements INoticeService {

	@Override
	public IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice) {
		return page.setRecords(baseMapper.selectNoticePage(page, notice));
	}

}
