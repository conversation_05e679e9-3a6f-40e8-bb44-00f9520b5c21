
package com.chinaunicom.csf.auth.granter;

import com.chinaunicom.csf.auth.enums.CsfUserEnum;
import com.chinaunicom.csf.auth.utils.TokenUtil;
import com.chinaunicom.csf.common.cache.CacheNames;
import com.chinaunicom.csf.core.log.exception.ServiceException;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.*;
import com.chinaunicom.csf.system.user.entity.UserInfo;
import com.chinaunicom.csf.system.user.feign.IUserClient;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 验证码TokenGranter
 *
 */
@Component
@AllArgsConstructor
@Slf4j
public class CaptchaTokenGranter implements ITokenGranter {

	public static final String GRANT_TYPE = "captcha";

	private IUserClient userClient;
	private RedisUtil redisUtil;

	@Override
	public UserInfo grant(TokenParameter tokenParameter) {
		HttpServletRequest request = WebUtil.getRequest();

		String key = request.getHeader(TokenUtil.CAPTCHA_HEADER_KEY);
		String code = request.getHeader(TokenUtil.CAPTCHA_HEADER_CODE);
		// 获取验证码
		String redisCode = String.valueOf(redisUtil.get(CacheNames.CAPTCHA_KEY + key));
		// 判断验证码
		if (code == null || !StringUtil.equalsIgnoreCase(redisCode, code)) {
			throw new ServiceException(TokenUtil.CAPTCHA_NOT_CORRECT);
		}

		String tenantId = tokenParameter.getArgs().getStr("tenantId");
		String account = tokenParameter.getArgs().getStr("account");
		String password = tokenParameter.getArgs().getStr("password");
		UserInfo userInfo = null;
		if (Func.isNoneBlank(account, password)) {
			// 获取用户类型
			String userType = tokenParameter.getArgs().getStr("userType");
			String salt =  userClient.getUserSalt(tenantId, account);
			if(ObjectUtil.isEmpty(salt)) {
				salt = "";
			}
			R<UserInfo> result;
			// 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
			if (userType.equals(CsfUserEnum.WEB.getName())) {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			} else if (userType.equals(CsfUserEnum.APP.getName())) {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			} else {
				result = userClient.userInfo(tenantId, account, DigestUtil.encrypt(password + salt));
			}
			userInfo = result.isSuccess() ? result.getData() : null;
		}
		return userInfo;
	}

}
