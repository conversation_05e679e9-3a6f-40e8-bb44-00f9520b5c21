# 平均准确率功能测试指南

## 测试目标

验证EvaluationController的`/run-evaluation`接口新增的平均准确率计算功能是否正确工作。

## 测试前准备

### 1. 确保数据准备
```bash
# 确保已导入评测数据
POST /evaluation/import-dataset
{
  "knowledgeBaseId": 999,
  "maxCount": 10
}
```

### 2. 检查数据状态
```bash
# 获取数据集信息
GET /evaluation/dataset-info

# 检查统计信息
GET /evaluation/statistics?knowledgeBaseId=999
```

## 测试用例

### 测试用例1: 正常场景测试

**请求**:
```bash
POST /evaluation/run-evaluation
Content-Type: application/json

{
  "knowledgeBaseId": 999
}
```

**预期响应结构**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "knowledgeBaseId": 999,
    "totalQuestions": 10,
    "correctRecalls": 7,
    "recallRate": 0.7,
    "averageAccuracy": 0.65,  // 新增字段
    "avgExecutionTime": 150.0,
    "testResults": [
      {
        "question": "测试问题1",
        "expectedDocId": "doc1",
        "isCorrect": true,
        "accuracy": 0.5,  // 新增字段
        "recallCount": 2,
        "recalledDocIds": ["doc1", "doc2"],
        "similarityScores": [0.95, 0.78],
        "executionTime": 120
      }
    ],
    "startTime": "2025-01-07T10:30:00",
    "endTime": "2025-01-07T10:30:05",
    "totalDuration": 5000
  },
  "msg": "评测测试完成"
}
```

**验证点**:
- ✅ 响应包含`averageAccuracy`字段
- ✅ 每个`testResult`包含`accuracy`字段
- ✅ `averageAccuracy`值在0.0-1.0范围内
- ✅ 单个问题的`accuracy`值在0.0-1.0范围内

### 测试用例2: 边界情况测试

#### 2.1 空知识库测试
```bash
POST /evaluation/run-evaluation
{
  "knowledgeBaseId": 888  // 空知识库
}
```

**预期响应**:
```json
{
  "data": {
    "totalQuestions": 0,
    "averageAccuracy": 0.0,
    "recallRate": 0.0,
    "testResults": []
  }
}
```

#### 2.2 无召回结果测试
如果某些问题无法召回任何文档，验证：
- 该问题的`accuracy`为0.0
- 该问题的`recallCount`为0
- 该问题的`recalledDocIds`为空数组

## 手动计算验证

### 验证步骤

1. **记录测试结果**
   ```
   问题1: recalledDocIds=[doc1, doc2, doc3], expectedDocId=doc1
   问题2: recalledDocIds=[doc2, doc4], expectedDocId=doc2  
   问题3: recalledDocIds=[doc5], expectedDocId=doc3
   ```

2. **计算单个问题准确率**
   ```
   问题1: accuracy = 1/3 ≈ 0.333 (3个召回，1个正确)
   问题2: accuracy = 1/2 = 0.5 (2个召回，1个正确)
   问题3: accuracy = 0/1 = 0.0 (1个召回，0个正确)
   ```

3. **计算平均准确率**
   ```
   averageAccuracy = (0.333 + 0.5 + 0.0) / 3 ≈ 0.278
   ```

4. **对比API返回值**
   - 检查每个问题的`accuracy`是否与手动计算一致
   - 检查`averageAccuracy`是否与手动计算一致

## 日志验证

### 查看应用日志
```bash
# 查看评测完成日志
grep "评测完成" application.log

# 期望看到类似日志：
# INFO - 评测完成，知识库ID: 999, 总问题数: 10, 正确召回数: 7, 召回率: 70.00%, 平均准确率: 65.50%
```

### 验证日志内容
- ✅ 日志包含平均准确率信息
- ✅ 百分比格式正确显示
- ✅ 数值与API响应一致

## 性能测试

### 测试大数据量场景
```bash
# 导入更多数据
POST /evaluation/import-dataset
{
  "knowledgeBaseId": 999,
  "maxCount": 100
}

# 执行评测
POST /evaluation/run-evaluation
{
  "knowledgeBaseId": 999
}
```

**验证点**:
- ✅ 响应时间在可接受范围内
- ✅ 内存使用正常
- ✅ 计算结果准确

## 兼容性测试

### 验证向后兼容性
1. **现有字段保持不变**
   - `recallRate`计算逻辑不变
   - `correctRecalls`统计正确
   - `isCorrect`判断逻辑不变

2. **新字段不影响现有功能**
   - 现有客户端可以忽略新字段
   - API路径和参数保持不变

## 错误场景测试

### 测试异常处理
1. **网络异常**: 模拟检索服务不可用
2. **数据异常**: 测试expectedDocId为null的情况
3. **系统异常**: 测试数据库连接异常

**验证点**:
- ✅ 异常情况下accuracy设置为0.0
- ✅ 错误信息正确记录
- ✅ 不影响其他正常问题的计算

## 测试检查清单

### 功能验证
- [ ] averageAccuracy字段存在且类型正确
- [ ] 单个问题accuracy字段存在且类型正确
- [ ] 准确率计算公式正确
- [ ] 边界情况处理正确
- [ ] 异常情况处理正确

### 性能验证
- [ ] 响应时间在可接受范围
- [ ] 内存使用正常
- [ ] 大数据量测试通过

### 兼容性验证
- [ ] 现有字段功能不变
- [ ] API接口保持兼容
- [ ] 日志格式正确

### 数据验证
- [ ] 手动计算与API结果一致
- [ ] 数值范围正确(0.0-1.0)
- [ ] 百分比显示正确

## 常见问题排查

### 1. averageAccuracy为0但有正确召回
**可能原因**: 
- expectedDocId为空被跳过
- 召回结果中没有正确文档

**排查方法**:
```bash
# 检查测试结果详情
grep "expectedDocId" response.json
grep "recalledDocIds" response.json
```

### 2. accuracy计算不正确
**可能原因**:
- 文档ID映射错误
- 召回结果解析错误

**排查方法**:
```bash
# 检查日志中的调试信息
grep "测试完成" application.log
```

### 3. 性能问题
**可能原因**:
- 数据量过大
- 数据库查询效率低

**排查方法**:
```bash
# 监控数据库查询
# 检查响应时间分布
```

## 测试报告模板

```
测试时间: 2025-01-07
测试环境: 开发环境
知识库ID: 999

测试结果:
✅ 功能测试通过
✅ 性能测试通过  
✅ 兼容性测试通过
✅ 边界情况测试通过

关键指标:
- 总问题数: 10
- 平均准确率: 65.5%
- 召回率: 70.0%
- 平均响应时间: 2.5秒

问题发现: 无

建议: 功能正常，可以发布
```
