
#set($wrapperPackage=$package.Entity.replace("entity","wrapper"))
package $!{wrapperPackage};

import lombok.AllArgsConstructor;
import com.chinaunicom.csf.core.mp.support.BaseEntityWrapper;
import com.chinaunicom.csf.core.tool.utils.BeanUtil;
import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("entity","vo"))
import $!{voPackage}.$!{entity}VO;

/**
 * $!{table.comment}包装类,返回视图层所需的字段
 *
 * @since $!{date}
 */
public class $!{entity}Wrapper extends BaseEntityWrapper<$!{entity}, $!{entity}VO>  {

    public static $!{entity}Wrapper build() {
        return new $!{entity}Wrapper();
    }

	@Override
	public $!{entity}VO entityVO($!{entity} $!{table.entityPath}) {
		$!{entity}VO $!{table.entityPath}VO = BeanUtil.copy($!{table.entityPath}, $!{entity}VO.class);

		return $!{table.entityPath}VO;
	}

}
