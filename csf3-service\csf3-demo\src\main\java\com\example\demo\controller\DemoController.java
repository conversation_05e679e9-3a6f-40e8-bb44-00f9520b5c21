
package com.example.demo.controller;

import com.example.demo.props.DemoProperties;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Demo控制器
 * author: Bugbean
 */
@RefreshScope
@RestController
@RequestMapping("demo")
@Tag(description = "配置接口", name = "即时刷新配置")
public class DemoController {

	@Value("${demo.name}")
	private String name;

	private final DemoProperties properties;

	public DemoController(DemoProperties properties) {
		this.properties = properties;
	}


	@GetMapping("name")
	public String getName() {
		return name;
	}

	@GetMapping("name-by-props")
	public String getNameByProps() {
		return properties.getName();
	}

}
