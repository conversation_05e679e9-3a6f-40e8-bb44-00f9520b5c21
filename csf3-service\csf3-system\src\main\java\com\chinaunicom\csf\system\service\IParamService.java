
package com.chinaunicom.csf.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseService;
import com.chinaunicom.csf.system.entity.Param;
import com.chinaunicom.csf.system.vo.ParamVO;

/**
 * 服务类
 *
 */
public interface IParamService extends BaseService<Param> {

	/***
	 * 自定义分页
	 * @param page
	 * @param param
	 * @return
	 */
	IPage<ParamVO> selectParamPage(IPage<ParamVO> page, ParamVO param);

}
