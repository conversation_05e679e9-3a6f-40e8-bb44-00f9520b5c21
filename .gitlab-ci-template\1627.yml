code_check:
  stage: code_check
  image: ************:8080/ums/maven:3.8.1-openjdk-17
  tags:
  - devops
  script:
  - mvn clean package
  artifacts:
    expire_in: 3 hrs
    paths:
    - ./csf3-service/csf3-demo/target/*.jar
  only:
    variables:
    - $IS_CODE_CHECK == "1"
push_images:
  stage: push_images
  image: ************:8080/library/docker:19.03.14
  tags:
  - devops
  services:
  - ************:8080/library/docker:19.03.14-dind
  before_script:
  - mkdir -p $HOME/.docker
  - echo $DOCKER_AUTH_CONFIG > $HOME/.docker/config.json
  - docker info
  script:
  - echo $DOCKERFILE_PATH
  - docker build -t ${CI_REGISTRY_IMAGE_DIR}:${imageTag} -f $DOCKERFILE_PATH .
  - docker push ${CI_REGISTRY_IMAGE_DIR}:${imageTag}
  - wget --no-check-certificate --post-data "imageTag=$imageTag" $IMAGE_WEBHOOK_URL
  only:
    variables:
    - $IS_PUSH_IMAGE == "1"
deploy:
  stage: deploy
  image: ************:8080/devops-ci/mvn:1.0.1
  tags:
  - devops
  script:
  - echo $imageTag
  - echo $DEPLOY_WEBHOOK_URL
  - echo gitlabProjectId=$CI_PROJECT_ID&pipelineId=$CI_PIPELINE_ID&ref=$CI_COMMIT_REF_NAME&imageName=$imageName&tag=$imageTag&deployVersion=$deployVersion&description=$description&deployParams=$deployParams&envName=$envName&deployType=$deployType&localPipelineId=$localPipelineId&deployPath=$deployPath
  - wget --no-check-certificate --header="Authorization:$AUTH_TOKEN" --post-data="gitlabProjectId=$CI_PROJECT_ID&pipelineId=$CI_PIPELINE_ID&ref=$CI_COMMIT_REF_NAME&imageName=$imageName&tag=$imageTag&deployVersion=$deployVersion&description=$description&deployParams=$deployParams&envName=$envName&deployType=$deployType&localPipelineId=$localPipelineId&deployPath=$deployPath"
    $DEPLOY_WEBHOOK_URL
  - echo $DEPLOY_LOG_URL
  - 'curl -ks -X POST -H "Authorization: $AUTH_TOKEN" -d "gitlabProjectId=$CI_PROJECT_ID&pipelineId=$CI_PIPELINE_ID&ref=$CI_COMMIT_REF_NAME&imageName=$imageName&tag=$imageTag&deployVersion=$deployVersion&description=$description&deployParams=$deployParams&envName=$envName&deployType=$deployType&localPipelineId=$localPipelineId&deployPath=$deployPath"
    $DEPLOY_LOG_URL'
  - echo "finish deploy..."
  only:
    variables:
    - $IS_DEPLOY == "1"
test:
  image: ************:8080/devops-ci/mvn:1.0.1
  stage: test
  only:
    variables:
    - $IS_TEST == "1"
  script:
  - echo $imageTag
  - wget --no-check-certificate --header="Authorization:$AUTH_TOKEN" --post-data "localPipelineId=$localPipelineId&stageId=${stageId}&jobId=$CI_JOB_ID"
    $TEST_PLAN_WEBHOOK_URL
  tags:
  - devops
