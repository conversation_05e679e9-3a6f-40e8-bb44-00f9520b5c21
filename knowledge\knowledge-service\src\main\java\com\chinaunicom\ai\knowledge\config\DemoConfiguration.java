
package com.chinaunicom.ai.knowledge.config;


import com.chinaunicom.ai.knowledge.props.DemoProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * 配置feign、mybatis包名、properties
 *
 * <AUTHOR>
 */
@Configuration
@EnableFeignClients({"com.chinaunicom.csf", "com.chinaunicom.ai.knowledge"})
@MapperScan({"com.chinaunicom.csf.**.mapper.**", "com.chinaunicom.ai.knowledge.**.mapper.**"})
@EnableConfigurationProperties(DemoProperties.class)
public class DemoConfiguration {

}
