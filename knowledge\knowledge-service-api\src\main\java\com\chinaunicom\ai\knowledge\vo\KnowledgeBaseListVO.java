package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库列表VO
 */
@Data
@Schema(description = "知识库列表VO")
public class KnowledgeBaseListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "知识库名称")
    private String name;

    @Schema(description = "知识库描述")
    private String descrip; // 将 'desc' 更改为 'descrip'

    @Schema(description = "嵌入模型ID")
    private Long vecModelId;

    @Schema(description = "知识库下文档数量")
    private Long documentCount;

    @Schema(description = "最近更新时间")
    private LocalDateTime lastUpdateTime;
}