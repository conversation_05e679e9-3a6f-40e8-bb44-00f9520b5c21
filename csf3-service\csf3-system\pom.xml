<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>csf3-service</artifactId>
        <groupId>com.chinaunicom.csf</groupId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>csf3-system</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-dict-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-system-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-audit-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-plugins-datasecurity</artifactId>
        </dependency>
        <!--Zipkin-->
        <!--<dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-zipkin</artifactId>
        </dependency>-->
    </dependencies>

</project>
