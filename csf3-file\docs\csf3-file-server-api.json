{"openapi": "3.0.3", "info": {"title": "CSF3文件服务器API", "description": "基于MinIO的文件服务器微服务，提供文件上传、下载、删除、元数据查询等功能", "version": "1.0.0", "contact": {"name": "CSF3 Team", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:8080", "description": "本地开发环境"}, {"url": "http://***********:8080", "description": "测试环境"}], "security": [{"Authorization": [], "CsfAuth": []}], "components": {"securitySchemes": {"Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "认证令牌"}, "CsfAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Csf-auth", "description": "CSF框架认证头"}}, "schemas": {"R": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应码"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"description": "响应数据"}, "msg": {"type": "string", "description": "响应消息"}}}, "FileMetadataDTO": {"type": "object", "properties": {"id": {"type": "string", "description": "文件ID (32位不含横线的UUID)"}, "fileName": {"type": "string", "description": "文件名（系统处理后的名称）"}, "objectName": {"type": "string", "description": "MinIO中的对象名称"}, "bucketName": {"type": "string", "description": "MinIO桶名称"}, "fileSize": {"type": "integer", "format": "int64", "description": "文件大小（字节）"}, "mimeType": {"type": "string", "description": "文件MIME类型"}, "uploadTime": {"type": "string", "format": "date-time", "description": "上传时间"}, "bizType": {"type": "string", "description": "文件业务类型（如document, avatar）"}, "originalFileName": {"type": "string", "description": "原始文件名"}, "accessPath": {"type": "string", "description": "文件访问路径或URL"}, "uploadId": {"type": "string", "description": "分片上传ID（如果是非分片上传则为空）"}, "partCount": {"type": "integer", "description": "分片数量（如果是非分片上传则为空）"}}}, "InitMultipartUploadRequest": {"type": "object", "required": ["fileName", "mimeType"], "properties": {"fileName": {"type": "string", "description": "文件名"}, "mimeType": {"type": "string", "description": "文件MIME类型"}, "bizType": {"type": "string", "description": "业务类型 (例如: 'avatar', 'document')"}}}, "InitMultipartUploadResponse": {"type": "object", "properties": {"fileId": {"type": "string", "description": "文件ID (32位不含横线的UUID)"}, "objectName": {"type": "string", "description": "MinIO中的对象名"}, "bucketName": {"type": "string", "description": "存储的桶名"}, "uploadId": {"type": "string", "description": "Upload ID，用于后续分片上传"}}}, "CompleteMultipartUploadRequest": {"type": "object", "required": ["fileId", "objectName", "bucketName", "uploadId", "partETags"], "properties": {"fileId": {"type": "string", "description": "文件ID (32位不含横线的UUID)"}, "objectName": {"type": "string", "description": "MinIO中的对象名"}, "bucketName": {"type": "string", "description": "存储的桶名"}, "uploadId": {"type": "string", "description": "Upload ID，用于完成分片上传"}, "partETags": {"type": "array", "items": {"$ref": "#/components/schemas/PartETag"}, "description": "已上传分片的信息列表"}}}, "PartETag": {"type": "object", "properties": {"partNumber": {"type": "integer", "description": "分片编号"}, "etag": {"type": "string", "description": "分片Etag"}}}}}, "paths": {"/file/upload": {"post": {"tags": ["文件上传"], "summary": "上传文件", "description": "上传单个文件到MinIO存储", "security": [{"Authorization": [], "CsfAuth": []}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "要上传的文件"}, "bizType": {"type": "string", "description": "业务类型 (例如: 'avatar', 'document')"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "上传成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"$ref": "#/components/schemas/FileMetadataDTO"}}}]}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/file/download/{fileId}": {"get": {"tags": ["文件下载"], "summary": "下载文件", "description": "通过文件ID下载文件", "security": [{"Authorization": [], "CsfAuth": []}], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "文件ID"}], "responses": {"200": {"description": "文件下载成功", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "文件不存在"}, "500": {"description": "服务器内部错误"}}}}, "/file/multipart/init": {"post": {"tags": ["分片上传"], "summary": "初始化分片上传", "description": "初始化大文件分片上传", "security": [{"Authorization": [], "CsfAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitMultipartUploadRequest"}}}}, "responses": {"200": {"description": "初始化成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"$ref": "#/components/schemas/InitMultipartUploadResponse"}}}]}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/file/multipart/uploadPart": {"post": {"tags": ["分片上传"], "summary": "上传文件分片", "description": "上传文件的单个分片", "security": [{"Authorization": [], "CsfAuth": []}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "分片文件"}, "fileId": {"type": "string", "description": "文件ID"}, "objectName": {"type": "string", "description": "MinIO/S3 中的对象名"}, "bucketName": {"type": "string", "description": "存储的桶名"}, "uploadId": {"type": "string", "description": "Upload ID"}, "partNumber": {"type": "integer", "description": "分片编号"}}, "required": ["file", "fileId", "objectName", "bucketName", "uploadId", "partNumber"]}}}}, "responses": {"200": {"description": "分片上传成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"type": "string", "description": "分片的ETag"}}}]}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/file/multipart/complete": {"post": {"tags": ["分片上传"], "summary": "完成分片上传", "description": "完成分片上传并合并文件", "security": [{"Authorization": [], "CsfAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteMultipartUploadRequest"}}}}, "responses": {"200": {"description": "分片上传完成", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"$ref": "#/components/schemas/FileMetadataDTO"}}}]}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}, "/file/metadata/{fileId}": {"get": {"tags": ["文件管理"], "summary": "获取文件元数据", "description": "通过文件ID获取文件的详细元数据信息", "security": [{"Authorization": [], "CsfAuth": []}], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "文件ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"$ref": "#/components/schemas/FileMetadataDTO"}}}]}}}}, "404": {"description": "文件不存在"}, "500": {"description": "服务器内部错误"}}}}, "/file/accessUrl/{fileId}": {"get": {"tags": ["文件管理"], "summary": "获取文件直接访问URL", "description": "获取文件的直接访问URL（预签名URL或公共URL）", "security": [{"Authorization": [], "CsfAuth": []}], "parameters": [{"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "文件ID"}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"type": "string", "description": "文件直接访问URL"}}}]}}}}, "404": {"description": "文件不存在"}, "500": {"description": "服务器内部错误"}}}}, "/file/delete": {"delete": {"tags": ["文件管理"], "summary": "删除文件", "description": "批量删除文件（从MinIO和数据库中删除）", "security": [{"Authorization": [], "CsfAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "description": "要删除的文件ID列表", "example": ["abc123def456", "xyz789uvw012"]}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/R"}, {"properties": {"data": {"type": "boolean", "description": "删除操作结果"}}}]}}}}, "400": {"description": "请求参数错误"}, "500": {"description": "服务器内部错误"}}}}}, "tags": [{"name": "文件上传", "description": "文件上传相关接口"}, {"name": "文件下载", "description": "文件下载相关接口"}, {"name": "分片上传", "description": "大文件分片上传相关接口"}, {"name": "文件管理", "description": "文件元数据管理相关接口"}]}