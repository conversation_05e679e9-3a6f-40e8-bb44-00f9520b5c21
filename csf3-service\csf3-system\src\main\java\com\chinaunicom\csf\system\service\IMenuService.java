
package com.chinaunicom.csf.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseService;
import com.chinaunicom.csf.core.secure.CsfUser;
import com.chinaunicom.csf.core.tool.support.Kv;
import com.chinaunicom.csf.system.entity.Menu;
import com.chinaunicom.csf.system.vo.MenuVO;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 */
public interface IMenuService extends BaseService<Menu> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param menu
	 * @return
	 */
	IPage<MenuVO> selectMenuPage(IPage<MenuVO> page, MenuVO menu);

	/**
	 * 懒加载菜单列表
	 *
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<MenuVO> lazyMenuList(Long parentId, Map<String, Object> param);

	/**
	 * 菜单树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<MenuVO> routes(String roleId);

	/**
	 * 按钮树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<MenuVO> buttons(String roleId);

	/**
	 * 树形结构
	 *
	 * @return
	 */
	List<MenuVO> tree();

	/**
	 * 授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<MenuVO> grantTree(CsfUser user);

	/**
	 * 数据权限授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<MenuVO> grantDataScopeTree(CsfUser user);

	/**
	 * 接口权限授权树形结构
	 *
	 * @param user
	 * @return
	 */
	List<MenuVO> grantApiScopeTree(CsfUser user);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> roleTreeKeys(String roleIds);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> dataScopeTreeKeys(String roleIds);

	/**
	 * 默认选中节点
	 *
	 * @param roleIds
	 * @return
	 */
	List<String> apiScopeTreeKeys(String roleIds);

	/**
	 * 获取配置的角色权限
	 *
	 * @param user
	 * @return
	 */
	List<Kv> authRoutes(CsfUser user);

	/**
	 * 删除当前菜单及其子菜单
	 *
	 * @param menuId
	 * @return
	 */
	int deleteMenuAndChildren(Long menuId);

}
