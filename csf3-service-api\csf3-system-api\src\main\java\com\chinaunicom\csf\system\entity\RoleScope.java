
package com.chinaunicom.csf.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinaunicom.csf.core.mp.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 实体类
 */
@Data
@TableName("csf_role_scope")
@Schema(description = "RoleScope对象")
public class RoleScope extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 权限id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "权限id")
	private Long scopeId;

	/**
	 * 角色id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "角色id")
	private Long roleId;

	/**
	 * 权限类型
	 */
	@Schema(description = "权限类型")
	private Integer scopeCategory;


}
