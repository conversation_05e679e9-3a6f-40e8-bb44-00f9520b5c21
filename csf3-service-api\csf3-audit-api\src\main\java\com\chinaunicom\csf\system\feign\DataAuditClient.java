package com.chinaunicom.csf.system.feign;

import com.chinaunicom.csf.core.launch.constant.AppConstant;
import com.chinaunicom.csf.plugins.datasecurity.entity.AuditLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
	value = AppConstant.APPLICATION_SYSTEM_NAME,
	fallback = DataAuditClientFallback.class
)
public interface DataAuditClient {

	@PostMapping("/data-audit/logs")
	int log(@RequestBody AuditLog log);
}

