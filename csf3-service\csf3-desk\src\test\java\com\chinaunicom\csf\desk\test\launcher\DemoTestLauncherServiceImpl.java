
package com.chinaunicom.csf.desk.test.launcher;

import com.chinaunicom.csf.common.constant.LauncherConstant;
import com.chinaunicom.csf.core.launch.service.LauncherService;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 *
 */
public class DemoTestLauncherServiceImpl implements LauncherService {

	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile) {
		Properties props = System.getProperties();
		props.setProperty("spring.cloud.nacos.discovery.server-addr", LauncherConstant.NACOS_DEV_ADDR);
		props.setProperty("spring.cloud.nacos.config.server-addr", LauncherConstant.NACOS_DEV_ADDR);
		props.setProperty("spring.cloud.sentinel.transport.dashboard", LauncherConstant.SENTINEL_DEV_ADDR);
	}

	@Override
	public int getOrder() {
		return 10;
	}
}
