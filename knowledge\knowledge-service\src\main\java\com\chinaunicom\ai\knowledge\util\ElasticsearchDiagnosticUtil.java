package com.chinaunicom.ai.knowledge.util;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.indices.GetIndexRequest;
import co.elastic.clients.elasticsearch.indices.GetIndexResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import java.util.*;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Elasticsearch诊断工具类
 * 用于验证分词器配置和分词效果
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-11
 */
@Slf4j
@Component
public class ElasticsearchDiagnosticUtil {

    @Autowired
    private ElasticsearchClient elasticsearchClient;

    @Value("${spring.elasticsearch.uris}")
    private String elasticsearchUris;

    @Value("${spring.elasticsearch.username}")
    private String elasticsearchUsername;

    @Value("${spring.elasticsearch.password}")
    private String elasticsearchPassword;

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 检查索引的mapping配置
     * 验证text字段是否正确配置了ik_max_word分词器
     */
    public void checkIndexMapping(String indexName) {
        try {
            log.info("🔍 开始检查索引mapping配置 - 索引: {}", indexName);
            
            GetIndexRequest request = GetIndexRequest.of(builder -> builder.index(indexName));
            GetIndexResponse response = elasticsearchClient.indices().get(request);
            
            if (response.result().containsKey(indexName)) {
                var indexInfo = response.result().get(indexName);
                var mappings = indexInfo.mappings();
                
                if (mappings != null && mappings.properties() != null) {
                    var textProperty = mappings.properties().get("text");
                    if (textProperty != null && textProperty.isText()) {
                        var textMapping = textProperty.text();
                        
                        log.info("📋 text字段mapping配置:");
                        log.info("  - analyzer: {}", textMapping.analyzer());
                        log.info("  - searchAnalyzer: {}", textMapping.searchAnalyzer());
                        log.info("  - type: text");
                        
                        // 验证分词器配置
                        String analyzer = textMapping.analyzer();
                        String searchAnalyzer = textMapping.searchAnalyzer();

                        // 检查analyzer配置
                        boolean analyzerCorrect = "ik_max_word".equals(analyzer);

                        // 检查searchAnalyzer配置
                        // 如果searchAnalyzer为null，ES会默认使用analyzer的值，这是正常的
                        boolean searchAnalyzerCorrect = searchAnalyzer == null || "ik_max_word".equals(searchAnalyzer);

                        if (analyzerCorrect && searchAnalyzerCorrect) {
                            if (searchAnalyzer == null) {
                                log.info("✅ text字段分词器配置正确 - analyzer: ik_max_word, searchAnalyzer: 默认使用analyzer值");
                            } else {
                                log.info("✅ text字段分词器配置正确 - analyzer: ik_max_word, searchAnalyzer: ik_max_word");
                            }
                        } else {
                            log.warn("❌ text字段分词器配置异常:");
                            if (!analyzerCorrect) {
                                log.warn("  - analyzer配置错误: 期望 ik_max_word, 实际: {}", analyzer);
                            }
                            if (!searchAnalyzerCorrect) {
                                log.warn("  - searchAnalyzer配置错误: 期望 ik_max_word 或 null, 实际: {}", searchAnalyzer);
                            }
                        }
                    } else {
                        log.warn("❌ 未找到text字段或字段类型不是text");
                    }
                } else {
                    log.warn("❌ 索引mappings为空");
                }
            } else {
                log.warn("❌ 索引不存在: {}", indexName);
            }
            
        } catch (Exception e) {
            log.error("检查索引mapping配置失败 - 索引: {}, 错误: {}", indexName, e.getMessage(), e);
        }
    }

    /**
     * 测试分词效果
     * 验证指定文本的分词结果
     */
    public Map<String, Object> testTokenization(String indexName, String text) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔍 开始测试分词效果 - 索引: {}, 文本: '{}'", indexName, text);

            // 构建分析请求
            Map<String, Object> analyzeRequest = new HashMap<>();
            analyzeRequest.put("analyzer", "ik_max_word");
            analyzeRequest.put("text", text);

            // 调用Elasticsearch分析API
            Map<String, Object> response = callElasticsearchAnalyzeAPI(indexName, analyzeRequest);

            if (response != null && response.containsKey("tokens")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> tokens = (List<Map<String, Object>>) response.get("tokens");

                log.info("📋 ik_max_word分词结果 (共{}个词):", tokens.size());
                List<String> tokenStrings = new ArrayList<>();

                for (int i = 0; i < tokens.size(); i++) {
                    Map<String, Object> token = tokens.get(i);
                    String tokenText = (String) token.get("token");
                    Integer startOffset = (Integer) token.get("start_offset");
                    Integer endOffset = (Integer) token.get("end_offset");

                    log.info("  {}. '{}' (位置: {}-{})", i + 1, tokenText, startOffset, endOffset);
                    tokenStrings.add(tokenText);
                }

                log.info("📋 分词结果摘要: {}", tokenStrings);

                result.put("status", "success");
                result.put("tokens", tokens);
                result.put("tokenStrings", tokenStrings);
                result.put("tokenCount", tokens.size());
            } else {
                log.warn("❌ 分析API返回结果格式异常");
                result.put("status", "error");
                result.put("message", "分析API返回结果格式异常");
            }

        } catch (Exception e) {
            log.error("测试分词效果失败 - 索引: {}, 文本: '{}', 错误: {}",
                    indexName, text, e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "测试分词效果失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 调用Elasticsearch分析API
     */
    private Map<String, Object> callElasticsearchAnalyzeAPI(String indexName, Map<String, Object> analyzeRequest) {
        try {
            // 构建请求URL
            String url = elasticsearchUris + "/" + indexName + "/_analyze";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 添加Basic认证
            if (elasticsearchUsername != null && elasticsearchPassword != null) {
                String auth = elasticsearchUsername + ":" + elasticsearchPassword;
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
                headers.set("Authorization", "Basic " + encodedAuth);
            }

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(analyzeRequest, headers);

            // 发送请求
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                Map.class
            );

            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = response.getBody();
            return responseBody;

        } catch (HttpClientErrorException e) {
            log.error("调用Elasticsearch分析API失败 - HTTP错误: {}, 响应: {}",
                    e.getStatusCode(), e.getResponseBodyAsString());
            return null;
        } catch (Exception e) {
            log.error("调用Elasticsearch分析API失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 对比不同分词器的效果
     */
    public Map<String, Object> compareAnalyzers(String text) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔍 对比不同分词器效果 - 文本: '{}'", text);

            // 测试standard分词器
            Map<String, Object> standardResult = testAnalyzer("standard", text);

            // 测试ik_max_word分词器
            Map<String, Object> ikResult = testAnalyzer("ik_max_word", text);

            result.put("status", "success");
            result.put("text", text);
            result.put("standard", standardResult);
            result.put("ik_max_word", ikResult);

            // 对比分析
            if (standardResult.containsKey("tokenStrings") && ikResult.containsKey("tokenStrings")) {
                @SuppressWarnings("unchecked")
                List<String> standardTokens = (List<String>) standardResult.get("tokenStrings");
                @SuppressWarnings("unchecked")
                List<String> ikTokens = (List<String>) ikResult.get("tokenStrings");

                log.info("📋 分词器对比结果:");
                log.info("  - standard分词器: {} 个词 - {}", standardTokens.size(), standardTokens);
                log.info("  - ik_max_word分词器: {} 个词 - {}", ikTokens.size(), ikTokens);

                Map<String, Object> comparison = new HashMap<>();
                comparison.put("standardTokenCount", standardTokens.size());
                comparison.put("ikTokenCount", ikTokens.size());
                comparison.put("difference", ikTokens.size() - standardTokens.size());
                result.put("comparison", comparison);
            }

        } catch (Exception e) {
            log.error("对比分词器效果失败 - 文本: '{}', 错误: {}", text, e.getMessage(), e);
            result.put("status", "error");
            result.put("message", "对比分词器效果失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 测试指定分词器的效果
     */
    private Map<String, Object> testAnalyzer(String analyzerName, String text) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 构建分析请求
            Map<String, Object> analyzeRequest = new HashMap<>();
            analyzeRequest.put("analyzer", analyzerName);
            analyzeRequest.put("text", text);

            // 调用通用分析API（不指定索引）
            Map<String, Object> response = callElasticsearchGeneralAnalyzeAPI(analyzeRequest);

            if (response != null && response.containsKey("tokens")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> tokens = (List<Map<String, Object>>) response.get("tokens");

                List<String> tokenStrings = new ArrayList<>();
                for (Map<String, Object> token : tokens) {
                    tokenStrings.add((String) token.get("token"));
                }

                log.info("📋 {} 分词结果: {}", analyzerName, tokenStrings);

                result.put("status", "success");
                result.put("analyzer", analyzerName);
                result.put("tokens", tokens);
                result.put("tokenStrings", tokenStrings);
                result.put("tokenCount", tokens.size());
            } else {
                log.warn("❌ {}分词器分析失败", analyzerName);
                result.put("status", "error");
                result.put("message", analyzerName + "分词器分析失败");
            }

        } catch (Exception e) {
            log.warn("测试分词器失败 - 分词器: {}, 错误: {}", analyzerName, e.getMessage());
            result.put("status", "error");
            result.put("message", "测试分词器失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 调用Elasticsearch通用分析API（不指定索引）
     */
    private Map<String, Object> callElasticsearchGeneralAnalyzeAPI(Map<String, Object> analyzeRequest) {
        try {
            // 构建请求URL（通用分析API）
            String url = elasticsearchUris + "/_analyze";

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 添加Basic认证
            if (elasticsearchUsername != null && elasticsearchPassword != null) {
                String auth = elasticsearchUsername + ":" + elasticsearchPassword;
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
                headers.set("Authorization", "Basic " + encodedAuth);
            }

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(analyzeRequest, headers);

            // 发送请求
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                Map.class
            );

            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = response.getBody();
            return responseBody;

        } catch (HttpClientErrorException e) {
            log.error("调用Elasticsearch通用分析API失败 - HTTP错误: {}, 响应: {}",
                    e.getStatusCode(), e.getResponseBodyAsString());
            return null;
        } catch (Exception e) {
            log.error("调用Elasticsearch通用分析API失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查索引是否存在ik分词器插件
     */
    public Map<String, Object> checkIkPlugin() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("🔍 检查IK分词器插件是否可用");

            // 尝试使用ik_max_word分词器分析简单文本
            Map<String, Object> ikResult = testAnalyzer("ik_max_word", "中文分词测试");

            if ("success".equals(ikResult.get("status"))) {
                log.info("✅ IK分词器插件可用");
                result.put("status", "success");
                result.put("message", "IK分词器插件可用");
                result.put("ikPluginAvailable", true);
                result.put("testResult", ikResult);
            } else {
                log.error("❌ IK分词器插件不可用或配置错误");
                result.put("status", "error");
                result.put("message", "IK分词器插件不可用或配置错误");
                result.put("ikPluginAvailable", false);
                result.put("testResult", ikResult);
            }

        } catch (Exception e) {
            log.error("❌ 检查IK分词器插件失败: {}", e.getMessage());
            result.put("status", "error");
            result.put("message", "检查IK分词器插件失败: " + e.getMessage());
            result.put("ikPluginAvailable", false);
        }

        return result;
    }
}
