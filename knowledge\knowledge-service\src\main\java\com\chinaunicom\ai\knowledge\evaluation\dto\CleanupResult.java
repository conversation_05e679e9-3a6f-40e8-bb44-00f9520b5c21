package com.chinaunicom.ai.knowledge.evaluation.dto;

import lombok.Data;

import java.util.List;

/**
 * 数据清理结果DTO
 */
@Data
public class CleanupResult {
    
    /**
     * 清理状态：SUCCESS-成功, FAILED-失败, PARTIAL-部分成功
     */
    private String status;
    
    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;
    
    /**
     * 删除的评测文档数量
     */
    private Integer deletedDocuments;
    
    /**
     * 删除的测试历史数量
     */
    private Integer deletedTestHistory;
    
    /**
     * 删除的知识库文档数量
     */
    private Integer deletedKnowledgeDocuments;
    
    /**
     * 删除的MinIO文件数量
     */
    private Integer deletedFiles;
    
    /**
     * 清理失败的文件列表
     */
    private List<String> failedFiles;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 清理耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 是否清理了MinIO文件
     */
    private Boolean cleanedFiles;
    
    /**
     * 是否清理了Elasticsearch索引
     */
    private Boolean cleanedIndex;
    
    public static CleanupResult success(Long knowledgeBaseId, Integer deletedDocuments, 
                                      Integer deletedTestHistory, Integer deletedKnowledgeDocuments,
                                      Integer deletedFiles, Long duration, Boolean cleanedFiles, Boolean cleanedIndex) {
        CleanupResult result = new CleanupResult();
        result.setStatus("SUCCESS");
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setDeletedDocuments(deletedDocuments);
        result.setDeletedTestHistory(deletedTestHistory);
        result.setDeletedKnowledgeDocuments(deletedKnowledgeDocuments);
        result.setDeletedFiles(deletedFiles);
        result.setDuration(duration);
        result.setCleanedFiles(cleanedFiles);
        result.setCleanedIndex(cleanedIndex);
        return result;
    }
    
    public static CleanupResult partial(Long knowledgeBaseId, Integer deletedDocuments, 
                                      Integer deletedTestHistory, Integer deletedKnowledgeDocuments,
                                      Integer deletedFiles, List<String> failedFiles, Long duration,
                                      Boolean cleanedFiles, Boolean cleanedIndex) {
        CleanupResult result = new CleanupResult();
        result.setStatus("PARTIAL");
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setDeletedDocuments(deletedDocuments);
        result.setDeletedTestHistory(deletedTestHistory);
        result.setDeletedKnowledgeDocuments(deletedKnowledgeDocuments);
        result.setDeletedFiles(deletedFiles);
        result.setFailedFiles(failedFiles);
        result.setDuration(duration);
        result.setCleanedFiles(cleanedFiles);
        result.setCleanedIndex(cleanedIndex);
        return result;
    }
    
    public static CleanupResult failed(String errorMessage) {
        CleanupResult result = new CleanupResult();
        result.setStatus("FAILED");
        result.setErrorMessage(errorMessage);
        return result;
    }
}
