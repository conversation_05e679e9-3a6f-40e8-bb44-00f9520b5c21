package com.chinaunicom.csf.system.wrapper;


import com.chinaunicom.csf.core.mp.support.BaseEntityWrapper;
import com.chinaunicom.csf.core.tool.utils.BeanUtil;
import com.chinaunicom.csf.core.tool.utils.SpringUtil;
import com.chinaunicom.csf.system.entity.ApiScope;
import com.chinaunicom.csf.system.service.IDictService;
import com.chinaunicom.csf.system.vo.ApiScopeVO;

import java.util.Objects;


/**
 * 包装类,返回视图层所需的字段
 */
public class ApiScopeWrapper extends BaseEntityWrapper<ApiScope, ApiScopeVO> {
	private static IDictService dictService;

	static {
		dictService = SpringUtil.getBean(IDictService.class);
	}

	public static ApiScopeWrapper build() {
		return new ApiScopeWrapper();
	}

	@Override
	public ApiScopeVO entityVO(ApiScope dataScope) {
		ApiScopeVO apiScopeVO = Objects.requireNonNull(BeanUtil.copy(dataScope, ApiScopeVO.class));
		String scopeTypeName = dictService.getValue("api_scope_type", dataScope.getScopeType());
		apiScopeVO.setScopeTypeName(scopeTypeName);
		return apiScopeVO;
	}

}
