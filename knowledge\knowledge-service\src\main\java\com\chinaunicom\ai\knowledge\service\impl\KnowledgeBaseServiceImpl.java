package com.chinaunicom.ai.knowledge.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseCreateDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseQueryDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseUpdateDTO;
import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.enums.ModelTypeEnum;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.KnowledgeBaseService;
import com.chinaunicom.ai.knowledge.service.RerankService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseDetailVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseListVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import com.chinaunicom.csf.core.log.exception.ServiceException;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import com.chinaunicom.csf.core.tool.api.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识库 服务实现类
 * @date 2025/6/3 17:14
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> implements KnowledgeBaseService {
    @Autowired
    private AiModelMapper aiModelMapper;
    @Autowired
    private KnowledgeDocumentMapper knowledgeDocumentMapper;
    @Autowired
    private IElasticSearchService elasticSearchService; // 注入接口
    @Autowired
    private FileUploadService fileUploadService; // 用于删除MinIO文件
    @Autowired
    private PythonEmbeddingService pythonEmbeddingService; // 注入 PythonEmbeddingService
    @Autowired
    private RerankService rerankService; // 注入 RerankService
    @Autowired
    private HybridSearchConfig hybridSearchConfig; // 注入混合检索配置

    /**
     * 创建知识库
     * 在MySQL创建知识库记录的同时，去ElasticSearch上创建一个对应的index。
     * @param createDTO 知识库创建DTO
     * @return 是否创建成功
     */
    @Override
    @Transactional // 事务管理，确保MySQL和ES操作的原子性
    public R<Boolean> createKnowledgeBase(KnowledgeBaseCreateDTO createDTO) {
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();

        // 1. 检查关联模型是否存在且为向量嵌入模型
        AiModel aiModel = aiModelMapper.selectById(createDTO.getVecModel());
        if (aiModel == null || !ModelTypeEnum.EMBEDDING.getCode().equals(aiModel.getModelType())) {
            throw new ServiceException("关联模型不存在或不是向量嵌入模型");
        }
        // 检查模型是否属于当前租户
        if (!tenantId.equals(aiModel.getTenantId())) {
            throw new ServiceException("无权使用该模型");
        }

        // 2. 构造知识库实体
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        BeanUtils.copyProperties(createDTO, knowledgeBase);
        knowledgeBase.setCreateUser(userId);
        knowledgeBase.setCreateTime(LocalDateTime.now());
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        knowledgeBase.setUpdateUser(userId);
        knowledgeBase.setIsDeleted(0); // 未删除
        knowledgeBase.setStatus(0); // 默认状态
        knowledgeBase.setTenantId(tenantId);

        // 3. 在MySQL中创建知识库记录
        int insertCount = baseMapper.insert(knowledgeBase);
        if (insertCount <= 0) {
            log.error("知识库创建失败，MySQL插入失败。名称: {}", createDTO.getName());
            return R.fail("知识库创建失败");
        }

        // 生成唯一的ES索引名称，建议加上租户ID前缀，避免不同租户间索引名称冲突
        String indexName = "kb-" + tenantId.toLowerCase() + "-" + knowledgeBase.getId();
        knowledgeBase.setIndexName(indexName);
        baseMapper.updateById(knowledgeBase);

        // 4. 在ElasticSearch中创建对应的索引
        // 使用ai_model表的dims作为dense_vector的维度
        boolean esIndexCreated = elasticSearchService.createIndex(indexName, aiModel.getDims());
        if (!esIndexCreated) {
            // 如果ES索引创建失败，抛出异常以触发事务回滚
            throw new ServiceException("ElasticSearch索引创建失败，知识库创建回滚");
        }

        return R.data(true);
    }

    /**
     * 编辑知识库
     * @param updateDTO 知识库更新DTO
     * @return 是否编辑成功
     */
    @Override
    @Transactional
    public R<Boolean> updateKnowledgeBase(KnowledgeBaseUpdateDTO updateDTO) {
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();

        // 1. 检查知识库是否存在且属于当前租户
        KnowledgeBase existingKnowledgeBase = baseMapper.selectById(updateDTO.getId());
        if (existingKnowledgeBase == null || existingKnowledgeBase.getIsDeleted() == 1 || !tenantId.equals(existingKnowledgeBase.getTenantId())) {
            throw new ServiceException("知识库不存在或无权操作");
        }

        // 2. 更新知识库实体（只更新允许修改的字段：name 和 descrip）
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        knowledgeBase.setId(updateDTO.getId());
        knowledgeBase.setName(updateDTO.getName());
        knowledgeBase.setDescrip(updateDTO.getDescrip());
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        knowledgeBase.setUpdateUser(userId);

        // 注意：vecModel 字段不允许修改，以避免复杂的ES索引重建和文档重新向量化操作。
        // 如果需要更换向量模型，建议创建新的知识库。

        int updateCount = baseMapper.updateById(knowledgeBase);
        return R.data(updateCount > 0);
    }

    /**
     * 查询知识库详情
     * 根据知识库id查询知识库名称、知识库描述、关联模型id以及模型名称。
     * @param id 知识库ID
     * @return 知识库详情VO
     */
    @Override
    public R<KnowledgeBaseDetailVO> getKnowledgeBaseDetail(Long id) {
        String tenantId = SecureUtil.getTenantId();

        // 1. 查询知识库基本信息
        KnowledgeBase knowledgeBase = baseMapper.selectById(id);
        if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
            throw new ServiceException("知识库不存在或无权操作");
        }

        // 2. 查询关联模型名称
        AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
        String vecModelName = (aiModel != null) ? aiModel.getDisplayName() : "未知模型";

        // 3. 封装VO
        KnowledgeBaseDetailVO detailVO = new KnowledgeBaseDetailVO();
        BeanUtils.copyProperties(knowledgeBase, detailVO);
        detailVO.setVecModelName(vecModelName);

        return R.data(detailVO);
    }

    /**
     * 查询知识库列表
     * 需要支持使用知识库名称模糊查询，列表需要展示知识库名称、知识库下文档的数量、最近更新时间。需要支持分页。
     * @param queryDTO 查询DTO
     * @return 知识库列表分页数据
     */
    @Override
    public R<IPage<KnowledgeBaseListVO>> getKnowledgeBaseList(KnowledgeBaseQueryDTO queryDTO) {
        String tenantId = SecureUtil.getTenantId();
        if (StringUtils.isNotBlank(queryDTO.getTenantId())) {
            tenantId = queryDTO.getTenantId();
        }
        if (StringUtils.isBlank(tenantId)) {
            throw new ServiceException("请登录");
        }

        Page<KnowledgeBaseListVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        // 调用Mapper中的自定义SQL，实现文档数量和最近更新时间的统计
        IPage<KnowledgeBaseListVO> resultPage = baseMapper.selectKnowledgeBaseList(page, queryDTO.getName(), tenantId);

        return R.data(resultPage);
    }

    /**
     * 删除知识库
     * 逻辑删除mysql中的知识库（在knowledge_base表），逻辑删除知识库下的文档记录（在knowledge_document表），
     * 物理删除ElasticSearch上对应的index，物理删除minio上面对应的文档。
     * @param id 知识库ID
     * @return 是否删除成功
     */
    @Override
    @Transactional
    public R<Boolean> deleteKnowledgeBase(Long id) {
        String tenantId = SecureUtil.getTenantId();
        Long userId = SecureUtil.getUserId();

        // 1. 检查知识库是否存在且属于当前租户
        KnowledgeBase knowledgeBase = baseMapper.selectById(id);
        if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
            throw new ServiceException("知识库不存在或无权操作");
        }

        // 2. 逻辑删除MySQL中的知识库记录
        knowledgeBase.setIsDeleted(1);
        knowledgeBase.setUpdateTime(LocalDateTime.now());
        knowledgeBase.setUpdateUser(userId);
        int baseUpdateCount = baseMapper.updateById(knowledgeBase);
        if (baseUpdateCount <= 0) {
            log.error("逻辑删除知识库失败，知识库ID: {}", id);
            return R.fail("删除失败");
        }

        // 获取该知识库下所有未删除的文档，以便物理删除MinIO文件
        List<KnowledgeDocument> documentsToDeleteFromMinio = knowledgeDocumentMapper.selectList(
                new LambdaQueryWrapper<KnowledgeDocument>()
                        .eq(KnowledgeDocument::getBaseId, id)
                        .eq(KnowledgeDocument::getTenantId, tenantId)
                        .eq(KnowledgeDocument::getIsDeleted, 0) // 查询逻辑删除前的文档，以便获取fileObjectId
        );

        for (KnowledgeDocument doc : documentsToDeleteFromMinio) {
            // 物理删除MinIO上的文件
            try {
                fileUploadService.deleteMinioFile(doc.getFileObjectId());
            } catch (Exception e) {
                log.error("删除MinIO文件失败，文件对象ID: {}，知识库ID: {}", doc.getFileObjectId(), id, e);
                // 即使MinIO删除失败，也继续处理，但记录日志，不影响事务回滚
            }
        }

        // 3. 逻辑删除知识库下的文档记录，并物理删除MinIO上的文件
        // 使用自定义Mapper方法进行批量逻辑删除
        knowledgeDocumentMapper.logicalDeleteByBaseId(id, tenantId, DocumentStatusEnum.DELETED.getCode(), LocalDateTime.now(), userId);

        // 4. 物理删除ElasticSearch上对应的索引
        boolean esIndexDeleted = elasticSearchService.deleteIndex(knowledgeBase.getIndexName());
        if (!esIndexDeleted) {
            log.error("ElasticSearch索引删除失败，索引名称: {}，知识库ID: {}", knowledgeBase.getIndexName(), id);
            // ES索引删除失败不抛出异常，因为MySQL和MinIO的删除已经完成，避免回滚整个事务
            // 实际中可能需要告警或补偿机制
        }

        return R.data(true);
    }

    /**
     * 智能体接口：根据向量去知识库对应的向量数据库ES中匹配文档片段
     * @param knowledgeBaseId 知识库ID
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @return 匹配的文档片段列表
     */
    @Override
    public List<AgentSearchResultVO> searchKnowledgeBaseByVector(Long knowledgeBaseId, float[] queryVector, Integer topK) {
        String tenantId = SecureUtil.getTenantId();

        // 1. 检查知识库是否存在且属于当前租户
        KnowledgeBase knowledgeBase = baseMapper.selectById(knowledgeBaseId);
        if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
            throw new ServiceException("知识库不存在或无权操作");
        }

        // 2. 调用ElasticSearch服务进行向量搜索
        List<AgentSearchResultVO> results = elasticSearchService.searchVector(
                knowledgeBase.getIndexName(),
                queryVector,
                topK,
                knowledgeBaseId,
                tenantId
        );

        // 3. 补充知识库名称 (ES中可能没有直接存储知识库名称)
        String knowledgeBaseName = knowledgeBase.getName();
        results.forEach(vo -> vo.setKnowledgeBaseName(knowledgeBaseName));

        return results;
    }

    /**
     * 智能体接口：知识库文档召回
     * 根据知识库ID列表和问题文本，进行向量化后在多个知识库中检索，召回相关文档片段。
     * 对于每个知识库，根据其关联的模型调用Python服务进行向量化，然后进行ES搜索。
     * @param kbIds 知识库ID列表
     * @param queryText 问题文本
     * @param topK 召回文档数量
     * @return 召回的文档片段列表
     */
    @Override
    public List<RecallDocSegment> recallDocuments(List<Long> kbIds, String queryText, Integer topK) {
        String tenantId = SecureUtil.getTenantId();
        // 先屏蔽tenantId
        if (StringUtils.isBlank(tenantId)) {
            tenantId = "000000";
        }
        List<RecallDocSegment> allRecalledSegments = new ArrayList<>();

        if (kbIds == null || kbIds.isEmpty()) {
            log.warn("召回请求中知识库ID列表为空，不执行召回操作。");
            return allRecalledSegments;
        }
        if (!StringUtils.hasText(queryText)) {
            log.warn("召回请求中问题文本为空，不执行召回操作。");
            return allRecalledSegments;
        }
        if (topK == null || topK <= 0) {
            log.warn("召回请求中topK参数无效: {}，使用默认值5", topK);
            topK = 5;
        }

        // 用于缓存每个模型的向量，避免重复调用Python服务
        Map<String, float[]> modelToVectorCache = new HashMap<>();

        // 遍历每个知识库ID进行检索
        for (Long kbId : kbIds) {
            // 1. 检查知识库是否存在且属于当前租户
            KnowledgeBase knowledgeBase = baseMapper.selectById(kbId);
            if (knowledgeBase == null || knowledgeBase.getIsDeleted() == 1 || !tenantId.equals(knowledgeBase.getTenantId())) {
                log.warn("召回操作跳过无效知识库ID: {} (不存在、已删除或无权操作)", kbId);
                continue; // 跳过当前知识库，继续处理下一个
            }

            // 2. 获取该知识库关联的向量模型名称
            AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
            if (aiModel == null || !ModelTypeEnum.EMBEDDING.getCode().equals(aiModel.getModelType())) {
                log.warn("召回操作跳过知识库ID: {}，因为未找到有效关联的向量嵌入模型或模型类型不匹配。", kbId);
                continue;
            }
            String embedModelName = aiModel.getDisplayName();

            // 3. 获取问题文本的向量（从缓存获取或调用Python服务）
            float[] queryVector = modelToVectorCache.get(embedModelName);
            if (queryVector == null) {
                try {
                    queryVector = pythonEmbeddingService.vectorizeText(queryText, embedModelName); // 调用Python向量化服务
                    if (queryVector == null || queryVector.length == 0) {
                        log.warn("知识库ID: {} 对应的模型 {} 向量化后的问题向量为空，跳过ES搜索。", kbId, embedModelName);
                        continue;
                    }
                    modelToVectorCache.put(embedModelName, queryVector); // 缓存向量
                } catch (ServiceException e) {
                    log.error("知识库ID: {} 的问题文本向量化失败，模型: {}，错误: {}", kbId, embedModelName, e.getMessage());
                    continue; // 向量化失败，跳过当前知识库的ES搜索
                }
            }

            // 4. 调用ElasticSearch服务进行检索（支持混合检索），使用传递的topK参数
            List<AgentSearchResultVO> searchResults;

            // 根据配置决定使用混合检索还是纯向量检索
            if (hybridSearchConfig.getEnabled() && hybridSearchConfig.isValid()) {
                log.debug("使用混合检索 - 知识库: {}, 查询: '{}', topK: {}", kbId, queryText, topK);
                searchResults = elasticSearchService.searchHybrid(
                        knowledgeBase.getIndexName(),
                        queryText,
                        queryVector,
                        topK,
                        kbId,
                        tenantId,
                        hybridSearchConfig
                );
            } else {
                log.debug("使用纯向量检索 - 知识库: {}, 查询: '{}', topK: {}", kbId, queryText, topK);
                searchResults = elasticSearchService.searchVector(
                        knowledgeBase.getIndexName(),
                        queryVector, // 使用当前知识库对应模型的queryVector
                        topK,
                        kbId, // 传入知识库ID作为过滤条件
                        tenantId
                );
            }

            // 5. 将 AgentSearchResultVO 转换为 RecallDocSegment
            List<RecallDocSegment> segments = searchResults.stream()
                    .map(result -> {
                        RecallDocSegment segment = new RecallDocSegment();
                        segment.setDocId(result.getDocumentId());
                        segment.setDocTitle(result.getDocumentName()); // 使用文档名称作为文档标题
                        segment.setContent(result.getFragmentContent());
                        segment.setScore(result.getScore()); // 设置ES搜索的原始分数
                        return segment;
                    })
                    .collect(Collectors.toList());

            allRecalledSegments.addAll(segments);
        }

        // 6. 记录召回结果
        if (allRecalledSegments.isEmpty() || allRecalledSegments.size() == 1) {
            return allRecalledSegments;
        }

        // 7. 使用Rerank服务对召回结果进行重排序
        try {
            List<RecallDocSegment> rerankedSegments = rerankService.rerankDocuments(queryText, allRecalledSegments, null);
            log.info("知识库召回成功（含Rerank重排序） - 查询: '{}', 召回片段数: {}, topK: {}", queryText, rerankedSegments.size(), topK);
            return rerankedSegments;
        } catch (Exception e) {
            log.warn("Rerank重排序失败，返回原始召回结果 - 查询: '{}', Error: {}", queryText, e.getMessage());
            log.info("知识库召回成功（原始排序） - 查询: '{}', 召回片段数: {}, topK: {}", queryText, allRecalledSegments.size(), topK);
            return allRecalledSegments;
        }
    }


}
