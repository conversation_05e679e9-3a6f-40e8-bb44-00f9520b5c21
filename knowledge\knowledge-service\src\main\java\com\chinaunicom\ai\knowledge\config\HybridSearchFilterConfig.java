package com.chinaunicom.ai.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 混合检索过滤配置类
 * 用于配置混合检索的分数阈值过滤机制
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-16
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "hybrid-search.filter")
public class HybridSearchFilterConfig {
    
    /**
     * 是否启用混合检索分数过滤
     * 默认值：true
     */
    private boolean enabled = true;
    
    /**
     * 混合检索最低分数阈值
     * 低于此阈值的结果将被过滤
     * 基于ES混合检索分数范围(0-10+)设计
     * 默认值：7.0
     */
    private double minimumScore = 7.0;
    
    /**
     * 是否启用过滤调试日志
     * 默认值：false
     */
    private boolean enableDebugLog = false;
    
    /**
     * 验证配置的有效性
     * @return 配置是否有效
     */
    public boolean isValid() {
        return minimumScore >= 0.0 && minimumScore <= 10.0;
    }
    
    /**
     * 获取配置描述信息
     * @return 配置描述
     */
    public String getConfigDescription() {
        return String.format("HybridSearchFilter[enabled=%s, minimumScore=%.1f, debugLog=%s]", 
                enabled, minimumScore, enableDebugLog);
    }
}
