
package com.chinaunicom.csf.system.user.vo;

import com.chinaunicom.csf.system.user.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "UserVO对象")
public class UserVO extends User {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 角色名
	 */
	private String roleName;

	/**
	 * 部门名
	 */
	private String deptName;

	/**
	 * 岗位名
	 */
	private String postName;

	/**
	 * 性别
	 */
	private String sexName;

	/**
	 * 租户名称
	 */
	private String tenantName;

	/**
	 * 对外隐藏password
	 */
	@JsonIgnore
	private String password;

	@JsonIgnore
	private String salt;
}
