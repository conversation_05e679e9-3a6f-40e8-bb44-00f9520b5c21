server:
  port: 80

spring:
  application:
    name: csf-gateway
  profiles:
    active: dev
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
    nacos:
      discovery:
        server-addr: 10.81.9.154:8848
        namespace: agent-factory-hwm
        register-enabled: true
        enabled: true
      config:
        server-addr: 10.81.9.154:8848
        namespace: agent-factory-dev
        refresh-enabled: true
        username: nacos
        password: Cyhlw@nacos2023
        enabled: true
        prefix: ${spring.application.name}
        file-extension: yml
      username: nacos
      password: <PERSON><PERSON><PERSON>@nacos2023
    loadbalancer:
      retry:
        enabled: true

logging:
  pattern:
    console: "%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(%5p [${spring.application.name:-},%X{X-B3-traceId:-},%X{X-B3-spanId:-},%X{X-Span-Export:-}]) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"


