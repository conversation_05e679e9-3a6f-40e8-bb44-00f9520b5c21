
package com.chinaunicom.csf.desk.feign;

import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.desk.entity.Notice;
import com.chinaunicom.csf.desk.mapper.NoticeMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Notice Feign
 *
 */
@Tag(name = "ExcludedTag")
@RestController
@AllArgsConstructor
public class NoticeClient implements INoticeClient {

	NoticeMapper mapper;

	@Override
	@GetMapping(API_PREFIX + "/top")
	public R<List<Notice>> top(Integer number) {
		return R.data(mapper.topList(number));
	}

}
