package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 文档重命名请求DTO
 */
@Data
@Schema(description = "文档重命名请求DTO")
public class DocumentRenameDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "文档ID不能为空")
    @Schema(description = "文档ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "101")
    private Long documentId;

    @NotBlank(message = "新的文件名前缀不能为空")
    @Schema(description = "新的文件名前缀（不包含后缀）", requiredMode = Schema.RequiredMode.REQUIRED, example = "新文档名称")
    private String newFileNamePrefix;
}
