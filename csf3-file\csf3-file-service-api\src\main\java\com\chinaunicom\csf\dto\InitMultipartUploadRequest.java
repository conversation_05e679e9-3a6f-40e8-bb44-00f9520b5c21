package com.chinaunicom.csf.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 初始化分片上传请求DTO
 */
@Data
public class InitMultipartUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 业务类型 (例如: 'avatar', 'document')
     */
    private String bizType;
} 