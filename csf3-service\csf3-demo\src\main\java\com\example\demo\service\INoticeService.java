
package com.example.demo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseService;
import com.example.demo.entity.Notice;

/**
 * 服务类
 *
 */
public interface INoticeService extends BaseService<Notice> {

	/**
	 * 自定义分页
	 * @param page
	 * @param notice
	 * @return
	 */
	IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice);

	boolean updateContent(Long id, String content);
}
