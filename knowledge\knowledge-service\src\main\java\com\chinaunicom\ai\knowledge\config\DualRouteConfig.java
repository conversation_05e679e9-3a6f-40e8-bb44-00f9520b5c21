package com.chinaunicom.ai.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 双路检索配置类
 * 支持精确匹配和混合检索的权重配置，简化版多路检索方案
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-08
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "dual-route-search")
public class DualRouteConfig {
    
    /**
     * 是否启用双路检索功能
     * 默认关闭，需要显式配置启用
     */
    private Boolean enabled = false;
    
    /**
     * 精确匹配权重 (0.0-1.0)
     * 建议值：0.3-0.5，用于捕获关键词精确匹配
     */
    private Double exactMatchWeight = 0.4;

    /**
     * 混合检索权重 (0.0-1.0)
     * 建议值：0.5-0.7，保持语义理解的主导地位
     */
    private Double hybridSearchWeight = 0.6;
    
    /**
     * 去重阈值 (0.0-1.0)
     * 用于基础去重处理，阈值越高去重越严格
     */
    private Double duplicateThreshold = 0.9;
    
    /**
     * 最终返回结果数量
     * 控制融合后的最终结果数量
     */
    private Integer maxFinalResults = 10;
    
    /**
     * 检索超时时间（秒）
     * 控制双路并行检索的超时时间
     */
    private Integer searchTimeoutSeconds = 3;
    
    /**
     * 验证配置参数的有效性
     * 
     * @return 配置是否有效
     */
    public boolean isValid() {
        // 权重验证
        if (exactMatchWeight < 0 || exactMatchWeight > 1 || 
            hybridSearchWeight < 0 || hybridSearchWeight > 1) {
            return false;
        }
        
        // 权重和验证（允许一定的浮点误差）
        double weightSum = exactMatchWeight + hybridSearchWeight;
        if (Math.abs(weightSum - 1.0) > 0.01) {
            return false;
        }
        
        // 去重阈值验证
        if (duplicateThreshold < 0.0 || duplicateThreshold > 1.0) {
            return false;
        }
        
        // 超时时间验证
        if (searchTimeoutSeconds <= 0 || searchTimeoutSeconds > 10) {
            return false;
        }
        
        // 最终结果数量验证
        if (maxFinalResults <= 0 || maxFinalResults > 100) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取标准化的权重配置
     * 确保权重和为1.0
     * 
     * @return 标准化后的配置
     */
    public DualRouteConfig normalize() {
        double weightSum = exactMatchWeight + hybridSearchWeight;
        if (weightSum > 0 && Math.abs(weightSum - 1.0) > 0.01) {
            this.exactMatchWeight = exactMatchWeight / weightSum;
            this.hybridSearchWeight = hybridSearchWeight / weightSum;
        }
        return this;
    }
}
