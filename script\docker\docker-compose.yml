version: '3'
services:
  nacos:
    image: ${REGISTER}/nacos/nacos-server:v2.1.2
    hostname: "nacos-standalone"
    environment:
      - MODE=standalone
    volumes:
      - /docker/nacos/standalone-logs/:/home/<USER>/logs
      - /docker/nacos/init.d/custom.properties:/home/<USER>/init.d/custom.properties
    ports:
      - 8848:8848
      - 9848:9848
      - 9849:9849
    networks:
      csf_net:
        ipv4_address: ***********

  sentinel:
    image: ${REGISTER}/csf/sentinel-dashboard:1.8.0
    hostname: "sentinel"
    ports:
      - 8858:8858
    restart: on-failure
    networks:
      csf_net:
        ipv4_address: ***********

  csf-nginx:
    image: ${REGISTER}/library/nginx:1.22.0
    hostname: "csf-nginx"
    ports:
    - 88:88
    volumes:
    - /docker/nginx/api/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
    - csf_net

  web-nginx:
    image: ${REGISTER}/library/nginx:1.22.0
    hostname: "web-nginx"
    ports:
      - 8000:8000
    volumes:
      - /docker/nginx/web/html:/usr/share/nginx/html
      - /docker/nginx/web/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-redis:
    image: ${REGISTER}/csf/redis:5.0.3
    hostname: "csf-redis"
    ports:
    - 30379:6379
    volumes:
    - /docker/redis/data:/data
    command: "redis-server --appendonly yes"
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-admin:
    image: "${REGISTER}/csf/csf-admin:${TAG}"
    ports:
    - 7002:7002
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-swagger:
    image: "${REGISTER}/csf/csf-swagger:${TAG}"
    ports:
      - 18000:18000
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-gateway:
    image: "${REGISTER}/csf/csf-gateway:${TAG}"
    ports:
      - 80:80
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-auth:
    image: "${REGISTER}/csf/csf-auth:${TAG}"
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-report:
    image: "${REGISTER}/csf/csf-report:${TAG}"
    ports:
      - 8108:8108
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-log:
    image: "${REGISTER}/csf/csf-log:${TAG}"
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-desk:
    image: "${REGISTER}/csf/csf-desk:${TAG}"
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-user:
    image: "${REGISTER}/csf/csf-user:${TAG}"
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-system:
    image: "${REGISTER}/csf/csf-system:${TAG}"
    privileged: true
    restart: always
    networks:
    - csf_net

  csf-resource:
    image: "${REGISTER}/csf/csf-resource:${TAG}"
    privileged: true
    restart: always
    networks:
      - csf_net

  csf-develop:
    image: "${REGISTER}/csf/csf-develop:${TAG}"
    privileged: true
    restart: always
    networks:
      - csf_net

networks:
  csf_net:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
