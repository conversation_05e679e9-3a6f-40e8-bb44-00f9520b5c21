
package com.chinaunicom.csf.system.user.service.impl;

import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import lombok.AllArgsConstructor;
import com.chinaunicom.csf.system.user.entity.UserOauth;
import com.chinaunicom.csf.system.user.mapper.UserOauthMapper;
import com.chinaunicom.csf.system.user.service.IUserOauthService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 */
@Service
@AllArgsConstructor
public class UserOauthServiceImpl extends BaseServiceImpl<UserOauthMapper, UserOauth> implements IUserOauthService {

}
