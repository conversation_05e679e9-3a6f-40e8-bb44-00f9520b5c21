package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 知识库检索测试历史查询DTO
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@Schema(description = "知识库检索测试历史查询DTO")
public class KnowledgeTestHistoryQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 知识库ID（必填，查询指定知识库的测试历史）
     */
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID，必填参数", example = "1", required = true)
    private Long knowledgeBaseId;

    /**
     * 问题文本关键词（可选，用于模糊搜索）
     */
    @Schema(description = "问题文本关键词，支持模糊搜索", example = "向量数据库")
    private String questionKeyword;
}
