#服务器端口
server:
  port: 10001

#数据源配置
spring:
  datasource:
    url: ${csf.datasource.dev.url}
    username: ${csf.datasource.dev.username}
    password: ${csf.datasource.dev.password}

#spring:
#  #排除DruidDataSourceAutoConfigure
#  autoconfigure:
#    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
#  datasource:
#    dynamic:
#      #设置默认的数据源或者数据源组,默认值即为master
#      primary: master
#      datasource:
#        master:
#          url: ${csf.datasource.demo.master.url}
#          username: ${csf.datasource.demo.master.username}
#          password: ${csf.datasource.demo.master.password}
#        slave:
#          url: ${csf.datasource.demo.slave.url}
#          username: ${csf.datasource.demo.slave.username}
#          password: ${csf.datasource.demo.slave.password}

# 向量化并发控制配置
vectorization:
  concurrency:
    # Python向量化服务的并发限制（基于实际服务能力）
    python-service-limit: 20
    # 安全阈值，实际使用时的并发限制（留余量避免过载）
    safe-threshold: 18
    # 分批处理的批次大小
    batch-size: 20
    # 批次之间的等待时间（秒）
    batch-wait-seconds: 15
    # 向量化超时时间（分钟）
    timeout-minutes: 30
    # 超时检查间隔（毫秒，5分钟）
    timeout-check-interval-ms: 300000
    # 最大文档数量提示阈值（仅用于日志警告，不强制限制）
    max-documents-limit: 1000
    # 超时文档自动清理阈值
    timeout-cleanup-threshold: 10
    # 是否启用并发控制
    enabled: true
    # 是否启用自动超时清理
    auto-timeout-cleanup: true
evaluation:
    # 开发环境启用定时状态同步任务
    # 同步任务执行间隔（毫秒），开发环境可以更频繁
    interval: 600000  # 10分钟
    # 批量处理大小
    batch-size: 100
    # 开发环境启动时执行一次同步，便于调试
    sync-on-startup: true
    # 评测前自动同步状态（开发环境推荐启用）
    auto-sync-before-evaluation: true

# 混合检索调试日志配置
logging:
  level:
    com.chinaunicom.ai.knowledge.service.impl.ElasticSearchServiceImpl: DEBUG
    com.chinaunicom.ai.knowledge.service.impl.KnowledgeBaseServiceImpl: DEBUG
    com.chinaunicom.ai.knowledge.service.impl.KnowledgeTestServiceImpl: DEBUG
    com.chinaunicom.ai.knowledge.config.HybridSearchConfig: DEBUG
