
package com.chinaunicom.csf.desk.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinaunicom.csf.core.mp.base.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("csf_notice")
public class Notice extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@Schema(description = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 标题
	 */
	@Schema(description = "标题")
	private String title;

	/**
	 * 通知类型
	 */
	@Schema(description = "通知类型")
	private Integer category;

	/**
	 * 发布日期
	 */
	@Schema(description = "发布日期")
	private Date releaseTime;

	/**
	 * 内容
	 */
//	@FieldEncrypt
	@Schema(description = "内容")
	private String content;


}
