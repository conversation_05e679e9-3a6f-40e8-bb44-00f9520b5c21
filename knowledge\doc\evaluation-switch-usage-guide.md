# 评测功能开关使用指南

## 概述

本文档说明如何使用评测功能开关机制来控制EvaluationController中所有接口的访问权限。

## 功能特性

### ✅ 已实现的功能

1. **统一开关控制**：一个配置项控制所有评测接口
2. **配置热更新**：支持Nacos配置实时生效，无需重启服务
3. **AOP切面拦截**：使用Spring AOP统一拦截所有评测接口
4. **统一错误响应**：功能关闭时返回一致的错误格式
5. **详细日志记录**：记录配置变更和访问拒绝日志
6. **参数验证**：自动验证配置参数的有效性

### 🎯 涉及的接口

功能开关控制以下所有评测接口：

- `POST /evaluation/import-dataset` - 导入评测数据集
- `POST /evaluation/run-evaluation` - 执行评测测试
- `GET /evaluation/statistics` - 获取评测统计
- `GET /evaluation/dataset-info` - 获取数据集信息
- `DELETE /evaluation/cleanup` - 清理评测数据
- `DELETE /evaluation/cleanup-history` - 清理评测历史
- `GET /evaluation/cleanup-preview` - 清理预览

## 配置说明

### Nacos配置项

在Nacos配置中心的`knowledge-service.yml`中添加以下配置：

```yaml
evaluation:
  controller:
    # 评测功能开关状态（true=开启，false=关闭）
    enabled: true
    # 功能关闭时的HTTP状态码
    disabled-http-status: 503
    # 功能关闭时的错误码
    disabled-error-code: "EVALUATION_DISABLED"
    # 功能关闭时的错误信息
    disabled-message: "评测功能暂时关闭，请稍后再试"
    # 是否记录功能关闭时的访问日志
    log-disabled-access: true
    # 功能关闭时的详细说明信息
    disabled-detail: "系统维护中，评测相关功能暂时不可用"
```

### 配置参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 功能开关状态 |
| `disabled-http-status` | int | 503 | 关闭时的HTTP状态码 |
| `disabled-error-code` | string | "EVALUATION_DISABLED" | 错误码 |
| `disabled-message` | string | "评测功能暂时关闭，请稍后再试" | 错误信息 |
| `log-disabled-access` | boolean | true | 是否记录访问日志 |
| `disabled-detail` | string | "系统维护中，评测相关功能暂时不可用" | 详细说明 |

## 使用方法

### 1. 关闭评测功能

在Nacos中修改配置：
```yaml
evaluation:
  controller:
    enabled: false
    disabled-message: "评测功能维护中，请稍后再试"
    disabled-detail: "预计维护时间：2小时"
```

### 2. 开启评测功能

在Nacos中修改配置：
```yaml
evaluation:
  controller:
    enabled: true
```

### 3. 自定义错误响应

```yaml
evaluation:
  controller:
    enabled: false
    disabled-http-status: 423
    disabled-error-code: "EVALUATION_LOCKED"
    disabled-message: "评测功能已锁定"
    disabled-detail: "请联系管理员解锁"
```

## 错误响应格式

当功能开关关闭时，所有评测接口返回统一格式：

```json
{
  "code": 503,
  "success": false,
  "data": {
    "errorCode": "EVALUATION_DISABLED",
    "message": "评测功能暂时关闭，请稍后再试。系统维护中，评测相关功能暂时不可用",
    "httpStatus": 503,
    "timestamp": 1704614400000,
    "service": "evaluation-service",
    "suggestion": {
      "action": "请稍后重试或联系系统管理员",
      "contact": "如需紧急使用评测功能，请联系技术支持"
    }
  },
  "msg": "评测功能暂时关闭，请稍后再试"
}
```

## 日志监控

### 配置变更日志

```
INFO  - 评测功能开关配置 - 配置刷新: enabled=false, httpStatus=503, errorCode=EVALUATION_DISABLED, message=评测功能暂时关闭，请稍后再试
WARN  - ⚠️ 评测功能已被关闭！所有评测接口将拒绝访问
```

### 访问拒绝日志

```
WARN  - 🚫 评测功能已关闭，拒绝访问 - 接口: EvaluationController.importDataset, 请求: POST /evaluation/import-dataset, 客户端: 192.168.1.100, UA: PostmanRuntime/7.32.3
```

## 常见场景

### 1. 系统维护

```yaml
evaluation:
  controller:
    enabled: false
    disabled-message: "系统正在维护中"
    disabled-detail: "预计维护时间：2小时，请稍后再试"
```

### 2. 紧急关闭

```yaml
evaluation:
  controller:
    enabled: false
    disabled-http-status: 503
    disabled-message: "评测服务暂时不可用"
    disabled-detail: "服务异常，正在紧急修复中"
```

### 3. 功能测试

```yaml
evaluation:
  controller:
    enabled: false
    disabled-message: "评测功能正在测试中"
    disabled-detail: "功能升级测试，预计1小时后恢复"
    log-disabled-access: true
```

## 注意事项

1. **配置生效时间**：Nacos配置修改后，通常在几秒内生效，无需重启服务
2. **日志监控**：建议监控配置变更和访问拒绝日志
3. **权限控制**：确保只有授权人员可以修改Nacos配置
4. **业务影响**：关闭功能前评估对业务的影响范围
5. **回滚准备**：重要操作前备份原配置，便于快速回滚

## 技术实现

- **配置类**：`EvaluationSwitchConfig` - 使用`@RefreshScope`支持热更新
- **切面类**：`EvaluationSwitchAspect` - 使用AOP拦截所有评测接口
- **依赖**：`spring-boot-starter-aop` - 提供AOP支持
- **注解**：`@ConfigurationProperties` - 绑定配置属性

## 故障排查

### 配置不生效

1. 检查Nacos配置是否正确发布
2. 确认应用是否连接到正确的Nacos实例
3. 查看应用日志中的配置刷新记录

### 接口仍然可访问

1. 确认配置项名称是否正确
2. 检查AOP切面是否正常工作
3. 验证Controller包路径是否匹配切点表达式

### 错误响应格式异常

1. 检查R类的fail方法调用是否正确
2. 确认错误响应创建逻辑是否有异常
