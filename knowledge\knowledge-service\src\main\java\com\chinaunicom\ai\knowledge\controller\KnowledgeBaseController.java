package com.chinaunicom.ai.knowledge.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.ai.knowledge.dto.*;
import com.chinaunicom.ai.knowledge.service.AiModelService;
import com.chinaunicom.ai.knowledge.service.KnowledgeBaseService;
import com.chinaunicom.ai.knowledge.service.KnowledgeDocumentService;
import com.chinaunicom.ai.knowledge.service.KnowledgeTestService;
import com.chinaunicom.ai.knowledge.service.impl.FileUploadService;
import com.chinaunicom.ai.knowledge.vo.*;
import com.chinaunicom.csf.core.tool.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 知识库管理控制器
 */
@Slf4j
@RestController
@AllArgsConstructor // 使用Lombok自动生成所有参数的构造函数，简化注入
@RequestMapping("/knowledge-base")
@Tag(name = "知识库管理", description = "知识库模块相关API") // Swagger标签
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;
    private final AiModelService aiModelService;
    private final KnowledgeDocumentService knowledgeDocumentService;
    private final FileUploadService fileUploadService;
    private final KnowledgeTestService knowledgeTestService;

    /**
     * 知识库创建
     * @param createDTO 知识库创建DTO
     * @return 是否创建成功
     */
    @PostMapping("/create")
    @Operation(summary = "创建知识库", description = "根据知识库名称、描述和关联模型ID创建新的知识库")
    public R<Boolean> createKnowledgeBase(@Validated @RequestBody KnowledgeBaseCreateDTO createDTO) {
        log.info("接收到知识库创建请求: {}", createDTO);
        return knowledgeBaseService.createKnowledgeBase(createDTO);
    }

    /**
     * 模型下拉框数据展示功能
     * @param modelType 模型类型 (1.大语言模型 2.向量嵌入模型)
     * @return 模型名称列表
     */
    @GetMapping("/models/dropdown")
    @Operation(summary = "获取AI模型下拉框数据", description = "根据模型类型查询AI模型列表，用于下拉框展示")
    public R<List<AiModelDropdownVO>> getModelsForDropdown(
            @Parameter(description = "模型类型 (1:大语言模型, 2:向量嵌入模型)", required = true)
            @RequestParam Integer modelType) {
        log.info("接收到获取模型下拉框请求，模型类型: {}", modelType);
        return R.data(aiModelService.getModelsForDropdown(modelType));
    }

    /**
     * 知识库编辑
     * 只允许修改知识库的名称和描述，不允许修改关联的向量模型。
     * @param updateDTO 知识库更新DTO
     * @return 是否编辑成功
     */
    @PutMapping("/update")
    @Operation(summary = "编辑知识库", description = "根据知识库ID更新知识库的名称和描述，不允许修改关联的向量模型")
    public R<Boolean> updateKnowledgeBase(@Validated @RequestBody KnowledgeBaseUpdateDTO updateDTO) {
        log.info("接收到知识库编辑请求: {}", updateDTO);
        return knowledgeBaseService.updateKnowledgeBase(updateDTO);
    }

    /**
     * 查询知识库详情
     * @param id 知识库ID
     * @return 知识库详情VO
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询知识库详情", description = "根据知识库ID查询知识库的详细信息，包括关联模型名称")
    public R<KnowledgeBaseDetailVO> getKnowledgeBaseDetail(
            @Parameter(description = "知识库ID", required = true)
            @PathVariable Long id) {
        log.info("接收到查询知识库详情请求，知识库ID: {}", id);
        return knowledgeBaseService.getKnowledgeBaseDetail(id);
    }

    /**
     * 查询知识库列表
     * @param queryDTO 查询DTO
     * @return 知识库列表分页数据
     */
    @GetMapping("/list")
    @Operation(summary = "查询知识库列表", description = "支持根据知识库名称模糊查询，并支持分页")
    public R<IPage<KnowledgeBaseListVO>> getKnowledgeBaseList(@Validated KnowledgeBaseQueryDTO queryDTO) {
        log.info("接收到查询知识库列表请求: {}", queryDTO);
        return knowledgeBaseService.getKnowledgeBaseList(queryDTO);
    }

    /**
     * 根据知识库ID查询知识库下的文档列表，支持文件名模糊查询和分页
     * @param queryDTO 包含知识库ID、文件名关键词、分页信息的查询DTO
     * @return 文档列表分页数据VO
     */
    @GetMapping("/documents/list") // 修改了路径，使其更RESTful并避免路径变量冲突
    @Operation(summary = "查询知识库下的文档列表", description = "根据知识库ID、文件名关键词查询文档列表，并支持分页")
    public R<IPage<KnowledgeDocumentVO>> getKnowledgeDocumentsByBaseId(@Validated KnowledgeDocumentQueryDTO queryDTO) {
        log.info("接收到查询知识库下文档列表请求: {}", queryDTO);
        return R.data(knowledgeDocumentService.getKnowledgeDocumentsByBaseId(queryDTO));
    }

    /**
     * 文档上传 - 初始化分片上传
     * @param initDTO 初始化请求DTO
     * @return 分片上传ID和文档ID
     */
    @PostMapping("/document/upload/init")
    @Operation(summary = "文档上传 - 初始化分片上传", description = "初始化MinIO分片上传，并创建文档记录")
    public R<FileUploadInitResultVO> initDocumentUpload(@Validated @RequestBody KnowledgeDocumentUploadInitDTO initDTO) {
        log.info("接收到文档上传初始化请求: {}", initDTO);
        return R.data(fileUploadService.initMultipartUpload(initDTO.getKnowledgeBaseId(), initDTO.getFileName(), initDTO.getFileType(), initDTO.getFileSize()));
    }

    /**
     * 文档上传 - 上传单个分片
     * @param uploadId 分片上传ID
     * @param partNumber 分片序号
     * @param filePart 文件分片
     * @return 分片ETag
     * @throws IOException IO异常
     */
    @PostMapping("/document/upload/part")
    @Operation(summary = "文档上传 - 上传分片", description = "上传文档的单个分片")
    public R<String> uploadDocumentPart(
            @Parameter(description = "MinIO分片上传ID", required = true) @RequestParam String uploadId,
            @Parameter(description = "分片序号", required = true) @RequestParam int partNumber,
            @Parameter(description = "文件分片", required = true) @RequestPart("file") MultipartFile filePart) throws IOException {
        log.info("接收到文档上传分片请求，uploadId: {}, partNumber: {}", uploadId, partNumber);
        return R.data(fileUploadService.uploadPart(uploadId, partNumber, filePart));
    }

    /**
     * 文档上传 - 完成分片上传
     * @param completeDTO 完成上传请求DTO
     * @return 是否成功
     */
    @PostMapping("/document/upload/complete")
    @Operation(summary = "文档上传 - 完成分片上传", description = "完成MinIO分片上传，更新文档状态并触发向量化")
    public R<Boolean> completeDocumentUpload(@Validated @RequestBody KnowledgeDocumentUploadCompleteDTO completeDTO) {
        log.info("接收到文档上传完成请求: {}", completeDTO);
        fileUploadService.completeMultipartUpload(completeDTO.getUploadId(), completeDTO.getDocumentId());
        return R.data(true); // 异步处理，这里只表示请求已接受
    }

    /**
     * 知识库删除
     * @param id 知识库ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除知识库", description = "逻辑删除知识库及其下文档，物理删除MinIO文件和ElasticSearch索引")
    public R<Boolean> deleteKnowledgeBase(
            @Parameter(description = "知识库ID", required = true)
            @PathVariable Long id) {
        log.info("接收到删除知识库请求，知识库ID: {}", id);
        return knowledgeBaseService.deleteKnowledgeBase(id);
    }

    /**
     * 智能体接口：知识库文档召回
     * 根据知识库ID列表和问题文本，向量化后召回相关文档片段。
     * @param request 召回请求体，包含知识库ID列表和问题文本
     * @return 召回的文档片段列表
     */
    @Operation(
            summary = "知识库文档召回",
            description = "根据知识库ID列表和问题文本，向量化后召回相关文档片段"
    )
    @PostMapping("/recall-documents") // 使用 /recall-documents 路径
    public List<RecallDocSegment> recallDocuments(@RequestBody RecallDocumentsRequest request) {
        log.info("接收到知识库文档召回请求，知识库ID列表: {}, 问题文本: {}",
                request.getKbIds(), request.getQueryText());
        // 调用Service层处理召回逻辑，使用默认topK=5保持向后兼容
        return knowledgeBaseService.recallDocuments(request.getKbIds(), request.getQueryText(), 5);
    }

    /**
     * 根据文档ID重命名文档
     * 文档的后缀名不允许修改，前端只会传前缀名进来，如果前端传了后缀名，则忽略这个后缀名，最终的后缀名用旧文档名称中的后缀名。
     * @param renameDTO 包含文档ID和新的文件名前缀的DTO
     * @return 是否重命名成功
     */
    @PutMapping("/document/rename")
    @Operation(summary = "重命名知识库文档", description = "根据文档ID重命名文档，保留原文档后缀名")
    public R<Boolean> renameDocument(@Validated @RequestBody DocumentRenameDTO renameDTO) {
        log.info("接收到文档重命名请求: {}", renameDTO);
        boolean success = knowledgeDocumentService.renameDocument(renameDTO);
        return R.data(success);
    }

    /**
     * 根据文档ID列表删除知识库文档
     * 支持单个或批量删除。执行逻辑删除数据库记录，并物理删除MinIO文件。
     * @param deleteRequest 包含要删除的文档ID列表的请求DTO
     * @return 成功删除的数量
     */
    @PostMapping("/documents/deleteBatch") // POST请求，使用RequestBody接收ID列表
    @Operation(summary = "删除知识库文档", description = "根据文档ID列表删除知识库文档，支持单个或批量删除")
    public R<Integer> deleteDocuments(@Validated @RequestBody DocumentDeleteRequest deleteRequest) {
        log.info("接收到文档删除请求，文档ID列表: {}", deleteRequest.getDocumentIds());
        int deletedCount = knowledgeDocumentService.deleteDocuments(deleteRequest);
        return R.data(deletedCount);
    }

    /**
     * 前端检索测试接口：知识库文档召回测试
     * 专门为前端检索测试功能设计，返回包含测试历史记录的完整数据结构。
     * 与 /recall-documents 接口不同，此接口会保存测试历史到数据库，并返回包含执行时间、分数等详细信息的结果。
     * @param testRecallDTO 检索测试请求DTO，包含知识库ID和问题文本
     * @return 测试历史项VO，包含测试结果和执行信息
     */
    @PostMapping("/test-recall")
    @Operation(
            summary = "知识库检索测试",
            description = "专门为前端检索测试功能设计，执行知识库文档召回测试并保存测试历史，返回包含执行时间、匹配分数等详细信息的结果"
    )
    public R<DatasetTestHistoryItemVO> executeRecallTest(@Validated @RequestBody KnowledgeTestRecallDTO testRecallDTO) {
        log.info("接收到知识库检索测试请求，知识库ID: {}, 问题文本: {}",
                testRecallDTO.getKnowledgeBaseId(), testRecallDTO.getQueryText());
        DatasetTestHistoryItemVO result = null;
        try {
            result = knowledgeTestService.executeRecallTest(testRecallDTO);
        } catch (Exception e) {
            log.error("知识库检索测试报错", e);
            return R.fail("执行知识库检索测试失败");
        }
        return R.data(result);
    }

    /**
     * 获取检索测试历史详情
     * 根据测试历史ID获取完整的测试详情，包含所有测试结果。
     * 知识库名称等信息通过关联查询动态获取，避免数据冗余。
     * @param testHistoryId 测试历史记录ID
     * @return 测试历史项VO，包含完整的测试结果信息
     */
    @GetMapping("/test-recall/{testHistoryId}")
    @Operation(
            summary = "获取检索测试历史详情",
            description = "根据测试历史ID获取完整的测试详情，包含所有测试结果和关联信息"
    )
    public R<DatasetTestHistoryItemVO> getTestHistoryDetail(@PathVariable String testHistoryId) {
        log.info("接收到获取测试历史详情请求，测试ID: {}", testHistoryId);
        DatasetTestHistoryItemVO result = knowledgeTestService.getTestHistoryDetail(testHistoryId);
        return R.data(result);
    }

    /**
     * 分页查询检索测试历史列表
     * 根据指定知识库ID查询测试历史，支持问题关键词筛选。
     * 返回测试ID、问题文本、执行时间(秒)、创建时间四个字段，按创建时间倒序排序。
     * @param queryDTO 查询条件DTO，必须包含知识库ID，可选问题关键词和分页参数
     * @return 分页的测试历史列表，包含id、question、second、createTime字段
     */
    @GetMapping("/test-recall")
    @Operation(
            summary = "分页查询检索测试历史列表",
            description = "根据指定知识库ID查询测试历史，支持问题关键词筛选，返回分页的测试历史列表，包含id、question、second、createTime四个字段，按创建时间倒序排序"
    )
    public R<IPage<KnowledgeTestHistoryListVO>> getTestHistoryList(@Validated KnowledgeTestHistoryQueryDTO queryDTO) {
        log.info("接收到查询测试历史列表请求，页码: {}, 页大小: {}, 知识库ID: {}, 关键词: {}",
                queryDTO.getPageNum(), queryDTO.getPageSize(), queryDTO.getKnowledgeBaseId(), queryDTO.getQuestionKeyword());
        IPage<KnowledgeTestHistoryListVO> result = knowledgeTestService.getTestHistoryList(queryDTO);
        return R.data(result);
    }

    /**
     * 删除检索测试历史记录
     * 逻辑删除指定的测试历史记录及其相关的测试结果。
     * 只能删除当前租户下的测试记录，确保数据安全。
     * @param testHistoryId 测试历史记录ID
     * @return 删除结果
     */
    @DeleteMapping("/test-recall/{testHistoryId}")
    @Operation(
            summary = "删除检索测试历史记录",
            description = "逻辑删除指定的测试历史记录及其相关的测试结果，只能删除当前租户的记录"
    )
    public R<Boolean> deleteTestHistory(@PathVariable String testHistoryId) {
        log.info("接收到删除测试历史记录请求，测试ID: {}", testHistoryId);
        Boolean result = knowledgeTestService.deleteTestHistory(testHistoryId);
        return R.data(result);
    }
}
