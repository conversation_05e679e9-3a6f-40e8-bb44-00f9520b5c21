-- 知识库检索测试历史表
CREATE TABLE knowledge_test_history (
 id VARCHAR(32) NOT NULL COMMENT '测试记录ID，32位不含横线的UUID',
 question TEXT NOT NULL COMMENT '检索测试的问题文本',
 knowledge_base_id BIGINT NOT NULL COMMENT '知识库ID',
 execution_time_ms BIGINT NOT NULL COMMENT '查询执行时间（毫秒）',
 result_count INT NOT NULL DEFAULT 0 COMMENT '返回结果数量',
 create_user BIGINT COMMENT '创建用户ID',
 create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 update_user BIGINT COMMENT '更新用户ID',
 update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 status INT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
 is_deleted INT NOT NULL DEFAULT 0 COMMENT '是否已删除：0-未删除，1-已删除',
 tenant_id VARCHAR(12) NOT NULL DEFAULT '000000' COMMENT '租户ID',
 PRIMARY KEY (id),
 INDEX idx_knowledge_base_id (knowledge_base_id),
 INDEX idx_create_time (create_time),
 INDEX idx_tenant_id (tenant_id),
 INDEX idx_create_user (create_user)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库检索测试历史表';

-- 知识库检索测试结果表
CREATE TABLE knowledge_test_result (
   id VARCHAR(32) NOT NULL COMMENT '测试结果ID，32位不含横线的UUID',
   test_history_id VARCHAR(32) NOT NULL COMMENT '测试历史记录ID',
   content TEXT NOT NULL COMMENT '匹配到的文档分片内容',
   score DECIMAL(10,6) NOT NULL COMMENT '匹配分数',
   document_id BIGINT NOT NULL COMMENT '文档ID',
   document_name VARCHAR(500) NOT NULL COMMENT '文档名称',
   knowledge_base_id BIGINT NOT NULL COMMENT '知识库ID',
   create_user BIGINT COMMENT '创建用户ID',
   create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   update_user BIGINT COMMENT '更新用户ID',
   update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   status INT NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
   is_deleted INT NOT NULL DEFAULT 0 COMMENT '是否已删除：0-未删除，1-已删除',
   tenant_id VARCHAR(12) NOT NULL DEFAULT '000000' COMMENT '租户ID',
   PRIMARY KEY (id),
   INDEX idx_test_history_id (test_history_id),
   INDEX idx_document_id (document_id),
   INDEX idx_knowledge_base_id (knowledge_base_id),
   INDEX idx_tenant_id (tenant_id),
   FOREIGN KEY (test_history_id) REFERENCES knowledge_test_history(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库检索测试结果表';