package com.chinaunicom.csf.system.handler;

import com.chinaunicom.csf.plugins.datasecurity.config.DataSecurityAutoConfiguration;
import com.chinaunicom.csf.plugins.datasecurity.entity.AuditLog;
import com.chinaunicom.csf.plugins.datasecurity.handler.DataAuditHandler;
import com.chinaunicom.csf.system.feign.DataAuditClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.stereotype.Component;


@Component
@AutoConfigureBefore(DataSecurityAutoConfiguration.class)
public class DataAuditCloudHandler implements DataAuditHandler {

	private DataAuditClient dataAuditClient;

	@Autowired
	public DataAuditCloudHandler(DataAuditClient dataAuditClient) {
		this.dataAuditClient = dataAuditClient;
	}

	@Override
	public int save(AuditLog auditLog) {
		return dataAuditClient.log(auditLog);
	}
}
