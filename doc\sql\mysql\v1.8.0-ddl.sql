ALTER TABLE `csf_code`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `is_deleted`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`;

ALTER TABLE `csf_dept`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `is_deleted`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`;

ALTER TABLE `csf_dict`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `is_deleted`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`;

ALTER TABLE `csf_menu`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `is_deleted`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`;

ALTER TABLE `csf_region`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `remark`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`,
ADD COLUMN `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除' AFTER `status`;

ALTER TABLE `csf_role`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `is_deleted`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`;

ALTER TABLE `csf_role_menu`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `role_id`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`,
ADD COLUMN `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除' AFTER `status`;

ALTER TABLE `csf_user_oauth`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `source`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`,
ADD COLUMN `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除' AFTER `status`;

ALTER TABLE `csf_user`
    ADD COLUMN `password_last_modified_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上次密码修改时间' AFTER `update_time`;

ALTER TABLE `csf_user`
    ADD COLUMN `salt` varchar(20) DEFAULT '' COMMENT '密码加盐' AFTER `password`;

ALTER TABLE `csf_role_scope`
    ADD COLUMN `create_user` bigint(20) DEFAULT NULL COMMENT '创建用户ID' AFTER `role_id`,
ADD COLUMN `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `create_user`,
ADD COLUMN `update_user` bigint(20) DEFAULT NULL COMMENT '更新用户ID' AFTER `create_time`,
ADD COLUMN `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' AFTER `update_user`,
ADD COLUMN `status` int(2) DEFAULT NULL COMMENT '状态' AFTER `update_time`,
ADD COLUMN `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除' AFTER `status`;
