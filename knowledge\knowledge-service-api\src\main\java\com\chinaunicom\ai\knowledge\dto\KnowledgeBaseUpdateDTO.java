package com.chinaunicom.ai.knowledge.dto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;
/**
 * 知识库更新请求DTO
 */
@Data
@Schema(description = "知识库更新请求DTO")
public class KnowledgeBaseUpdateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @NotBlank(message = "知识库名称不能为空")
    @Schema(description = "知识库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "更新后的知识库名称")
    private String name;

    @Schema(description = "知识库描述", example = "这是更新后的知识库描述。")
    private String descrip; // 将 'desc' 更改为 'descrip'
}