package com.chinaunicom.ai.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 知识库检索测试历史列表VO
 *
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025/6/12
 */
@Data
@Schema(description = "知识库检索测试历史列表VO")
public class KnowledgeTestHistoryListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测试记录ID
     */
    @Schema(description = "测试记录ID", example = "abc123def456ghi789jkl012mno345pq")
    private String id;

    /**
     * 检索测试的问题文本
     */
    @Schema(description = "检索测试的问题文本", example = "什么是向量数据库？")
    private String question;

    /**
     * 查询执行时间（秒）
     */
    @Schema(description = "查询执行时间（秒）", example = "1.25")
    private Double second;

    /**
     * 创建时间（ISO格式字符串）
     */
    @Schema(description = "创建时间", example = "2025-06-12T10:30:00")
    private String createTime;
}
