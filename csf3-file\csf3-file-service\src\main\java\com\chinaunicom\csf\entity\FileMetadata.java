package com.chinaunicom.csf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 文件元数据实体类
 */
@Data
@TableName("file_metadata")
@EqualsAndHashCode(callSuper = true)
public class FileMetadata extends Model<FileMetadata> {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID (32位不含横线的UUID)
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 文件名（系统处理后的名称）
     */
    @TableField("file_name")
    private String fileName;

    /**
     * MinIO中的对象名称
     */
    @TableField("object_name")
    private String objectName;

    /**
     * MinIO桶名称
     */
    @TableField("bucket_name")
    private String bucketName;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    @TableField("content_type")
    private String mimeType;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    /**
     * 文件业务类型（如document, avatar）
     */
    @TableField("file_type")
    private String bizType;

    /**
     * 原始文件名
     */
    @TableField("original_file_name")
    private String originalFileName;

    /**
     * 文件访问路径或URL
     */
    @TableField("file_path")
    private String accessPath;

    /**
     * URL过期时间（仅对预签名URL有效）
     */
    @TableField("url_expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime urlExpireTime;

    /**
     * 分片上传ID（如果是非分片上传则为空）
     */
    @TableField("upload_id")
    private String uploadId;

    /**
     * 分片数量（如果是非分片上传则为空）
     */
    @TableField("part_count")
    private Integer partCount;
}