package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseCreateDTO;
import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.evaluation.config.VectorizationConcurrencyConfig;
import com.chinaunicom.ai.knowledge.evaluation.dto.EvaluationResult;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationRequest;
import com.chinaunicom.ai.knowledge.evaluation.dto.FullEvaluationResult;
import com.chinaunicom.ai.knowledge.evaluation.dto.ImportResult;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.KnowledgeBaseService;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import com.chinaunicom.csf.core.tool.api.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 端到端评测事务服务
 * 
 * 专门处理需要事务保护的操作，解决Spring AOP代理问题
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-08
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class FullEvaluationTransactionService {

    private final KnowledgeBaseService knowledgeBaseService;
    private final EvaluationDataImporter dataImporter;
    private final EvaluationTestService evaluationTestService;
    private final EvaluationCleanupService cleanupService;
    private final IElasticSearchService elasticSearchService;
    private final AiModelMapper aiModelMapper;
    private final VectorizationTimeoutMonitor timeoutMonitor;
    private final VectorizationConcurrencyConfig concurrencyConfig;

    /**
     * 构造函数 - 手动处理可选依赖
     */
    public FullEvaluationTransactionService(
            KnowledgeBaseService knowledgeBaseService,
            EvaluationDataImporter dataImporter,
            EvaluationTestService evaluationTestService,
            EvaluationCleanupService cleanupService,
            IElasticSearchService elasticSearchService,
            AiModelMapper aiModelMapper,
            VectorizationConcurrencyConfig concurrencyConfig,
            @Autowired(required = false) VectorizationTimeoutMonitor timeoutMonitor) {

        this.knowledgeBaseService = knowledgeBaseService;
        this.dataImporter = dataImporter;
        this.evaluationTestService = evaluationTestService;
        this.cleanupService = cleanupService;
        this.elasticSearchService = elasticSearchService;
        this.aiModelMapper = aiModelMapper;
        this.concurrencyConfig = concurrencyConfig;
        this.timeoutMonitor = timeoutMonitor;

        // 记录VectorizationTimeoutMonitor的可用状态
        if (timeoutMonitor != null) {
            log.info("VectorizationTimeoutMonitor已启用，支持向量化超时监控功能");
        } else {
            log.info("VectorizationTimeoutMonitor未启用，向量化超时监控功能不可用");
        }
    }

    /**
     * 创建临时知识库（独立事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createTemporaryKnowledgeBase(FullEvaluationRequest request, FullEvaluationResult result) {
        log.info("创建临时知识库，名称前缀: {}", request.getKnowledgeBaseName());
        
        // 获取默认向量模型
        Long vectorModelId = request.getVectorModelId();
        if (vectorModelId == null) {
            vectorModelId = getDefaultVectorModel();
        }
        
        // 创建知识库DTO
        KnowledgeBaseCreateDTO createDTO = new KnowledgeBaseCreateDTO();
        createDTO.setName(request.getGeneratedKnowledgeBaseName());
        createDTO.setDescrip("端到端评测临时知识库 - " + LocalDateTime.now());
        createDTO.setVecModel(vectorModelId);
        
        // 调用知识库服务创建
        R<Boolean> createResult = knowledgeBaseService.createKnowledgeBase(createDTO);
        if (!createResult.isSuccess() || !Boolean.TRUE.equals(createResult.getData())) {
            throw new RuntimeException("创建临时知识库失败: " + createResult.getMsg());
        }
        
        // 查询创建的知识库信息
        String tenantId = SecureUtil.getTenantId();
        List<KnowledgeBase> knowledgeBases = knowledgeBaseService.list(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeBase>()
                .eq(KnowledgeBase::getName, createDTO.getName())
                .eq(KnowledgeBase::getTenantId, tenantId)
                .eq(KnowledgeBase::getIsDeleted, 0)
                .orderByDesc(KnowledgeBase::getCreateTime)
                .last("LIMIT 1")
        );
        
        if (knowledgeBases.isEmpty()) {
            throw new RuntimeException("无法找到刚创建的知识库");
        }
        
        KnowledgeBase knowledgeBase = knowledgeBases.get(0);
        AiModel aiModel = aiModelMapper.selectById(vectorModelId);
        
        // 设置知识库信息
        FullEvaluationResult.KnowledgeBaseInfo kbInfo = new FullEvaluationResult.KnowledgeBaseInfo();
        kbInfo.setId(knowledgeBase.getId());
        kbInfo.setName(knowledgeBase.getName());
        kbInfo.setIndexName(knowledgeBase.getIndexName());
        kbInfo.setCreateTime(knowledgeBase.getCreateTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        kbInfo.setVectorModelId(vectorModelId);
        kbInfo.setVectorModelName(aiModel != null ? aiModel.getDisplayName() : "Unknown");
        
        result.setKnowledgeBaseInfo(kbInfo);
        
        log.info("临时知识库创建成功，ID: {}, 名称: {}, 索引: {}", 
                knowledgeBase.getId(), knowledgeBase.getName(), knowledgeBase.getIndexName());
        
        return knowledgeBase.getId();
    }

    /**
     * 导入评测文档（无事务，避免长事务导致数据库锁等待）
     *
     * 移除@Transactional注解的原因：
     * 1. 文档导入涉及向量化服务调用，可能耗时很长
     * 2. 向量化是异步过程，事务不需要等待其完成
     * 3. 每个文档插入使用MyBatis自动提交，保证数据持久化
     * 4. 通过状态字段和超时监控机制保证数据一致性
     */
    public void importEvaluationDocuments(Long knowledgeBaseId, FullEvaluationRequest request, FullEvaluationResult result) {
        log.info("开始导入评测文档到知识库 {}", knowledgeBaseId);

        try {
            // 检查向量化服务状态，防止过载
            checkVectorizationServiceStatus(request.getEffectiveMaxDocuments());

            // 调用数据导入服务（注意：当前实现只支持固定的1doc_QA.json文件）
            ImportResult importResult = dataImporter.importDataset(knowledgeBaseId, request.getEffectiveMaxDocuments());
            
            // 统计导入结果
            FullEvaluationResult.DocumentImportStats importStats = new FullEvaluationResult.DocumentImportStats();
            importStats.setTotalDocuments(importResult.getTotalCount());
            importStats.setSuccessCount(importResult.getSuccessCount());
            importStats.setFailureCount(importResult.getFailedCount() != null ? importResult.getFailedCount() : 0);
            importStats.setImportDurationMs(importResult.getDuration());

            // 设置失败原因
            List<String> failureReasons = new ArrayList<>();
            if (importResult.getFailedDocIds() != null && !importResult.getFailedDocIds().isEmpty()) {
                failureReasons.add("失败的文档ID: " + String.join(", ", importResult.getFailedDocIds()));
            }
            if (importResult.getErrorMessage() != null) {
                failureReasons.add("错误信息: " + importResult.getErrorMessage());
            }
            importStats.setFailureReasons(failureReasons);
            
            result.setDocumentImportStats(importStats);
            
            log.info("评测文档导入完成，总数: {}, 耗时: {}ms", 
                    importStats.getTotalDocuments(), importStats.getImportDurationMs());
            
        } catch (Exception e) {
            log.error("导入评测文档失败", e);
            throw new RuntimeException("导入评测文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行评测（独立事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void runEvaluation(Long knowledgeBaseId, FullEvaluationRequest request, FullEvaluationResult result) {
        log.info("开始执行评测，知识库ID: {}", knowledgeBaseId);

        try {
            EvaluationResult evaluationResult;

            if (request.shouldUseNewRecallFormula()) {
                // 使用新的召回率计算公式
                evaluationResult = runEvaluationWithNewRecallFormula(knowledgeBaseId, request);
            } else {
                // 使用原有的召回率计算公式，传递topK参数
                evaluationResult = evaluationTestService.runEvaluation(knowledgeBaseId, request.getTopK());
            }

            result.setEvaluationResult(evaluationResult);

            // 设置公式说明到FullEvaluationResult中
            if (request.shouldUseNewRecallFormula()) {
                // 新召回率公式：基于整个知识库的片段召回率
                long totalSegments = elasticSearchService.countDocuments(knowledgeBaseService.getById(knowledgeBaseId).getIndexName());
                long totalCorrectSegments = calculateCorrectSegments(evaluationResult);

                // 重新计算基于整个知识库的召回率
                double newRecallRate = totalSegments > 0 ? (double) totalCorrectSegments / totalSegments : 0.0;

                result.setRecallRateFormula("新召回率 = 正确召回的片段数 / 知识库总片段数 = " + totalCorrectSegments + " / " + totalSegments + " = " + String.format("%.4f", newRecallRate));

                log.info("新召回率计算 - 知识库总片段数: {}, 正确召回片段数: {}, 新召回率: {:.4f}, 原召回率: {:.4f}",
                        totalSegments, totalCorrectSegments, newRecallRate, evaluationResult.getRecallRate());
            } else {
                // 传统召回率公式
                result.setRecallRateFormula("传统召回率 = 正确召回的问题数 / 总问题数 = " + evaluationResult.getCorrectRecalls() + " / " + evaluationResult.getTotalQuestions() + " = " + String.format("%.4f", evaluationResult.getRecallRate()));
            }

            // 平均准确率公式
            if (evaluationResult.getAverageAccuracy() != null) {
                result.setAverageAccuracyFormula(buildDetailedAverageAccuracyFormula(evaluationResult));
            }

            log.info("评测执行完成，召回率: {:.2f}%, 平均准确率: {:.2f}%",
                    evaluationResult.getRecallRate() * 100,
                    evaluationResult.getAverageAccuracy() != null ? evaluationResult.getAverageAccuracy() * 100 : 0);

        } catch (Exception e) {
            log.error("执行评测失败", e);
            throw new RuntimeException("执行评测失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理资源（独立事务）
     */
    @Transactional(rollbackFor = Exception.class)
    public void cleanupResources(Long knowledgeBaseId, FullEvaluationResult result) {
        log.info("开始清理临时资源，知识库ID: {}", knowledgeBaseId);

        long cleanupStartTime = System.currentTimeMillis();
        FullEvaluationResult.CleanupStatus cleanupStatus = new FullEvaluationResult.CleanupStatus();
        cleanupStatus.setEnabled(true);

        try {
            // 清理评测数据
            cleanupService.cleanupEvaluationData(knowledgeBaseId, true, true);

            // 删除知识库（包括ES索引和文档）
            R<Boolean> deleteResult = knowledgeBaseService.deleteKnowledgeBase(knowledgeBaseId);
            cleanupStatus.setKnowledgeBaseDeleted(deleteResult.isSuccess());
            cleanupStatus.setEsIndexDeleted(deleteResult.isSuccess());
            cleanupStatus.setDocumentsDeleted(deleteResult.isSuccess());

            if (!deleteResult.isSuccess()) {
                cleanupStatus.setCleanupError("删除知识库失败: " + deleteResult.getMsg());
                log.warn("删除临时知识库失败: {}", deleteResult.getMsg());
            }

        } catch (Exception e) {
            cleanupStatus.setCleanupError("清理资源异常: " + e.getMessage());
            log.error("清理临时资源失败", e);
        } finally {
            cleanupStatus.setCleanupDurationMs(System.currentTimeMillis() - cleanupStartTime);
            result.setCleanupStatus(cleanupStatus);
        }

        log.info("资源清理完成，耗时: {}ms, 状态: {}",
                cleanupStatus.getCleanupDurationMs(),
                cleanupStatus.getCleanupError() != null ? "失败" : "成功");
    }

    /**
     * 获取默认向量模型ID
     */
    private Long getDefaultVectorModel() {
        // 查找第一个可用的向量嵌入模型
        List<AiModel> models = aiModelMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<AiModel>()
                .eq(AiModel::getModelType, "EMBEDDING")
                .eq(AiModel::getTenantId, SecureUtil.getTenantId())
                .orderByDesc(AiModel::getCreateTime)
                .last("LIMIT 1")
        );
        
        if (models.isEmpty()) {
            throw new RuntimeException("未找到可用的向量嵌入模型");
        }
        
        return models.get(0).getId();
    }

    /**
     * 使用新召回率公式执行评测
     * 新公式：所有问题召回的正确片段数量之和 / 知识库总片段数
     */
    private EvaluationResult runEvaluationWithNewRecallFormula(Long knowledgeBaseId, FullEvaluationRequest request) {
        log.info("使用新召回率公式执行评测，知识库ID: {}", knowledgeBaseId);

        // 获取知识库总片段数
        KnowledgeBase knowledgeBase = knowledgeBaseService.getById(knowledgeBaseId);
        if (knowledgeBase == null) {
            throw new RuntimeException("知识库不存在: " + knowledgeBaseId);
        }

        long totalSegments = elasticSearchService.countDocuments(knowledgeBase.getIndexName());
        if (totalSegments == 0) {
            throw new RuntimeException("知识库中没有向量化的文档片段");
        }

        // 执行原有评测获取基础数据，传递topK参数
        EvaluationResult originalResult = evaluationTestService.runEvaluation(knowledgeBaseId, request.getTopK());

        // 计算新的召回率：统计所有问题召回的正确片段数量
        long totalCorrectSegments = 0;

        if (originalResult.getTestResults() != null) {
            for (EvaluationResult.TestResult testResult : originalResult.getTestResults()) {
                if (testResult.getRecalledDocIds() != null && testResult.getExpectedDocId() != null) {
                    // 统计该问题召回的正确片段数量
                    for (String recalledDocId : testResult.getRecalledDocIds()) {
                        if (testResult.getExpectedDocId().equals(recalledDocId)) {
                            totalCorrectSegments++;
                        }
                    }
                }
            }
        }

        // 计算新的召回率
        double newRecallRate = totalSegments > 0 ? (double) totalCorrectSegments / totalSegments : 0.0;

        // 创建新的评测结果
        EvaluationResult newResult = new EvaluationResult();
        newResult.setKnowledgeBaseId(knowledgeBaseId);
        newResult.setTotalQuestions(originalResult.getTotalQuestions());
        newResult.setCorrectRecalls(originalResult.getCorrectRecalls());
        newResult.setRecallRate(newRecallRate); // 使用新的召回率
        newResult.setAverageAccuracy(originalResult.getAverageAccuracy());
        newResult.setAvgExecutionTime(originalResult.getAvgExecutionTime());
        newResult.setTestResults(originalResult.getTestResults());
        newResult.setStartTime(originalResult.getStartTime());
        newResult.setEndTime(originalResult.getEndTime());
        newResult.setTotalDuration(originalResult.getTotalDuration());

        // 设置新召回率公式说明
        // 注意：公式字段在FullEvaluationResult中设置，这里不需要设置EvaluationResult的公式字段

        log.info("新召回率计算完成 - 总片段数: {}, 正确片段数: {}, 新召回率: {:.4f}, 原召回率: {:.4f}",
                totalSegments, totalCorrectSegments, newRecallRate, originalResult.getRecallRate());

        return newResult;
    }

    /**
     * 计算正确召回的片段数量
     */
    private long calculateCorrectSegments(EvaluationResult evaluationResult) {
        long totalCorrectSegments = 0;
        if (evaluationResult.getTestResults() != null) {
            for (EvaluationResult.TestResult testResult : evaluationResult.getTestResults()) {
                if (testResult.getRecalledDocIds() != null && testResult.getExpectedDocId() != null) {
                    // 处理expectedDocId可能带有后缀的情况
                    String realExpectedDocId = extractRealOriginalDocId(testResult.getExpectedDocId());

                    // 统计该问题召回的正确片段数量
                    for (String recalledDocId : testResult.getRecalledDocIds()) {
                        if (realExpectedDocId.equals(recalledDocId)) {
                            totalCorrectSegments++;
                        }
                    }
                }
            }
        }
        return totalCorrectSegments;
    }

    /**
     * 提取真实的原始文档ID
     * 处理重用文档的情况，去掉后缀获取真实的originalDocId
     */
    private String extractRealOriginalDocId(String originalDocId) {
        if (originalDocId == null) {
            return null;
        }

        // 检查是否是重用文档的ID（包含_reuse_kb后缀）
        if (originalDocId.contains("_reuse_kb")) {
            // 去掉后缀，返回真实的originalDocId
            return originalDocId.substring(0, originalDocId.indexOf("_reuse_kb"));
        }

        // 如果不是重用文档，直接返回原始ID
        return originalDocId;
    }

    /**
     * 构建详细的平均准确率计算公式字符串
     *
     * @param evaluationResult 评测结果
     * @return 详细的计算公式字符串
     */
    private String buildDetailedAverageAccuracyFormula(EvaluationResult evaluationResult) {
        if (evaluationResult.getTestResults() == null || evaluationResult.getTestResults().isEmpty()) {
            return "平均准确率 = 所有问题准确率的平均值 = 0 / 0 = 0.0000";
        }

        StringBuilder formula = new StringBuilder();
        formula.append("平均准确率 = 所有问题准确率的平均值 = (");

        // 收集所有有效问题的准确率
        List<String> accuracyValues = new ArrayList<>();
        double totalAccuracy = 0.0;
        int validAccuracyCount = 0;

        for (EvaluationResult.TestResult testResult : evaluationResult.getTestResults()) {
            if (testResult.getAccuracy() != null &&
                testResult.getExpectedDocId() != null &&
                !testResult.getExpectedDocId().trim().isEmpty()) {
                accuracyValues.add(String.format("%.4f", testResult.getAccuracy()));
                totalAccuracy += testResult.getAccuracy();
                validAccuracyCount++;
            }
        }

        // 拼接所有准确率值
        formula.append(String.join(" + ", accuracyValues));
        formula.append(") / ").append(validAccuracyCount);
        formula.append(" = ").append(String.format("%.4f", totalAccuracy));
        formula.append(" / ").append(validAccuracyCount);
        formula.append(" = ").append(String.format("%.4f", evaluationResult.getAverageAccuracy()));

        return formula.toString();
    }

    /**
     * 检查向量化服务状态，防止过载
     *
     * @param requestedDocuments 请求导入的文档数量
     * @throws RuntimeException 如果检测到向量化服务过载
     */
    private void checkVectorizationServiceStatus(Integer requestedDocuments) {
        try {
            // 检查timeoutMonitor是否可用
            if (timeoutMonitor == null) {
                log.warn("VectorizationTimeoutMonitor未启用，跳过向量化服务状态检查");
                return;
            }

            // 获取当前向量化状态统计
            VectorizationTimeoutMonitor.VectorizationStatusStats stats = timeoutMonitor.getVectorizationStatusStats();

            // 从配置中获取Python向量化服务的并发限制
            final int PYTHON_SERVICE_CONCURRENCY_LIMIT = concurrencyConfig.getPythonServiceLimit();
            final int SAFE_THRESHOLD = concurrencyConfig.getSafeThreshold();

            log.info("向量化服务状态检查 - 正在处理: {}, 已完成: {}, 失败: {}, 超时: {}",
                    stats.getVectorizingCount(), stats.getVectorizedCount(),
                    stats.getFailedCount(), stats.getTimeoutCount());

            // 检查1：当前正在向量化的文档数量是否过多
            if (stats.getVectorizingCount() > SAFE_THRESHOLD) {
                String errorMsg = String.format(
                    "向量化服务过载：当前有 %d 个文档正在向量化（安全阈值: %d），请稍后重试。" +
                    "建议：等待当前向量化任务完成，或联系管理员检查向量化服务状态。",
                    stats.getVectorizingCount(), SAFE_THRESHOLD
                );
                log.warn(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 检查2：请求的文档数量提示（仅记录日志，不限制）
            if (requestedDocuments != null && concurrencyConfig.isDocumentCountExceeded(requestedDocuments)) {
                log.warn("请求的文档数量较多：{} 个文档，建议关注向量化服务状态。Python向量化服务并发限制为 {}，" +
                        "大量文档可能导致较长处理时间。",
                        requestedDocuments, PYTHON_SERVICE_CONCURRENCY_LIMIT);
            }

            // 检查3：是否有大量超时文档需要处理
            if (stats.getTimeoutCount() > concurrencyConfig.getTimeoutCleanupThreshold()) {
                log.warn("发现 {} 个向量化超时文档，将自动清理", stats.getTimeoutCount());
                if (timeoutMonitor != null) {
                    timeoutMonitor.checkVectorizationTimeout();
                    log.info("已触发超时文档清理");
                } else {
                    log.warn("VectorizationTimeoutMonitor未启用，无法执行超时文档清理");
                }
            }

            log.info("向量化服务状态检查通过，可以开始导入 {} 个文档", requestedDocuments);

        } catch (RuntimeException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("检查向量化服务状态时发生异常", e);
            // 如果检查失败，为了安全起见，仍然允许继续执行，但记录警告
            log.warn("向量化服务状态检查失败，继续执行导入流程，但请注意监控系统状态");
        }
    }
}
