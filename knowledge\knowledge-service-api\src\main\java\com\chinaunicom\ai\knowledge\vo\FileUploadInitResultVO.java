package com.chinaunicom.ai.knowledge.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 文件上传初始化结果VO
 */
@Data
@Schema(description = "文件上传初始化结果VO")
public class FileUploadInitResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "MinIO分片上传ID")
    private String uploadId;

    @Schema(description = "知识文档ID")
    private Long documentId;
}