${AnsiColor.WHITE}  ,----..    .--.--.       ,---,.
${AnsiColor.WHITE} /   /   \  /  /    '.   ,'  .' |
${AnsiColor.WHITE}|   :     :|  :  /`. / ,---.'   |
${AnsiColor.WHITE}.   |  ;. /;  |  |--`  |   |   .'
${AnsiColor.WHITE}.   ; /--` |  :  ;_    :   :  :
${AnsiColor.WHITE};   | ;     \  \    `. :   |  |-,
${AnsiColor.WHITE}|   : |      `----.   \|   :  ;/|
${AnsiColor.WHITE}.   | '___   __ \  \  ||   |   .'
${AnsiColor.WHITE}'   ; : .'| /  /`--'  /'   :  '
${AnsiColor.WHITE}'   | '/  :'--'.     / |   |  |
${AnsiColor.WHITE}|   :    /   `--'---'  |   :  \
${AnsiColor.WHITE} \   \ .'              |   | ,'
${AnsiColor.WHITE}  `---`                `----'


${AnsiColor.MAGENTA}:: Csf :: ${spring.application.name}:${AnsiColor.MAGENTA}${csf.env}${AnsiColor.MAGENTA} :: Running SpringBoot ${spring-boot.version} :: ${AnsiColor.MAGENTA}
