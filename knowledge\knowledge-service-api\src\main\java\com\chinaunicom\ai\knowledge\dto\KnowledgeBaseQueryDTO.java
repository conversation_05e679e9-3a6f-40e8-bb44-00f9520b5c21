package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 知识库查询请求DTO
 */
@Data
@Schema(description = "知识库查询请求DTO")
public class KnowledgeBaseQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识库名称（模糊查询）", example = "知识库")
    private String name;

    @NotNull(message = "页码不能为空")
    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer pageNum;

    @NotNull(message = "每页大小不能为空")
    @Schema(description = "每页大小", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer pageSize;

    @Schema(description = "租户ID", example = "1",hidden = true)
    private String tenantId;
}