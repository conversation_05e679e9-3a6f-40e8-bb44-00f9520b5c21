package com.chinaunicom.csf.system.user.dto;

import com.chinaunicom.csf.plugins.datasecurity.annotations.FieldDecrypt;
import com.chinaunicom.csf.plugins.datasecurity.enums.AlgorithmEnum;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/7
 */
@Data
public class PasswordDto implements Serializable {
	private static final long serialVersionUID = 1L;

	@Parameter(description = "旧密码", required = true)
	@FieldDecrypt(algorithm = AlgorithmEnum.RSA)
	private String oldPassword;

	@Parameter(description = "新密码", required = true)
	@FieldDecrypt(algorithm = AlgorithmEnum.RSA)
	private String newPassword;

	@Parameter(description = "新密码确认", required = true)
	@FieldDecrypt(algorithm = AlgorithmEnum.RSA)
	private String newPassword1;

}
