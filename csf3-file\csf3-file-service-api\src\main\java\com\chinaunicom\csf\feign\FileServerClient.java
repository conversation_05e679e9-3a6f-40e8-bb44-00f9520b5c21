package com.chinaunicom.csf.feign;

import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.dto.CompleteMultipartUploadRequest;
import com.chinaunicom.csf.dto.FileMetadataDTO;
import com.chinaunicom.csf.dto.InitMultipartUploadRequest;
import com.chinaunicom.csf.dto.InitMultipartUploadResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件服务器 Feign 客户端接口
 */
@FeignClient(value = "csf3-file", path = "/file")
public interface FileServerClient {

    /**
     * 上传文件
     *
     * @param file    文件
     * @param bizType 业务类型
     * @return 文件元数据
     */
    @PostMapping(value = "/upload")
    R<FileMetadataDTO> uploadFile(@RequestPart("file") MultipartFile file,
                                  @RequestParam(value = "bizType", required = false) String bizType);

    /**
     * 初始化分片上传
     *
     * @param request 初始化分片上传请求
     * @return 初始化分片上传响应
     */
    @PostMapping("/multipart/init")
    R<InitMultipartUploadResponse> initMultipartUpload(@RequestBody InitMultipartUploadRequest request);

    /**
     * 上传文件分片
     *
     * @param file       分片文件
     * @param fileId     文件ID
     * @param objectName MinIO/S3 中的对象名
     * @param bucketName 存储的桶名
     * @param uploadId   Upload ID
     * @param partNumber 分片编号
     * @return etag
     */
    @PostMapping(value = "/multipart/uploadPart")
    R<String> uploadPart(@RequestPart("file") MultipartFile file,
                         @RequestParam("fileId") String fileId,
                         @RequestParam("objectName") String objectName,
                         @RequestParam("bucketName") String bucketName,
                         @RequestParam("uploadId") String uploadId,
                         @RequestParam("partNumber") Integer partNumber);

    /**
     * 完成分片上传
     *
     * @param request 完成分片上传请求
     * @return 文件元数据
     */
    @PostMapping("/multipart/complete")
    R<FileMetadataDTO> completeMultipartUpload(@RequestBody CompleteMultipartUploadRequest request);

    /**
     * 获取文件元数据
     *
     * @param fileId 文件ID
     * @return 文件元数据
     */
    @GetMapping("/metadata/{fileId}")
    R<FileMetadataDTO> getFileMetadata(@PathVariable("fileId") String fileId);

    /**
     * 获取文件直接访问URL
     *
     * @param fileId 文件ID
     * @return 文件直接访问URL
     */
    @GetMapping("/accessUrl/{fileId}")
    R<String> getFileAccessUrl(@PathVariable("fileId") String fileId);

    /**
     * 删除文件
     *
     * @param fileIds 文件ID列表
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    R<Boolean> deleteFiles(@RequestBody List<String> fileIds);
} 