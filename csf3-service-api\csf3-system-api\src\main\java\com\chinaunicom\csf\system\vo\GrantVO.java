
package com.chinaunicom.csf.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * GrantVO
 *
 */
@Data
public class GrantVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "roleIds集合")
	private List<Long> roleIds;

	@Schema(description = "menuIds集合")
	private List<Long> menuIds = new ArrayList<>();

	@Schema(description = "dataScopeIds集合")
	private List<Long> dataScopeIds = new ArrayList<>();


	@Schema(description = "apiScopeIds集合")
	private List<Long> apiScopeIds = new ArrayList<>();

}
