package com.chinaunicom.csf.agent.controller;


import com.chinaunicom.csf.agent.entity.AgentApps;
import com.chinaunicom.csf.agent.service.IAgentAppsService;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.tool.api.R;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 智能体应用表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/agent-apps")
public class AgentAppsController {

    @Autowired
    private IAgentAppsService agentAppsService;

    @GetMapping("/list")
    public List<AgentApps> list() {
        return agentAppsService.list();
    }

    @GetMapping("/{id}")
    public AgentApps getById(@PathVariable Integer id) {
        return agentAppsService.getById(id);
    }

    @PostMapping("/add")
    public boolean add(@RequestBody AgentApps agentApps) {
        return agentAppsService.save(agentApps);
    }

    @PutMapping("/update")
    public boolean update(@RequestBody AgentApps agentApps) {
        return agentAppsService.updateById(agentApps);
    }

    @DeleteMapping("/delete/{id}")
    public boolean delete(@PathVariable Integer id) {
        return agentAppsService.removeById(id);
    }
}
