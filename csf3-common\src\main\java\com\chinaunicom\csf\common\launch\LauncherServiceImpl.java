
package com.chinaunicom.csf.common.launch;

import com.chinaunicom.csf.common.constant.LauncherConstant;
import com.chinaunicom.csf.core.launch.service.LauncherService;
import org.springframework.boot.SpringApplication;

import java.util.HashMap;
import java.util.Map;

/**
 * 启动参数拓展
 *
 * @deprecated csf v1.5.0之后不推荐用此方式做启动拓展，请尽量用回原生spring boot的方式
 * 我们将会在后续版本不再兼容这个类
 */
public class LauncherServiceImpl implements LauncherService {

	@Override
	public void launcher(SpringApplication application, String appName, String profile) {
		Map<String, Object> props = new HashMap<>();
		props.put("spring.cloud.nacos.discovery.server-addr", LauncherConstant.nacosAddr(profile));
		props.put("spring.cloud.nacos.config.server-addr", LauncherConstant.nacosAddr(profile));
		props.put("spring.cloud.sentinel.transport.dashboard", LauncherConstant.sentinelAddr(profile));
//		 开启elk日志
//		props.put("csf.log.elk.destination", LauncherConstant.elkAddr(profile));
		application.setDefaultProperties(props);
	}

}
