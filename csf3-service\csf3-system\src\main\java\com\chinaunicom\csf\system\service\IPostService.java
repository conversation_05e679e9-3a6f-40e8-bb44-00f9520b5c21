
package com.chinaunicom.csf.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.core.mp.base.BaseService;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.system.entity.Post;
import com.chinaunicom.csf.system.vo.PostVO;

import java.util.List;

/**
 * 岗位表 服务类
 *
 */
public interface IPostService extends BaseService<Post> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param post
	 * @return
	 */
	IPage<PostVO> selectPostPage(IPage<PostVO> page, PostVO post);

	/**
	 * 获取岗位ID
	 *
	 * @param tenantId
	 * @param postNames
	 * @return
	 */
	String getPostIds(String tenantId, String postNames);

	/**
	 * 获取岗位名
	 *
	 * @param postIds
	 * @return
	 */
	List<String> getPostNames(String postIds);

	/**
	 *  post列表，补充岗位信息
	 * @param post
	 * @param query
	 * @return
	 */
	IPage<PostVO> getList(Post post, Query query);

	/**
	 * post岗位详情，补充岗位信息
	 * @param post
	 * @return
	 */
	PostVO getDetail(Post post);
}
