# CSF3 混合检索功能说明

## 📋 功能概述

CSF3混合检索功能将传统的"纯向量匹配"升级为"关键词匹配 + 向量匹配"的混合检索策略，显著提升文档召回的准确率和召回率。

### 核心优势
- **提升召回率**: 关键词匹配捕获精确匹配，向量匹配理解语义相似性
- **平衡准确性**: 通过权重配置平衡字面匹配和语义理解
- **中文优化**: 专门针对中文文档检索进行优化
- **向后兼容**: 保持现有API不变，支持渐进式升级

## 🚀 快速开始

### 1. 启用混合检索

在 `application-dev.yml` 中配置：

```yaml
hybrid-search:
  enabled: true           # 启用混合检索
  keyword-weight: 0.3     # 关键词权重
  vector-weight: 0.7      # 向量权重
```

### 2. 验证配置

启动应用后，检查日志中的配置信息：

```
混合检索启用状态: true
关键词权重: 0.3, 向量权重: 0.7
```

### 3. 测试效果

调用现有的召回接口，系统会自动使用混合检索：

```java
// 前端测试接口
POST /knowledge-base/test-recall
{
    "knowledgeBaseId": 1,
    "queryText": "机器学习算法优化"
}

// Feign客户端接口  
POST /knowledge-base/recall-documents
{
    "kbIds": [1, 2],
    "queryText": "人工智能技术应用"
}
```

## ⚙️ 配置详解

### 基础配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | Boolean | false | 是否启用混合检索 |
| `keyword-weight` | Double | 0.3 | 关键词匹配权重(0.0-1.0) |
| `vector-weight` | Double | 0.7 | 向量匹配权重(0.0-1.0) |
| `analyzer` | String | "ik_max_word" | 中文分词器 |
| `minimum-should-match` | String | "50%" | 最小匹配度 |

### minimumShouldMatch 配置说明

控制关键词查询的匹配严格程度：

- **百分比格式**: `"50%"` - 至少匹配50%的查询词
- **固定数量**: `"2"` - 至少匹配2个查询词
- **动态配置**: 根据查询词数量自动调整

```yaml
dynamic-minimum-should-match:
  1-word: "100%"    # 单词查询必须完全匹配
  2-word: "100%"    # 双词查询必须完全匹配
  3-word: "66%"     # 三词查询至少匹配2个
  4-word: "50%"     # 四词及以上至少匹配50%
```

### 性能配置

```yaml
performance:
  batch-size: 20      # 批处理大小（用于rerank服务等批处理场景）
```

**注意**：
- 已移除 `timeout` 配置：不再在应用层强制限制混合检索的执行时间，允许查询自然完成
- 已移除 `max-results` 配置：结果数量完全由ES查询的topK参数控制，避免重复限制

### 降级策略

```yaml
fallback:
  enabled: true       # 启用自动降级
  error-threshold: 3  # 错误阈值
  recovery-time: 60   # 恢复时间(秒)
```

## 🎯 最佳实践

### 1. 权重配置建议

| 场景 | 关键词权重 | 向量权重 | 适用情况 |
|------|-----------|---------|---------|
| 保守配置 | 0.2 | 0.8 | 优先语义理解，适合概念性查询 |
| **推荐配置** | **0.3** | **0.7** | **平衡精确匹配和语义理解** |
| 激进配置 | 0.4 | 0.6 | 强化精确匹配，适合专业术语查询 |

### 2. 环境配置策略

#### 开发环境
```yaml
hybrid-search:
  enabled: true
  keyword-weight: 0.3
  vector-weight: 0.7
  cache:
    enabled: true
    ttl: 300
  performance:
    batch-size: 20
```

#### 生产环境
```yaml
hybrid-search:
  enabled: false      # 初期建议禁用，验证后启用
  performance:
    batch-size: 20    # 保持与rerank服务一致的批处理大小
  fallback:
    enabled: true     # 必须启用降级机制
```

### 3. 监控指标

关注以下关键指标：

- **性能指标**: 平均响应时间（已移除超时率监控）
- **质量指标**: 召回率、准确率、用户满意度
- **系统指标**: ES集群负载、内存使用率
- **缓存指标**: 缓存命中率、缓存大小

## 🔧 故障排除

### 常见问题

1. **混合检索不生效**
   - 检查 `hybrid-search.enabled` 是否为 `true`
   - 验证权重配置是否有效（权重和应为1.0）

2. **响应时间过长**
   - 检查ES集群性能
   - 优化查询复杂度
   - 检查缓存配置是否生效

3. **检索结果质量下降**
   - 调整权重配置，增加向量权重
   - 检查 `minimum-should-match` 配置是否过于严格
   - 验证中文分词器配置

### 降级机制

当混合检索出现异常时，系统会自动降级到纯向量检索：

```java
// 自动降级逻辑
try {
    return searchHybrid(...);
} catch (Exception e) {
    log.error("混合检索失败，降级到纯向量检索", e);
    return searchVector(...);
}
```

## 📊 效果评估

### 预期提升

| 查询类型 | 当前召回率 | 混合检索召回率 | 提升幅度 |
|---------|-----------|---------------|---------|
| 专业术语 | 65% | 85% | +20% |
| 实体名称 | 70% | 90% | +20% |
| 语义查询 | 80% | 85% | +5% |
| **综合平均** | **72%** | **87%** | **+15%** |

### A/B测试建议

1. **小范围测试**: 选择1-2个知识库进行对比测试
2. **指标对比**: 记录召回率、准确率、响应时间
3. **用户反馈**: 收集用户对检索结果质量的反馈
4. **渐进推广**: 根据测试结果决定推广范围

## 🔄 升级路径

### 阶段一: 开发环境验证
- 启用混合检索功能
- 进行功能和性能测试
- 调优配置参数

### 阶段二: 小范围试点
- 选择部分知识库启用
- 对比测试效果
- 收集用户反馈

### 阶段三: 全面推广
- 根据试点效果决定推广
- 制定回滚方案
- 持续监控和优化

## 📞 技术支持

如有问题，请联系CSF3知识库团队或查看相关文档：

- 技术文档: `/docs/`
- 测试用例: `HybridSearchTest.java`
- 配置示例: `application-dev.yml`
