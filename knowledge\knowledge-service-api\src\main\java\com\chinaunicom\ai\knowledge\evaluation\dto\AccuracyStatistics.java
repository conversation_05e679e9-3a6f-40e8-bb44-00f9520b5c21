package com.chinaunicom.ai.knowledge.evaluation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 详细的准确率统计信息
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Schema(description = "准确率统计信息")
public class AccuracyStatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总召回片段数
     */
    @Schema(description = "总召回片段数")
    private Integer totalRecalledSegments;

    /**
     * 总正确片段数
     */
    @Schema(description = "总正确片段数")
    private Integer totalCorrectSegments;

    /**
     * 总错误片段数
     */
    @Schema(description = "总错误片段数")
    private Integer totalIncorrectSegments;

    /**
     * 有效问题数（参与准确率计算的问题数）
     */
    @Schema(description = "有效问题数")
    private Integer validQuestions;

    /**
     * 无召回结果的问题数
     */
    @Schema(description = "无召回结果的问题数")
    private Integer noRecallQuestions;

    /**
     * 准确率分布统计
     */
    @Schema(description = "准确率分布统计")
    private List<AccuracyDistribution> accuracyDistribution;

    /**
     * 详细计算过程
     */
    @Schema(description = "详细计算过程")
    private String detailedCalculation;
}
