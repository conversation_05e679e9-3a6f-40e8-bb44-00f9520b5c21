package com.chinaunicom.ai.knowledge.vo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 知识文档VO
 */
@Data
@Schema(description = "知识文档VO")
public class KnowledgeDocumentVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "文档ID")
    private Long id;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "上传时间")
    private LocalDateTime createTime;

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "状态中文名")
    private String statusName;
}