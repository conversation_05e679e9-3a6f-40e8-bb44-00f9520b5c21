package com.chinaunicom.ai.knowledge.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

// 轻量化：移除不需要的导入

/**
 * 混合检索配置类
 * 支持关键词匹配和向量匹配的权重配置，以及中文分词优化
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "hybrid-search")
public class HybridSearchConfig {
    
    /**
     * 是否启用混合检索功能
     * 默认关闭，需要显式配置启用
     */
    private Boolean enabled = false;
    
    /**
     * 关键词匹配权重 (0.0-1.0)
     * 建议值：0.2-0.4，过高可能影响语义理解
     * 优化：提高关键词权重以改善召回精度
     */
    private Double keywordWeight = 0.4;

    /**
     * 向量匹配权重 (0.0-1.0)
     * 建议值：0.6-0.8，保持语义理解的主导地位
     * 优化：降低向量权重以减少m3e-base模型误匹配影响
     */
    private Double vectorWeight = 0.6;
    
    /**
     * 中文分词器类型
     * 推荐使用ik_max_word进行细粒度分词
     */
    private String analyzer = "ik_max_word";
    
    /**
     * 关键词查询最小匹配度
     * 支持百分比（如"50%"）和固定数量（如"2"）
     * 优化：提高匹配度要求以减少无关文档召回
     */
    private String minimumShouldMatch = "75%";
    
    // 轻量化：移除复杂配置，只保留核心功能
    // 移除：dynamicMinimumShouldMatch - 使用固定的minimumShouldMatch
    // 移除：cache - 不使用缓存功能
    // 移除：performance - 不进行复杂的性能监控
    // 移除：fallback - 使用简单的异常处理
    
    // 轻量化：移除所有内部配置类
    // 不再支持缓存、性能监控、降级策略等复杂功能
    
    // 轻量化：移除动态最小匹配度方法
    // 直接使用固定的minimumShouldMatch配置
    
    /**
     * 验证配置参数的有效性
     * 
     * @return 配置是否有效
     */
    public boolean isValid() {
        // 权重验证
        if (keywordWeight < 0 || keywordWeight > 1 || 
            vectorWeight < 0 || vectorWeight > 1) {
            return false;
        }
        
        // 权重和验证（允许一定的浮点误差）
        double weightSum = keywordWeight + vectorWeight;
        if (Math.abs(weightSum - 1.0) > 0.01) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取标准化的权重配置
     * 确保权重和为1.0
     * 
     * @return 标准化后的配置
     */
    public HybridSearchConfig normalize() {
        double weightSum = keywordWeight + vectorWeight;
        if (weightSum > 0 && Math.abs(weightSum - 1.0) > 0.01) {
            this.keywordWeight = keywordWeight / weightSum;
            this.vectorWeight = vectorWeight / weightSum;
        }
        return this;
    }
}
