package com.chinaunicom.ai.knowledge.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 评测测试历史实体类
 * 用于记录每次评测的执行历史
 */
@Data
@TableName("evaluation_test_history")
public class EvaluationTestHistory {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;
    
    /**
     * 测试问题（来自questions字段）
     */
    private String question;
    
    /**
     * 期望的文档ID（问题对应的原始文档ID）
     */
    private String expectedDocId;
    
    /**
     * 召回的文档数量
     */
    private Integer recallCount;
    
    /**
     * 是否召回正确（是否包含期望的文档）
     */
    private Boolean isCorrectRecall;
    
    /**
     * 召回的文档ID列表（JSON格式）
     */
    private String recalledDocIds;
    
    /**
     * 相似度分数列表（JSON格式）
     */
    private String similarityScores;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 测试状态：SUCCESS-成功, FAILED-失败
     */
    private String status;
    
    /**
     * 错误信息（如果失败）
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
