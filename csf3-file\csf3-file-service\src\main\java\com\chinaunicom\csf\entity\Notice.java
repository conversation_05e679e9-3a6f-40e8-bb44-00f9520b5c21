package com.chinaunicom.csf.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinaunicom.csf.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("csf_notice")
@EqualsAndHashCode(callSuper = true)
public class Notice extends BaseEntity {

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 通知类型
	 */
	private Integer category;

	/**
	 * 发布日期
	 */
	private Date releaseTime;

	/**
	 * 内容
	 */
	private String content;

}
