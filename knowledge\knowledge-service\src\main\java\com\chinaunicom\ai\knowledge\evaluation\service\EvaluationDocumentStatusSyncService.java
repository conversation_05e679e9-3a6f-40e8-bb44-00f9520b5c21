package com.chinaunicom.ai.knowledge.evaluation.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评测文档状态同步服务
 * 
 * 用于同步 evaluation_document 和 knowledge_document 表之间的状态
 * 解决向量化完成后状态不一致的问题
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationDocumentStatusSyncService {

    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final KnowledgeDocumentMapper knowledgeDocumentMapper;

    /**
     * 同步单个文档的状态
     * 
     * @param knowledgeDocId 知识文档ID
     * @param newStatus 新状态
     */
    @Transactional
    public void syncDocumentStatus(Long knowledgeDocId, DocumentStatusEnum newStatus) {
        try {
            // 查找对应的评测文档
            LambdaQueryWrapper<EvaluationDocument> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EvaluationDocument::getKnowledgeDocId, knowledgeDocId);
            
            EvaluationDocument evaluationDocument = evaluationDocumentMapper.selectOne(queryWrapper);
            
            if (evaluationDocument != null) {
                String statusString = convertStatusToString(newStatus);
                
                // 只有状态确实不同时才更新
                if (!statusString.equals(evaluationDocument.getStatus())) {
                    evaluationDocument.setStatus(statusString);
                    evaluationDocument.setUpdateTime(LocalDateTime.now());
                    
                    int updateCount = evaluationDocumentMapper.updateById(evaluationDocument);
                    if (updateCount > 0) {
                        log.info("同步评测文档状态成功: 知识文档ID={}, 原始文档ID={}, 新状态={}", 
                                knowledgeDocId, evaluationDocument.getOriginalDocId(), statusString);
                    } else {
                        log.warn("同步评测文档状态失败，更新行数为0: 知识文档ID={}", knowledgeDocId);
                    }
                } else {
                    log.debug("评测文档状态已是最新，无需更新: 知识文档ID={}, 状态={}", knowledgeDocId, statusString);
                }
            } else {
                log.debug("未找到对应的评测文档，可能不是评测导入的文档: 知识文档ID={}", knowledgeDocId);
            }
            
        } catch (Exception e) {
            log.error("同步评测文档状态时发生异常: 知识文档ID={}, 状态={}", knowledgeDocId, newStatus, e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 批量修复状态不一致的数据
     * 
     * @return 修复的记录数
     */
    @Transactional
    public int fixInconsistentStatuses() {
        log.info("开始批量修复评测文档状态不一致问题");
        
        try {
            // 查找状态不一致的记录
            List<EvaluationDocument> inconsistentDocs = findInconsistentDocuments();
            
            if (inconsistentDocs.isEmpty()) {
                log.info("未发现状态不一致的评测文档");
                return 0;
            }
            
            log.info("发现 {} 个状态不一致的评测文档，开始修复", inconsistentDocs.size());
            
            int fixedCount = 0;
            for (EvaluationDocument evalDoc : inconsistentDocs) {
                try {
                    // 获取对应的知识文档状态
                    KnowledgeDocument knowledgeDoc = knowledgeDocumentMapper.selectById(evalDoc.getKnowledgeDocId());
                    if (knowledgeDoc != null) {
                        DocumentStatusEnum knowledgeStatus = DocumentStatusEnum.values()[knowledgeDoc.getStatus()];
                        String correctStatus = convertStatusToString(knowledgeStatus);
                        
                        // 更新评测文档状态
                        evalDoc.setStatus(correctStatus);
                        evalDoc.setUpdateTime(LocalDateTime.now());
                        
                        int updateCount = evaluationDocumentMapper.updateById(evalDoc);
                        if (updateCount > 0) {
                            fixedCount++;
                            log.info("修复评测文档状态: 原始文档ID={}, 知识文档ID={}, {} -> {}", 
                                    evalDoc.getOriginalDocId(), evalDoc.getKnowledgeDocId(), 
                                    evalDoc.getStatus(), correctStatus);
                        }
                    }
                } catch (Exception e) {
                    log.error("修复单个评测文档状态失败: 原始文档ID={}", evalDoc.getOriginalDocId(), e);
                }
            }
            
            log.info("批量修复完成，成功修复 {} 个评测文档状态", fixedCount);
            return fixedCount;
            
        } catch (Exception e) {
            log.error("批量修复评测文档状态时发生异常", e);
            throw new RuntimeException("批量修复评测文档状态失败", e);
        }
    }

    /**
     * 查找状态不一致的文档
     */
    private List<EvaluationDocument> findInconsistentDocuments() {
        // 这里使用原生SQL查询，因为需要跨表比较状态
        return evaluationDocumentMapper.selectList(
            new LambdaQueryWrapper<EvaluationDocument>()
                .isNotNull(EvaluationDocument::getKnowledgeDocId)
        ).stream()
        .filter(evalDoc -> {
            try {
                KnowledgeDocument knowledgeDoc = knowledgeDocumentMapper.selectById(evalDoc.getKnowledgeDocId());
                if (knowledgeDoc != null) {
                    DocumentStatusEnum knowledgeStatus = DocumentStatusEnum.values()[knowledgeDoc.getStatus()];
                    String expectedEvalStatus = convertStatusToString(knowledgeStatus);
                    return !expectedEvalStatus.equals(evalDoc.getStatus());
                }
                return false;
            } catch (Exception e) {
                log.warn("检查文档状态一致性时发生异常: 原始文档ID={}", evalDoc.getOriginalDocId(), e);
                return false;
            }
        })
        .toList();
    }

    /**
     * 将 DocumentStatusEnum 转换为 evaluation_document 表使用的字符串状态
     */
    private String convertStatusToString(DocumentStatusEnum status) {
        return switch (status) {
            case UPLOADING -> "PENDING";
            case UPLOADED -> "UPLOADED";
            case VECTORIZING -> "VECTORIZING";
            case VECTORIZED -> "VECTORIZED";
            case UPLOAD_FAILED -> "FAILED";
            case VECTORIZE_FAILED -> "VECTORIZE_FAILED";
            case DELETED -> "DELETED";
        };
    }

    /**
     * 检查并报告状态不一致的统计信息
     */
    public void reportInconsistentStatuses() {
        try {
            List<EvaluationDocument> inconsistentDocs = findInconsistentDocuments();
            
            if (inconsistentDocs.isEmpty()) {
                log.info("✅ 所有评测文档状态一致");
                return;
            }
            
            log.warn("⚠️ 发现 {} 个状态不一致的评测文档:", inconsistentDocs.size());
            
            // 按知识库分组统计
            inconsistentDocs.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    EvaluationDocument::getKnowledgeBaseId,
                    java.util.stream.Collectors.counting()
                ))
                .forEach((kbId, count) -> 
                    log.warn("  知识库 {}: {} 个不一致文档", kbId, count)
                );
                
        } catch (Exception e) {
            log.error("检查状态一致性时发生异常", e);
        }
    }
}
