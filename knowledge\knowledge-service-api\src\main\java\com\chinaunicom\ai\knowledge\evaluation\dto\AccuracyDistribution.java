package com.chinaunicom.ai.knowledge.evaluation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 准确率分布
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Schema(description = "准确率分布")
public class AccuracyDistribution implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 准确率值
     */
    @Schema(description = "准确率值")
    private Double accuracyValue;

    /**
     * 该准确率的问题数量
     */
    @Schema(description = "该准确率的问题数量")
    private Integer questionCount;

    /**
     * 贡献的准确率总和
     */
    @Schema(description = "贡献的准确率总和")
    private Double contributionSum;
}
