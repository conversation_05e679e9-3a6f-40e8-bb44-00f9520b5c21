<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationTestHistoryMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationTestHistory">
        <id column="id" property="id" />
        <result column="knowledge_base_id" property="knowledgeBaseId" />
        <result column="question" property="question" />
        <result column="expected_doc_id" property="expectedDocId" />
        <result column="recall_count" property="recallCount" />
        <result column="is_correct_recall" property="isCorrectRecall" />
        <result column="recalled_doc_ids" property="recalledDocIds" />
        <result column="similarity_scores" property="similarityScores" />
        <result column="execution_time" property="executionTime" />
        <result column="status" property="status" />
        <result column="error_message" property="errorMessage" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 分页查询测试历史 -->
    <select id="selectPageByKnowledgeBaseId" resultMap="BaseResultMap">
        SELECT * FROM evaluation_test_history 
        WHERE knowledge_base_id = #{knowledgeBaseId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计指定知识库的测试次数 -->
    <select id="countByKnowledgeBaseId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM evaluation_test_history 
        WHERE knowledge_base_id = #{knowledgeBaseId}
    </select>

    <!-- 统计指定知识库的正确召回次数 -->
    <select id="countCorrectRecallByKnowledgeBaseId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM evaluation_test_history 
        WHERE knowledge_base_id = #{knowledgeBaseId}
        AND is_correct_recall = 1
        AND status = 'SUCCESS'
    </select>

    <!-- 查询最近的测试记录 -->
    <select id="selectRecentTests" resultMap="BaseResultMap">
        SELECT * FROM evaluation_test_history
        WHERE knowledge_base_id = #{knowledgeBaseId}
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 批量插入测试历史记录 -->
    <insert id="insertBatch">
        INSERT INTO evaluation_test_history (
            knowledge_base_id, question, expected_doc_id, recall_count,
            is_correct_recall, recalled_doc_ids, similarity_scores,
            execution_time, status, error_message, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.knowledgeBaseId}, #{item.question}, #{item.expectedDocId}, #{item.recallCount},
                #{item.isCorrectRecall}, #{item.recalledDocIds}, #{item.similarityScores},
                #{item.executionTime}, #{item.status}, #{item.errorMessage}, #{item.createTime}
            )
        </foreach>
    </insert>

</mapper>
