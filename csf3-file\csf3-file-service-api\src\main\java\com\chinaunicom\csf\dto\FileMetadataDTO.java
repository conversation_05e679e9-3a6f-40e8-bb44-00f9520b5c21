package com.chinaunicom.csf.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 文件元数据DTO
 */
@Data
public class FileMetadataDTO {

    /**
     * 文件ID (32位不含横线的UUID)
     */
    private String id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * MinIO 中的对象名
     */
    private String objectName;

    /**
     * 存储的桶名
     */
    private String bucketName;

    /**
     * 文件大小 (字节)
     */
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 业务类型 (例如: 'avatar', 'document')
     */
    private String bizType;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 访问路径 (例如: 预签名URL或公共URL)
     */
    private String accessPath;

    /**
     * URL过期时间（仅对预签名URL有效）
     */
    private LocalDateTime urlExpireTime;

    /**
     * 分片上传ID（如果是非分片上传则为空）
     */
    private String uploadId;

    /**
     * 分片数量（如果是非分片上传则为空）
     */
    private Integer partCount;
}