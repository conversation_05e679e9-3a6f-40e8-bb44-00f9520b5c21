package com.chinaunicom.ai.knowledge.evaluation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationTestHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评测测试历史Mapper接口
 */
@Mapper
public interface EvaluationTestHistoryMapper extends BaseMapper<EvaluationTestHistory> {
    
    /**
     * 分页查询测试历史
     */
    IPage<EvaluationTestHistory> selectPageByKnowledgeBaseId(
            Page<EvaluationTestHistory> page, 
            @Param("knowledgeBaseId") Long knowledgeBaseId
    );
    
    /**
     * 统计指定知识库的测试次数
     */
    Integer countByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 统计指定知识库的正确召回次数
     */
    Integer countCorrectRecallByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 查询最近的测试记录
     */
    List<EvaluationTestHistory> selectRecentTests(@Param("knowledgeBaseId") Long knowledgeBaseId, @Param("limit") Integer limit);

    /**
     * 批量插入测试历史记录
     */
    int insertBatch(@Param("list") List<EvaluationTestHistory> list);
}
