package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;

@Data
@Schema(description = "知识库创建请求DTO")
public class KnowledgeBaseCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "知识库名称不能为空")
    @Schema(description = "知识库名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "我的第一个知识库")
    private String name;

    @Schema(description = "知识库描述", example = "这是一个关于人工智能的知识库。")
    private String descrip; // 将 'desc' 更改为 'descrip'

    @NotNull(message = "关联模型ID不能为空")
    @Schema(description = "关联模型ID（向量嵌入模型）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long vecModel;
}