package com.chinaunicom.ai.knowledge.service;

import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * Elasticsearch操作服务的空实现（No-Op）
 * 当 `elasticsearch.enabled` 配置为 `false` 时，此服务会被加载，用于跳过实际的ES操作。
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "elasticsearch.enabled", havingValue = "false") // 当 elasticsearch.enabled 为 false 时加载
public class NoOpElasticSearchService implements IElasticSearchService { // 实现接口

    public NoOpElasticSearchService() {
        log.warn("Elasticsearch集成已禁用。正在使用 NoOpElasticSearchService。");
    }

    @Override
    public boolean createIndex(String indexName, Integer dims) {
        log.warn("Elasticsearch集成已禁用。跳过创建索引: {}", indexName);
        return true; // 假装成功
    }

    @Override
    public boolean deleteIndex(String indexName) {
        log.warn("Elasticsearch集成已禁用。跳过删除索引: {}", indexName);
        return true; // 假装成功
    }

    @Override
    public List<AgentSearchResultVO> searchVector(String indexName, float[] queryVector, int topK, Long knowledgeBaseId, String tenantId) {
        log.warn("Elasticsearch集成已禁用。跳过向量搜索，索引: {}", indexName);
        return Collections.emptyList(); // 返回空列表
    }

    @Override
    public boolean deleteDocumentByMysqlDocId(String indexName, Long mysqlDocId) {
        log.warn("Elasticsearch集成已禁用。跳过删除文档片段，索引: {}, 文档ID: {}", indexName, mysqlDocId);
        return false;
    }

    @Override
    public long countDocuments(String indexName) {
        log.warn("Elasticsearch集成已禁用。跳过统计文档片段数量，索引: {}", indexName);
        return 0L; // 返回0表示没有片段
    }

    @Override
    public List<AgentSearchResultVO> searchHybrid(String indexName, String queryText, float[] queryVector,
                                                 int topK, Long knowledgeBaseId, String tenantId,
                                                 HybridSearchConfig config) {
        log.warn("Elasticsearch集成已禁用。跳过混合检索，索引: {}, 查询: '{}'", indexName, queryText);
        return Collections.emptyList(); // 返回空列表
    }
}
