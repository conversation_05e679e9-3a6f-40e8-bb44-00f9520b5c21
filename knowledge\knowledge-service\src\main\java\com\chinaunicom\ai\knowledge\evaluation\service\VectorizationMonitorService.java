package com.chinaunicom.ai.knowledge.evaluation.service;

import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 向量化状态监控服务
 * 
 * 用于监控文档向量化进度，等待向量化完成
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class VectorizationMonitorService {

    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    private final EvaluationDocumentMapper evaluationDocumentMapper;

    /**
     * 等待知识库中所有文档向量化完成
     * 
     * @param knowledgeBaseId 知识库ID
     * @param timeoutSeconds 超时时间（秒）
     * @param checkIntervalSeconds 检查间隔（秒）
     * @return 向量化监控结果
     */
    public VectorizationMonitorResult waitForVectorizationComplete(
            Long knowledgeBaseId, 
            int timeoutSeconds, 
            int checkIntervalSeconds) {
        
        log.info("开始监控知识库 {} 的向量化进度，超时时间: {}秒，检查间隔: {}秒", 
                knowledgeBaseId, timeoutSeconds, checkIntervalSeconds);
        
        VectorizationMonitorResult result = new VectorizationMonitorResult();
        result.setKnowledgeBaseId(knowledgeBaseId);
        result.setStartTime(LocalDateTime.now());
        result.setTimeoutSeconds(timeoutSeconds);
        result.setCheckIntervalSeconds(checkIntervalSeconds);
        
        long startTime = System.currentTimeMillis();
        long timeoutMs = timeoutSeconds * 1000L;
        int checkCount = 0;
        
        try {
            while (System.currentTimeMillis() - startTime < timeoutMs) {
                checkCount++;
                
                // 检查向量化状态
                VectorizationStatus status = checkVectorizationStatus(knowledgeBaseId);
                result.setLastCheckStatus(status);
                result.setCheckCount(checkCount);
                
                log.debug("第{}次检查向量化状态 - 总数: {}, 已完成: {}, 失败: {}, 进行中: {}", 
                        checkCount, status.getTotalDocuments(), status.getVectorizedCount(), 
                        status.getFailedCount(), status.getVectorizingCount());
                
                // 检查是否全部完成
                if (status.isAllCompleted()) {
                    result.setSuccess(true);
                    result.setCompletedSuccessfully(true);
                    result.setEndTime(LocalDateTime.now());
                    result.setDurationMs(System.currentTimeMillis() - startTime);
                    
                    log.info("知识库 {} 向量化完成！总文档数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                            knowledgeBaseId, status.getTotalDocuments(), status.getVectorizedCount(), 
                            status.getFailedCount(), result.getDurationMs());
                    
                    return result;
                }
                
                // 检查是否有失败的文档
                if (status.getFailedCount() > 0) {
                    log.warn("知识库 {} 有 {} 个文档向量化失败", knowledgeBaseId, status.getFailedCount());
                }
                
                // 等待下次检查
                try {
                    TimeUnit.SECONDS.sleep(checkIntervalSeconds);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    result.setSuccess(false);
                    result.setErrorMessage("向量化监控被中断");
                    return result;
                }
            }
            
            // 超时
            result.setSuccess(false);
            result.setTimedOut(true);
            result.setErrorMessage("向量化监控超时");
            result.setEndTime(LocalDateTime.now());
            result.setDurationMs(System.currentTimeMillis() - startTime);
            
            log.warn("知识库 {} 向量化监控超时，最后状态: {}", knowledgeBaseId, result.getLastCheckStatus());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage("向量化监控异常: " + e.getMessage());
            result.setEndTime(LocalDateTime.now());
            result.setDurationMs(System.currentTimeMillis() - startTime);
            
            log.error("知识库 {} 向量化监控异常", knowledgeBaseId, e);
        }
        
        return result;
    }

    /**
     * 检查知识库的向量化状态
     *
     * @param knowledgeBaseId 知识库ID
     * @return 向量化状态
     */
    public VectorizationStatus checkVectorizationStatus(Long knowledgeBaseId) {
        VectorizationStatus status = new VectorizationStatus();
        status.setKnowledgeBaseId(knowledgeBaseId);
        status.setCheckTime(LocalDateTime.now());

        try {
            // 查询知识库中的所有文档
            List<KnowledgeDocument> documents = knowledgeDocumentMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<KnowledgeDocument>()
                    .eq(KnowledgeDocument::getBaseId, knowledgeBaseId)
                    .eq(KnowledgeDocument::getIsDeleted, 0)
            );

            // 同时检查evaluation_document表的状态（如果存在）
            List<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument> evaluationDocuments =
                evaluationDocumentMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument>()
                        .eq(com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument::getKnowledgeBaseId, knowledgeBaseId)
                );
            
            status.setTotalDocuments(documents.size());
            
            // 统计各种状态的文档数量
            int vectorizedCount = 0;
            int vectorizingCount = 0;
            int failedCount = 0;
            int uploadedCount = 0;
            int uploadingCount = 0;

            // 如果存在evaluation_document，需要综合考虑两个表的状态
            if (!evaluationDocuments.isEmpty()) {
                log.debug("知识库 {} 包含评测文档，将综合检查两个表的状态", knowledgeBaseId);

                // 创建knowledge_document的映射，便于查找
                Map<Long, KnowledgeDocument> knowledgeDocMap = documents.stream()
                    .collect(java.util.stream.Collectors.toMap(KnowledgeDocument::getId, doc -> doc));

                // 遍历evaluation_document，综合判断状态
                for (com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument evalDoc : evaluationDocuments) {
                    if (evalDoc.getKnowledgeDocId() == null) {
                        continue;
                    }

                    KnowledgeDocument knowledgeDoc = knowledgeDocMap.get(evalDoc.getKnowledgeDocId());
                    if (knowledgeDoc == null) {
                        continue;
                    }

                    // 以knowledge_document的状态为准，因为它是真实的向量化状态
                    // evaluation_document的状态可能存在延迟同步的情况
                    Integer knowledgeStatus = knowledgeDoc.getStatus();

                    if (knowledgeStatus.equals(DocumentStatusEnum.VECTORIZED.getCode())) {
                        vectorizedCount++;
                    } else if (knowledgeStatus.equals(DocumentStatusEnum.VECTORIZING.getCode())) {
                        vectorizingCount++;
                    } else if (knowledgeStatus.equals(DocumentStatusEnum.VECTORIZE_FAILED.getCode())) {
                        failedCount++;
                    } else if (knowledgeStatus.equals(DocumentStatusEnum.UPLOADED.getCode())) {
                        uploadedCount++;
                    } else if (knowledgeStatus.equals(DocumentStatusEnum.UPLOADING.getCode())) {
                        uploadingCount++;
                    }
                }
            } else {
                // 没有evaluation_document，只检查knowledge_document
                for (KnowledgeDocument doc : documents) {
                    Integer docStatus = doc.getStatus();
                    if (docStatus == null) {
                        continue;
                    }

                    if (docStatus.equals(DocumentStatusEnum.VECTORIZED.getCode())) {
                        vectorizedCount++;
                    } else if (docStatus.equals(DocumentStatusEnum.VECTORIZING.getCode())) {
                        vectorizingCount++;
                    } else if (docStatus.equals(DocumentStatusEnum.VECTORIZE_FAILED.getCode())) {
                        failedCount++;
                    } else if (docStatus.equals(DocumentStatusEnum.UPLOADED.getCode())) {
                        uploadedCount++;
                    } else if (docStatus.equals(DocumentStatusEnum.UPLOADING.getCode())) {
                        uploadingCount++;
                    }
                }
            }
            
            status.setVectorizedCount(vectorizedCount);
            status.setVectorizingCount(vectorizingCount);
            status.setFailedCount(failedCount);
            status.setUploadedCount(uploadedCount);
            status.setUploadingCount(uploadingCount);
            
            // 计算进度
            if (status.getTotalDocuments() > 0) {
                status.setProgress((double) vectorizedCount / status.getTotalDocuments() * 100);
            }
            
        } catch (Exception e) {
            log.error("检查知识库 {} 向量化状态失败", knowledgeBaseId, e);
            status.setError("检查状态失败: " + e.getMessage());
        }
        
        return status;
    }

    /**
     * 专门用于评测的向量化状态检查
     * 综合考虑knowledge_document和evaluation_document两个表的状态
     *
     * @param knowledgeBaseId 知识库ID
     * @param autoSync 是否自动同步不一致的状态
     * @return 向量化状态
     */
    public VectorizationStatus checkEvaluationVectorizationStatus(Long knowledgeBaseId, boolean autoSync) {
        VectorizationStatus status = checkVectorizationStatus(knowledgeBaseId);

        // 如果是评测知识库，添加额外的状态一致性检查
        try {
            List<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument> evaluationDocuments =
                evaluationDocumentMapper.selectList(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument>()
                        .eq(com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument::getKnowledgeBaseId, knowledgeBaseId)
                );

            if (!evaluationDocuments.isEmpty()) {
                // 检查并可能修复状态不一致的情况
                int inconsistentCount = 0;
                int syncedCount = 0;

                for (com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument evalDoc : evaluationDocuments) {
                    if (evalDoc.getKnowledgeDocId() != null) {
                        KnowledgeDocument knowledgeDoc = knowledgeDocumentMapper.selectById(evalDoc.getKnowledgeDocId());
                        if (knowledgeDoc != null) {
                            String expectedEvalStatus = convertKnowledgeStatusToEvalStatus(knowledgeDoc.getStatus());

                            // 检查状态是否一致
                            if (!expectedEvalStatus.equals(evalDoc.getStatus())) {
                                inconsistentCount++;

                                if (autoSync) {
                                    // 自动同步状态
                                    try {
                                        String oldStatus = evalDoc.getStatus();
                                        evalDoc.setStatus(expectedEvalStatus);
                                        evalDoc.setUpdateTime(java.time.LocalDateTime.now());

                                        int updateResult = evaluationDocumentMapper.updateById(evalDoc);
                                        if (updateResult > 0) {
                                            syncedCount++;
                                            log.info("评测前自动同步文档状态: 原始文档ID={}, 知识文档ID={}, {} -> {}",
                                                    evalDoc.getOriginalDocId(), evalDoc.getKnowledgeDocId(),
                                                    oldStatus, expectedEvalStatus);
                                        }
                                    } catch (Exception e) {
                                        log.error("自动同步评测文档状态失败: 原始文档ID={}", evalDoc.getOriginalDocId(), e);
                                    }
                                }
                            }
                        }
                    }
                }

                if (autoSync && syncedCount > 0) {
                    log.info("评测前自动状态同步完成: 知识库={}, 同步了 {} 个文档状态", knowledgeBaseId, syncedCount);
                    // 重新检查状态，获取最新的统计信息
                    status = checkVectorizationStatus(knowledgeBaseId);
                } else if (inconsistentCount > 0) {
                    log.warn("知识库 {} 发现 {} 个状态不一致的评测文档", knowledgeBaseId, inconsistentCount);
                    status.setError(String.format("发现 %d 个状态不一致的文档", inconsistentCount));
                }
            }

        } catch (Exception e) {
            log.error("检查评测向量化状态时发生异常，知识库ID: {}", knowledgeBaseId, e);
            status.setError("状态检查异常: " + e.getMessage());
        }

        return status;
    }

    /**
     * 兼容性方法，默认不自动同步
     */
    public VectorizationStatus checkEvaluationVectorizationStatus(Long knowledgeBaseId) {
        return checkEvaluationVectorizationStatus(knowledgeBaseId, false);
    }

    /**
     * 将knowledge_document状态转换为evaluation_document状态
     */
    private String convertKnowledgeStatusToEvalStatus(Integer knowledgeStatus) {
        if (knowledgeStatus == null) {
            return "PENDING";
        }

        return switch (knowledgeStatus) {
            case 0 -> "PENDING";        // UPLOADING
            case 1 -> "UPLOADED";       // UPLOADED
            case 2 -> "VECTORIZING";    // VECTORIZING
            case 3 -> "VECTORIZED";     // VECTORIZED
            case 4 -> "FAILED";         // UPLOAD_FAILED
            case 5 -> "VECTORIZE_FAILED"; // VECTORIZE_FAILED
            case 6 -> "DELETED";        // DELETED
            default -> "PENDING";
        };
    }

    /**
     * 向量化监控结果
     */
    @Data
    public static class VectorizationMonitorResult {
        private Long knowledgeBaseId;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Long durationMs;
        private Integer timeoutSeconds;
        private Integer checkIntervalSeconds;
        private Integer checkCount;
        private Boolean success;
        private Boolean completedSuccessfully;
        private Boolean timedOut;
        private String errorMessage;
        private VectorizationStatus lastCheckStatus;
    }

    /**
     * 向量化状态
     */
    @Data
    public static class VectorizationStatus {
        private Long knowledgeBaseId;
        private LocalDateTime checkTime;
        private Integer totalDocuments;
        private Integer vectorizedCount;
        private Integer vectorizingCount;
        private Integer failedCount;
        private Integer uploadedCount;
        private Integer uploadingCount;
        private Double progress; // 百分比
        private String error;
        
        /**
         * 是否全部完成（成功或失败）
         */
        public boolean isAllCompleted() {
            if (totalDocuments == null || totalDocuments == 0) {
                return true;
            }
            
            int completedCount = (vectorizedCount != null ? vectorizedCount : 0) + 
                               (failedCount != null ? failedCount : 0);
            
            return completedCount >= totalDocuments;
        }
        
        /**
         * 是否有进行中的任务
         */
        public boolean hasInProgress() {
            return (vectorizingCount != null && vectorizingCount > 0) ||
                   (uploadingCount != null && uploadingCount > 0) ||
                   (uploadedCount != null && uploadedCount > 0);
        }
        
        /**
         * 获取状态描述
         */
        public String getStatusDescription() {
            if (error != null) {
                return "错误: " + error;
            }
            
            if (totalDocuments == null || totalDocuments == 0) {
                return "无文档";
            }
            
            if (isAllCompleted()) {
                return String.format("完成 - 成功: %d, 失败: %d", 
                    vectorizedCount != null ? vectorizedCount : 0,
                    failedCount != null ? failedCount : 0);
            }
            
            return String.format("进行中 - 总数: %d, 已完成: %d, 向量化中: %d, 失败: %d", 
                totalDocuments,
                vectorizedCount != null ? vectorizedCount : 0,
                vectorizingCount != null ? vectorizingCount : 0,
                failedCount != null ? failedCount : 0);
        }
    }
}
