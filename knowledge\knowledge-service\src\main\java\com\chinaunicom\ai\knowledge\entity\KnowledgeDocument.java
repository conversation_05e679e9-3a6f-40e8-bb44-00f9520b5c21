package com.chinaunicom.ai.knowledge.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识文档
 * @date 2025/6/3 17:14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KnowledgeDocument implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建用户ID
     */
    private Long createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新用户ID
     */
    private Long updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 知识库ID
     */
    private Long baseId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 切片数量
     */
    private Long sliceTotal;

    /**
     * 字符总数
     */
    private Long charTotal;

    /**
     * 文件存储地址
     */
    private String fileUrl;

    /**
     * minio上的文件id
     */
    private String fileObjectId;

    private String tenantId;

    private String fileType;
}
