package com.chinaunicom.ai.knowledge.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseCreateDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseQueryDTO;
import com.chinaunicom.ai.knowledge.dto.KnowledgeBaseUpdateDTO;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseDetailVO;
import com.chinaunicom.ai.knowledge.vo.KnowledgeBaseListVO;
import com.chinaunicom.ai.knowledge.vo.RecallDocSegment;
import com.chinaunicom.csf.core.tool.api.R;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:知识库 服务类
 * @date 2025/6/3 17:14
 */
public interface KnowledgeBaseService extends IService<KnowledgeBase> {

    @Transactional
        // 事务管理，确保MySQL和ES操作的原子性
    R<Boolean> createKnowledgeBase(KnowledgeBaseCreateDTO createDTO);

    @Transactional
    R<Boolean> updateKnowledgeBase(KnowledgeBaseUpdateDTO updateDTO);

    R<KnowledgeBaseDetailVO> getKnowledgeBaseDetail(Long id);

    R<IPage<KnowledgeBaseListVO>> getKnowledgeBaseList(KnowledgeBaseQueryDTO queryDTO);

    @Transactional
    R<Boolean> deleteKnowledgeBase(Long id);

    List<AgentSearchResultVO> searchKnowledgeBaseByVector(Long knowledgeBaseId, float[] queryVector, Integer topK);

    /**
     * 智能体接口：知识库文档召回
     * 根据知识库ID列表和问题文本，进行向量化后在多个知识库中检索，召回相关文档片段。
     * @param kbIds 知识库ID列表
     * @param queryText 问题文本
     * @param topK 召回文档数量
     * @return 召回的文档片段列表
     */
    List<RecallDocSegment> recallDocuments(List<Long> kbIds, String queryText, Integer topK);
}
