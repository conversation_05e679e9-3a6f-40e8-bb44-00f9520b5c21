package com.chinaunicom.csf.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 智能体与大模型关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent_apps_llm_relation")
@Schema(description="智能体与大模型关联表")
public class AgentAppsLlmRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联记录 ID，自增主键，唯一标识智能体与大模型的关联关系")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "关联的智能体 ID，外键关联 agent_apps.id，级联删除")
    @TableField("agent_id")
    private Integer agentId;

    @Schema(description = "关联的大模型 ID，外键关联 agent_llms.id，级联删除")
    @TableField("llm_id")
    private Integer llmId;

    @Schema(description = "是否为主模型：0=否，1=是（一个智能体最多有一个主模型）")
    @TableField("is_primary")
    private Boolean isPrimary;

    @Schema(description = "问答随机性参数（温度），取值范围 0.0-2.0：值越高，回答越随机")
    @TableField("temperature")
    private Float temperature;

    @Schema(description = "生成回复的最大 token 数，限制输出长度（如：OpenAI 模型默认最大 4096）")
    @TableField("max_tokens")
    private Integer maxTokens;

    @Schema(description = "保留的历史对话轮数，用于上下文理解（如：保留最近 5 轮对话）")
    @TableField("context_rounds")
    private Integer contextRounds;

    @Schema(description = "核采样概率（Top P），取值范围 0.0-1.0：值越小，回答越聚焦于高概率内容")
    @TableField("top_p")
    private Float topP;

    @Schema(description = "关联记录创建时间，自动填充当前时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @Schema(description = "关联记录更新时间，修改时自动更新")
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
