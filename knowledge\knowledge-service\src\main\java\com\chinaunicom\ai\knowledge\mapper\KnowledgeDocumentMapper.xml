<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.entity.KnowledgeDocument">
        <id column="id" property="id" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="is_deleted" property="isDeleted" />
        <result column="base_id" property="baseId" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="slice_total" property="sliceTotal" />
        <result column="char_total" property="charTotal" />
        <result column="file_url" property="fileUrl" />
        <result column="file_object_id" property="fileObjectId" />
        <result column="tenant_id" property="tenantId" />
        <result column="file_type" property="fileType" />
    </resultMap>

    <update id="logicalDeleteByBaseId">
        UPDATE knowledge_document
        SET
            is_deleted = 1,
            status = #{deletedStatusCode},
            update_time = #{updateTime},
            update_user = #{updateUser}
        WHERE
            base_id = #{baseId}
          AND tenant_id = #{tenantId}
          AND is_deleted = 0
    </update>

</mapper>
