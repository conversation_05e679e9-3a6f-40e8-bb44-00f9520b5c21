# 平均准确率公式详细显示功能测试指南

## 📋 功能概述

本次修改增强了端到端评测系统中`averageAccuracyFormula`字段的显示效果，从简单的结果显示改为详细的计算过程展示。

## 🎯 修改内容

### 修改前
```json
"averageAccuracyFormula": "平均准确率 = 所有问题准确率的平均值 = Σ(召回结果中正确文档数/召回结果总数) / 有效问题数 = 0.8133"
```

### 修改后
```json
"averageAccuracyFormula": "平均准确率 = 所有问题准确率的平均值 = (1.0000 + 1.0000 + 1.0000 + 1.0000 + 0.6667 + 1.0000 + 0.6667 + 0.8000 + 0.0000 + 1.0000) / 10 = 8.1333 / 10 = 0.8133"
```

## 🔧 修改的文件

### 1. EvaluationTestService.java
- **位置**: `knowledge/knowledge-service/src/main/java/com/chinaunicom/ai/knowledge/evaluation/service/EvaluationTestService.java`
- **修改内容**:
  - 第105行：调用新的`buildDetailedAverageAccuracyFormula`方法
  - 第396-430行：新增详细公式构建方法

### 2. FullEvaluationTransactionService.java  
- **位置**: `knowledge/knowledge-service/src/main/java/com/chinaunicom/ai/knowledge/evaluation/service/FullEvaluationTransactionService.java`
- **修改内容**:
  - 第187行：调用新的`buildDetailedAverageAccuracyFormula`方法
  - 第364-401行：新增详细公式构建方法

## 🧪 测试步骤

### 测试1：普通评测接口
```bash
# 调用普通评测接口
POST /evaluation/run-evaluation
{
  "knowledgeBaseId": 65
}
```

**期望结果**：
- 响应中的`averageAccuracyFormula`字段显示详细计算过程
- 包含每个问题的准确率值
- 显示求和过程和最终除法运算

### 测试2：端到端评测接口
```bash
# 调用端到端评测接口
POST /evaluation/run-full-evaluation
{
  "maxDocuments": 10,
  "useNewRecallFormula": true,
  "enableCleanup": true
}
```

**期望结果**：
- 响应中的`evaluationResult.averageAccuracyFormula`字段显示详细计算过程
- 格式与普通评测接口一致

## 📊 预期输出格式

### 完整示例
基于test4的实际数据，期望的公式格式：
```
平均准确率 = 所有问题准确率的平均值 = (1.0000 + 1.0000 + 1.0000 + 1.0000 + 0.6667 + 1.0000 + 0.6667 + 0.8000 + 0.0000 + 1.0000) / 10 = 8.1333 / 10 = 0.8133
```

### 格式说明
1. **前缀**: "平均准确率 = 所有问题准确率的平均值 = "
2. **准确率列表**: 用括号包围，用" + "连接，保留4位小数
3. **除法过程**: "/ 有效问题数 = 总和 / 有效问题数 = 最终结果"
4. **精度**: 所有数值保留4位小数

## ✅ 验证要点

### 功能验证
- [ ] 公式字符串格式正确
- [ ] 包含所有有效问题的准确率
- [ ] 计算过程完整（求和→除法→结果）
- [ ] 数值精度正确（4位小数）

### 边界情况验证
- [ ] 无有效问题时的处理
- [ ] 单个问题时的显示
- [ ] 所有问题准确率为0时的处理
- [ ] 所有问题准确率为1时的处理

### 兼容性验证
- [ ] 普通评测接口正常工作
- [ ] 端到端评测接口正常工作
- [ ] 其他字段不受影响
- [ ] 原有计算逻辑不变

## 🐛 可能的问题

### 1. 公式过长问题
如果问题数量很多，公式字符串可能会很长，需要考虑：
- 是否需要截断或分行显示
- 前端显示是否会有问题

### 2. 性能影响
字符串拼接操作可能对性能有轻微影响，但在正常评测规模下应该可以忽略。

### 3. 内存使用
StringBuilder的使用是合理的，不会造成内存问题。

## 📝 测试记录

### 测试环境
- 服务器: 开发环境
- 数据库: MySQL (10.81.9.154:3306/agent_factory)
- 知识库: 测试知识库

### 测试结果
- [ ] 普通评测接口测试通过
- [ ] 端到端评测接口测试通过
- [ ] 公式格式验证通过
- [ ] 计算结果验证通过

## 🎯 成功标准

1. **功能完整**: 两个评测接口都能正确显示详细公式
2. **格式正确**: 公式格式符合预期，易于阅读
3. **计算准确**: 显示的计算过程与实际计算结果一致
4. **性能稳定**: 修改不影响系统性能
5. **兼容性好**: 不影响其他功能的正常使用
