package com.chinaunicom.csf.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 智能体应用表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("agent_apps")
@Schema(description="智能体应用表")
public class AgentApps implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "智能体 ID，自增主键，唯一标识智能体应用")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @Schema(description = "创建智能体的用户 ID，关联用户表（csf_user.id）")
    @TableField("create_user")
    private Integer createUser;

    @Schema(description = "智能体发布状态：0=未发布，1=已发布（可被用户调用）")
    @TableField("is_publish")
    private Boolean isPublish;

    @Schema(description = "智能体名称，例如：客服小助手、代码生成器")
    @TableField("name")
    private String name;

    @Schema(description = "智能体功能描述，简要说明用途（如：处理客户咨询、生成代码片段）")
    @TableField("description")
    private String description;

    @Schema(description = "智能体头像 URL，用于前端展示（如：/static/avatars/agent_1.png）")
    @TableField("avatar_url")
    private String avatarUrl;

    @Schema(description = "智能体人物设定，定义性格、角色等（如：友好的客服，擅长解答产品问题）")
    @TableField("character_setting")
    private String characterSetting;

    @Schema(description = "用户首次交互时的欢迎语")
    @TableField("welcome_message")
    private String welcomeMessage;

    @Schema(description = "智能体创建时间，自动填充当前时间")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @Schema(description = "智能体信息更新时间，修改时自动更新")
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}
