package com.chinaunicom.ai.knowledge.controller;

import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.entity.AiModel;
import com.chinaunicom.ai.knowledge.entity.KnowledgeBase;
import com.chinaunicom.ai.knowledge.mapper.AiModelMapper;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeBaseMapper;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.service.impl.PythonEmbeddingService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import com.chinaunicom.csf.core.secure.utils.SecureUtil;
import com.chinaunicom.csf.core.tool.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调试控制器 - 用于排查召回接口问题
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/debug")
@Tag(name = "调试接口", description = "用于排查召回接口语义相关性问题")
public class DebugController {

    private final PythonEmbeddingService pythonEmbeddingService;
    private final IElasticSearchService elasticSearchService;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final AiModelMapper aiModelMapper;
    private final HybridSearchConfig hybridSearchConfig;

    /**
     * 测试Python向量化服务
     */
    @PostMapping("/test-vectorization")
    @Operation(summary = "测试Python向量化服务", description = "验证文本向量化是否正常工作")
    public R<Map<String, Object>> testVectorization(
            @RequestParam String queryText,
            @RequestParam String modelName) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始测试向量化，文本: {}, 模型: {}", queryText, modelName);
            
            long startTime = System.currentTimeMillis();
            float[] vector = pythonEmbeddingService.vectorizeText(queryText, modelName);
            long endTime = System.currentTimeMillis();
            
            result.put("success", true);
            result.put("queryText", queryText);
            result.put("modelName", modelName);
            result.put("vectorDimension", vector != null ? vector.length : 0);
            result.put("executionTime", endTime - startTime);
            result.put("vectorSample", vector != null && vector.length > 0 ? 
                java.util.Arrays.copyOf(vector, Math.min(10, vector.length)) : null);
            
            log.info("向量化测试完成，维度: {}, 耗时: {}ms", 
                vector != null ? vector.length : 0, endTime - startTime);
                
        } catch (Exception e) {
            log.error("向量化测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return R.data(result);
    }

    /**
     * 测试ES向量搜索
     */
    @PostMapping("/test-es-search")
    @Operation(summary = "测试ES向量搜索", description = "验证Elasticsearch向量搜索是否正常")
    public R<Map<String, Object>> testEsSearch(
            @RequestParam Long knowledgeBaseId,
            @RequestParam String queryText) {
        
        Map<String, Object> result = new HashMap<>();
        String tenantId = SecureUtil.getTenantId();
        if (tenantId == null) tenantId = "000000";
        
        try {
            // 1. 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                result.put("success", false);
                result.put("error", "知识库不存在");
                return R.data(result);
            }
            
            // 2. 获取模型信息
            AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
            if (aiModel == null) {
                result.put("success", false);
                result.put("error", "向量模型不存在");
                return R.data(result);
            }
            
            // 3. 向量化查询文本
            long vectorizeStart = System.currentTimeMillis();
            float[] queryVector = pythonEmbeddingService.vectorizeText(queryText, aiModel.getDisplayName());
            long vectorizeEnd = System.currentTimeMillis();
            
            if (queryVector == null || queryVector.length == 0) {
                result.put("success", false);
                result.put("error", "向量化失败");
                return R.data(result);
            }
            
            // 4. ES搜索
            long searchStart = System.currentTimeMillis();
            List<AgentSearchResultVO> searchResults = elasticSearchService.searchVector(
                    knowledgeBase.getIndexName(),
                    queryVector,
                    5,
                    knowledgeBaseId,
                    tenantId
            );
            long searchEnd = System.currentTimeMillis();
            
            // 5. 组装结果
            result.put("success", true);
            result.put("knowledgeBaseInfo", Map.of(
                "id", knowledgeBase.getId(),
                "name", knowledgeBase.getName(),
                "indexName", knowledgeBase.getIndexName()
            ));
            result.put("modelInfo", Map.of(
                "id", aiModel.getId(),
                "displayName", aiModel.getDisplayName(),
                "dims", aiModel.getDims()
            ));
            result.put("vectorInfo", Map.of(
                "dimension", queryVector.length,
                "vectorizeTime", vectorizeEnd - vectorizeStart,
                "sample", java.util.Arrays.copyOf(queryVector, Math.min(5, queryVector.length))
            ));
            result.put("searchInfo", Map.of(
                "resultCount", searchResults.size(),
                "searchTime", searchEnd - searchStart,
                "results", searchResults
            ));
            
            log.info("ES搜索测试完成，知识库: {}, 结果数: {}, 搜索耗时: {}ms", 
                knowledgeBaseId, searchResults.size(), searchEnd - searchStart);
                
        } catch (Exception e) {
            log.error("ES搜索测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return R.data(result);
    }

    /**
     * 获取知识库和模型配置信息
     */
    @GetMapping("/kb-model-info/{knowledgeBaseId}")
    @Operation(summary = "获取知识库模型配置", description = "查看知识库关联的模型配置信息")
    public R<Map<String, Object>> getKbModelInfo(@PathVariable Long knowledgeBaseId) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                result.put("success", false);
                result.put("error", "知识库不存在");
                return R.data(result);
            }
            
            AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
            
            result.put("success", true);
            result.put("knowledgeBase", knowledgeBase);
            result.put("aiModel", aiModel);
            
        } catch (Exception e) {
            log.error("获取知识库模型信息失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return R.data(result);
    }

    /**
     * 比较两个文本的向量相似度
     */
    @PostMapping("/compare-similarity")
    @Operation(summary = "比较文本相似度", description = "比较两个文本的向量相似度")
    public R<Map<String, Object>> compareSimilarity(
            @RequestParam String text1,
            @RequestParam String text2,
            @RequestParam String modelName) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 向量化两个文本
            float[] vector1 = pythonEmbeddingService.vectorizeText(text1, modelName);
            float[] vector2 = pythonEmbeddingService.vectorizeText(text2, modelName);
            
            if (vector1 == null || vector2 == null || vector1.length != vector2.length) {
                result.put("success", false);
                result.put("error", "向量化失败或维度不匹配");
                return R.data(result);
            }
            
            // 计算余弦相似度
            double similarity = calculateCosineSimilarity(vector1, vector2);
            
            result.put("success", true);
            result.put("text1", text1);
            result.put("text2", text2);
            result.put("modelName", modelName);
            result.put("vectorDimension", vector1.length);
            result.put("cosineSimilarity", similarity);
            
        } catch (Exception e) {
            log.error("相似度比较失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return R.data(result);
    }
    
    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(float[] vector1, float[] vector2) {
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 检查ES索引中的文档内容
     */
    @GetMapping("/check-es-documents/{knowledgeBaseId}")
    @Operation(summary = "检查ES索引文档", description = "查看ES索引中的文档内容和向量")
    public R<Map<String, Object>> checkEsDocuments(@PathVariable Long knowledgeBaseId) {
        Map<String, Object> result = new HashMap<>();

        try {
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                result.put("success", false);
                result.put("error", "知识库不存在");
                return R.data(result);
            }

            // 这里需要调用ES客户端直接查询索引内容
            // 由于当前代码结构限制，建议通过ES REST API直接查询
            result.put("success", true);
            result.put("indexName", knowledgeBase.getIndexName());
            result.put("suggestion", "请使用以下命令检查ES索引内容：");
            result.put("command", "curl -X GET \"http://************:9002/" + knowledgeBase.getIndexName() + "/_search?size=5&pretty\"");

        } catch (Exception e) {
            log.error("检查ES文档失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return R.data(result);
    }

    /**
     * 测试特定文档片段的相似度
     */
    @PostMapping("/test-fragment-similarity")
    @Operation(summary = "测试文档片段相似度", description = "测试查询文本与特定文档片段的相似度")
    public R<Map<String, Object>> testFragmentSimilarity(
            @RequestParam String queryText,
            @RequestParam String fragmentText,
            @RequestParam String modelName) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 向量化查询文本和文档片段
            float[] queryVector = pythonEmbeddingService.vectorizeText(queryText, modelName);
            float[] fragmentVector = pythonEmbeddingService.vectorizeText(fragmentText, modelName);

            if (queryVector == null || fragmentVector == null || queryVector.length != fragmentVector.length) {
                result.put("success", false);
                result.put("error", "向量化失败或维度不匹配");
                return R.data(result);
            }

            // 计算余弦相似度
            double similarity = calculateCosineSimilarity(queryVector, fragmentVector);

            result.put("success", true);
            result.put("queryText", queryText);
            result.put("fragmentText", fragmentText);
            result.put("modelName", modelName);
            result.put("cosineSimilarity", similarity);
            result.put("esScore", similarity + 1.0); // ES中使用的分数

            // 判断相似度等级
            String level;
            if (similarity > 0.8) {
                level = "高度相关";
            } else if (similarity > 0.6) {
                level = "中度相关";
            } else if (similarity > 0.4) {
                level = "低度相关";
            } else {
                level = "不相关";
            }
            result.put("similarityLevel", level);

        } catch (Exception e) {
            log.error("测试文档片段相似度失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return R.data(result);
    }

    /**
     * 测试混合检索分数归一化效果
     */
    @PostMapping("/test-hybrid-score-normalization")
    @Operation(summary = "测试混合检索分数归一化", description = "验证关键词分数和向量分数是否在相同范围内")
    public R<Map<String, Object>> testHybridScoreNormalization(
            @RequestParam String queryText,
            @RequestParam Long knowledgeBaseId) {

        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试混合检索分数归一化，查询: {}, 知识库ID: {}", queryText, knowledgeBaseId);

            String tenantId = SecureUtil.getTenantId();
            if (tenantId == null || tenantId.trim().isEmpty()) {
                tenantId = "000000";
            }

            // 1. 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase == null) {
                result.put("success", false);
                result.put("error", "知识库不存在: " + knowledgeBaseId);
                return R.data(result);
            }

            // 2. 获取向量模型信息
            AiModel aiModel = aiModelMapper.selectById(knowledgeBase.getVecModel());
            if (aiModel == null) {
                result.put("success", false);
                result.put("error", "向量模型不存在: " + knowledgeBase.getVecModel());
                return R.data(result);
            }

            // 3. 向量化查询文本
            float[] queryVector = pythonEmbeddingService.vectorizeText(queryText, aiModel.getDisplayName());
            if (queryVector == null) {
                result.put("success", false);
                result.put("error", "文本向量化失败");
                return R.data(result);
            }

            // 4. 执行混合检索
            List<AgentSearchResultVO> hybridResults = elasticSearchService.searchHybrid(
                    knowledgeBase.getIndexName(),
                    queryText,
                    queryVector,
                    5, // topK
                    knowledgeBaseId,
                    tenantId,
                    hybridSearchConfig
            );

            // 5. 执行纯向量检索作为对比
            List<AgentSearchResultVO> vectorResults = elasticSearchService.searchVector(
                    knowledgeBase.getIndexName(),
                    queryVector,
                    5, // topK
                    knowledgeBaseId,
                    tenantId
            );

            // 6. 分析分数分布
            Map<String, Object> scoreAnalysis = analyzeScoreDistribution(hybridResults, vectorResults);

            // 7. 构建返回结果
            result.put("success", true);
            result.put("queryText", queryText);
            result.put("knowledgeBaseId", knowledgeBaseId);
            result.put("knowledgeBaseName", knowledgeBase.getName());
            result.put("vectorModel", aiModel.getDisplayName());
            result.put("hybridSearchConfig", Map.of(
                    "enabled", hybridSearchConfig.getEnabled(),
                    "keywordWeight", hybridSearchConfig.getKeywordWeight(),
                    "vectorWeight", hybridSearchConfig.getVectorWeight(),
                    "minimumShouldMatch", hybridSearchConfig.getMinimumShouldMatch()
            ));
            result.put("hybridResults", hybridResults);
            result.put("vectorResults", vectorResults);
            result.put("scoreAnalysis", scoreAnalysis);

            log.info("混合检索分数归一化测试完成，混合检索结果数: {}, 纯向量检索结果数: {}",
                    hybridResults.size(), vectorResults.size());

        } catch (Exception e) {
            log.error("测试混合检索分数归一化失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return R.data(result);
    }

    /**
     * 分析分数分布
     */
    private Map<String, Object> analyzeScoreDistribution(List<AgentSearchResultVO> hybridResults,
                                                        List<AgentSearchResultVO> vectorResults) {
        Map<String, Object> analysis = new HashMap<>();

        // 分析混合检索分数
        if (!hybridResults.isEmpty()) {
            double maxHybridScore = hybridResults.stream().mapToDouble(r -> r.getScore()).max().orElse(0.0);
            double minHybridScore = hybridResults.stream().mapToDouble(r -> r.getScore()).min().orElse(0.0);
            double avgHybridScore = hybridResults.stream().mapToDouble(r -> r.getScore()).average().orElse(0.0);

            analysis.put("hybridScores", Map.of(
                    "max", maxHybridScore,
                    "min", minHybridScore,
                    "avg", avgHybridScore,
                    "range", maxHybridScore - minHybridScore,
                    "count", hybridResults.size()
            ));
        }

        // 分析纯向量检索分数
        if (!vectorResults.isEmpty()) {
            double maxVectorScore = vectorResults.stream().mapToDouble(r -> r.getScore()).max().orElse(0.0);
            double minVectorScore = vectorResults.stream().mapToDouble(r -> r.getScore()).min().orElse(0.0);
            double avgVectorScore = vectorResults.stream().mapToDouble(r -> r.getScore()).average().orElse(0.0);

            analysis.put("vectorScores", Map.of(
                    "max", maxVectorScore,
                    "min", minVectorScore,
                    "avg", avgVectorScore,
                    "range", maxVectorScore - minVectorScore,
                    "count", vectorResults.size()
            ));
        }

        // 归一化效果评估
        analysis.put("normalizationAssessment", Map.of(
                "description", "分数归一化效果评估",
                "expectedRange", "0-10（关键词和向量分数均应在此范围内）",
                "note", "混合检索分数应该反映关键词权重和向量权重的正确平衡"
        ));

        return analysis;
    }
}
