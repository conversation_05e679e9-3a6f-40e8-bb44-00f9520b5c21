package com.chinaunicom.ai.knowledge.service.impl;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.ScriptScoreQuery;
import co.elastic.clients.elasticsearch.core.DeleteByQueryRequest;
import co.elastic.clients.elasticsearch.core.DeleteByQueryResponse;
import co.elastic.clients.elasticsearch.indices.CreateIndexRequest;
import co.elastic.clients.elasticsearch.indices.DeleteIndexRequest;
import co.elastic.clients.elasticsearch.indices.ExistsRequest;
import co.elastic.clients.json.JsonData;
import com.chinaunicom.ai.knowledge.config.HybridSearchConfig;
import com.chinaunicom.ai.knowledge.config.HybridSearchFilterConfig;
import com.chinaunicom.ai.knowledge.config.VectorSearchConfig;
import com.chinaunicom.ai.knowledge.service.IElasticSearchService;
import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Elasticsearch操作服务
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "elasticsearch.enabled", havingValue = "true", matchIfMissing = true) // 默认启用
public class ElasticSearchServiceImpl implements IElasticSearchService {

    // 注入新的ElasticsearchClient
    @Autowired
    private ElasticsearchClient elasticsearchClient;

    // @Qualifier("elasticsearchTemplate")
    // @Autowired
    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate; // 用于搜索操作

    @Resource
    private VectorSearchConfig vectorSearchConfig;

    @Autowired
    private HybridSearchFilterConfig hybridSearchFilterConfig;

    /**
     * 创建ElasticSearch索引
     * @param indexName 索引名称
     * @param dims 向量维度
     * @return 是否创建成功
     */
    @Override
    public boolean createIndex(String indexName, Integer dims) {
        try {
            // 检查索引是否存在
            ExistsRequest existsRequest = ExistsRequest.of(e -> e.index(indexName));
            boolean exists = elasticsearchClient.indices().exists(existsRequest).value();
            if (exists) {
                log.warn("Elasticsearch索引 {} 已存在，无需重复创建。", indexName);
                return true;
            }

            // 定义索引设置和映射
            // 使用新的Java API Client的构建器模式来定义映射
            CreateIndexRequest createIndexRequest = CreateIndexRequest.of(c -> c
                    .index(indexName)
                    .settings(s -> s
                                    .numberOfShards("1") // 设置分片数量
                                    .numberOfReplicas("0") // 设置副本数量
                            // 根据需要可以添加其他设置，例如：
                            // .analysis(a -> a // 示例：如果需要自定义分析器
                            //     .analyzer("my_analyzer", an -> an.custom(ca -> ca.tokenizer("standard")))
                            // )
                    )
                    .mappings(m -> m
                            .properties("node_id", p -> p.keyword(k -> k)) // node_id 字段
                            .properties("ref_doc_id", p -> p.keyword(k -> k)) // ref_doc_id 字段
                            .properties("node_info", p -> p.object(o -> o // node_info 对象字段
                                    .enabled(true) // "enabled": True
                                    .properties("start", np -> np.keyword(k -> k)) // node_info.start
                                    .properties("end", np -> np.keyword(k -> k)) // node_info.end
                            ))
                            .properties("next_node", p -> p.keyword(k -> k)) // next_node 字段
                            .properties("text", p -> p.text(t -> t
                            .analyzer("ik_max_word")      // 索引时使用ik_max_word分词器
                            .searchAnalyzer("ik_max_word") // 查询时使用ik_max_word分词器
                    )) // text 字段
                            .properties("embedding", p -> p.denseVector(dv -> dv // embedding 向量字段
                                    .dims(dims) // 使用方法参数中的 dims，而不是硬编码 768
                                    .index(true) // "index": True
                                    // .similarity(s_v -> s_v.cosine()) // "similarity": "cosine"
                            ))
                            .properties("metadata", p -> p.object(o -> o // metadata 对象字段
                                    .enabled(true) // "enabled": True, 动态映射 metadata 中的字段
                                    .properties("mysqlDocId", mp -> mp.keyword(k -> k)) // metadata.mysqlDocId
                                    .properties("minioId", mp -> mp.keyword(k -> k)) // metadata.minioId
                                    .properties("fileName", mp -> mp.keyword(k -> k)) // metadata.fileName
                                    .properties("indexName", mp -> mp.keyword(k -> k)) // metadata.indexName
                                    .properties("chunk_size", mp -> mp.integer(i -> i)) // metadata.chunk_size
                                    .properties("is_truncated", mp -> mp.boolean_(b -> b)) // metadata.is_truncated
                            ))
                            .properties("created_at", p -> p.date(d -> d)) // created_at 日期字段
                            .properties("updated_at", p -> p.date(d -> d)) // updated_at 日期字段
                    )
            );

            elasticsearchClient.indices().create(createIndexRequest);
            log.info("Elasticsearch索引 {} 创建成功。", indexName);
            return true;
        } catch (IOException e) {
            log.error("创建Elasticsearch索引 {} 失败。", indexName, e);
            return false;
        }
    }

    /**
     * 删除ElasticSearch索引
     * @param indexName 索引名称
     * @return 是否删除成功
     */
    @Override
    public boolean deleteIndex(String indexName) {
        try {
            // 检查索引是否存在
            ExistsRequest existsRequest = ExistsRequest.of(e -> e.index(indexName));
            boolean exists = elasticsearchClient.indices().exists(existsRequest).value();
            if (!exists) {
                log.warn("Elasticsearch索引 {} 不存在，无需删除。", indexName);
                return true;
            }

            DeleteIndexRequest deleteIndexRequest = DeleteIndexRequest.of(d -> d.index(indexName));
            elasticsearchClient.indices().delete(deleteIndexRequest);
            log.info("Elasticsearch索引 {} 删除成功。", indexName);
            return true;
        } catch (IOException e) {
            log.error("删除Elasticsearch索引 {} 失败。", indexName, e);
            return false;
        }
    }

    /**
     * 根据向量在Elasticsearch中进行相似度搜索
     * @param indexName 索引名称
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @param knowledgeBaseId 知识库ID (用于过滤，可选，现在从 indexName 推断)
     * @param tenantId 租户ID (用于过滤，现在从 indexName 推断)
     * @return 匹配的文档片段列表
     */
    @Override
    public List<AgentSearchResultVO> searchVector(String indexName, float[] queryVector, int topK, Long knowledgeBaseId, String tenantId) {
        List<AgentSearchResultVO> results = new ArrayList<>();
        try {
            // 构建 script_score 查询的核心部分
            ScriptScoreQuery scriptScoreQuery = ScriptScoreQuery.of(ssq -> ssq
                    .query(q -> q.matchAll(m -> m)) // 内部查询使用 match_all，匹配所有文档
                    .script(s -> s // s 是 Script.Builder
                            .inline(is -> is // is 是 InlineScript.Builder
                                    .source("cosineSimilarity(params.queryVector, 'embedding') + 1.0") // 在 InlineScript.Builder 上设置脚本源代码
                                    .lang("painless") // 通常 inline 脚本需要指定语言，假定为 'painless'
                                    .params("queryVector", JsonData.of(queryVector)) // 在 InlineScript.Builder 上设置参数
                            )
                    )
            );

            // 构建 Elasticsearch Java API Client 的 Query DSL 对象
            // 直接使用 Query.of() 与已构建的 ScriptScoreQuery 对象
            co.elastic.clients.elasticsearch._types.query_dsl.Query esQuery = co.elastic.clients.elasticsearch._types.query_dsl.Query.of(q -> q.scriptScore(scriptScoreQuery));

            // 构建 Spring Data Elasticsearch 的 NativeQuery，并设置 topK
            Query query = NativeQuery.builder()
                    .withQuery(esQuery) // 设置 Elasticsearch Query DSL 对象
                    .withMaxResults(topK) // 设置返回结果数量
                    .build();

            // 执行搜索，返回 Map 类型的结果
            SearchHits<Map> searchHits = elasticsearchTemplate.search(query, Map.class, IndexCoordinates.of(indexName));

            log.info("🔍 ES搜索完成，索引: {}, 返回结果数: {}, topK: {}", indexName, searchHits.getTotalHits(), topK);

            for (SearchHit<Map> hit : searchHits) {
                Map<String, Object> source = hit.getContent();

                // 相似度阈值过滤
                // ES返回的分数是 cosineSimilarity + 1.0，所以需要减1得到真实的余弦相似度
                double realSimilarity = hit.getScore() - 1.0;
                double similarityThreshold = vectorSearchConfig.getSimilarityThreshold();

                // 相似度阈值过滤（如果启用）
                if (vectorSearchConfig.isEnableSimilarityFilter() && realSimilarity < similarityThreshold) {
                    String fragmentText = source.get("text") != null ? String.valueOf(source.get("text")) : "";
                    if (vectorSearchConfig.isEnableDebugLog()) {
                        log.debug("过滤低相似度结果，相似度: {:.3f}, 阈值: {:.3f}, 内容: {}",
                            realSimilarity, similarityThreshold,
                            fragmentText.length() > 50 ? fragmentText.substring(0, 50) : fragmentText);
                    }
                    continue; // 跳过低相似度的结果
                }

                AgentSearchResultVO vo = new AgentSearchResultVO();

                // 从 indexName 中提取 knowledgeBaseId
                // 假设 indexName 格式为 "kb-<tenantId>-<knowledgeBaseId>"
                String[] parts = indexName.split("-");
                if (parts.length >= 3) { // 至少三部分: "kb", "tenantId", "knowledgeBaseId"
                    try {
                        vo.setKnowledgeBaseId(Long.valueOf(parts[parts.length - 1]));
                    } catch (NumberFormatException e) {
                        log.warn("无法从索引名称 {} 中提取 knowledgeBaseId。设置为 null 或默认值。", indexName);
                        vo.setKnowledgeBaseId(null); // 或者设置为默认值
                    }
                } else {
                    log.warn("索引名称 {} 格式不符合预期，无法提取 knowledgeBaseId。", indexName);
                    vo.setKnowledgeBaseId(null); // 或者设置为默认值
                }

                // 从 metadata 中获取 documentId (mysqlDocId) 和 fileName
                if (source.get("metadata") instanceof Map) {
                    Map<String, Object> metadata = (Map<String, Object>) source.get("metadata");
                    Object docIdObj = metadata.get("mysqlDocId");
                    if (docIdObj != null) {
                        vo.setDocumentId(Long.valueOf(String.valueOf(docIdObj)));
                    }
                    Object fileNameObj = metadata.get("fileName");
                    if (fileNameObj != null) {
                        vo.setDocumentName(String.valueOf(fileNameObj));
                    }
                }

                Object textObj = source.get("text");
                if (textObj != null) {
                    vo.setFragmentContent(String.valueOf(textObj)); // 文本字段为 'text'
                }
                vo.setScore(hit.getScore());

                // 记录实际相似度到日志（如果启用调试日志）
                if (vectorSearchConfig.isEnableDebugLog()) {
                    log.debug("✅ 召回结果 - 相似度: {}, 分数: {}, 内容预览: {}",
                        realSimilarity, hit.getScore(),
                        vo.getFragmentContent() != null ? vo.getFragmentContent().substring(0, Math.min(100, vo.getFragmentContent().length())) : "无内容");
                }

                results.add(vo);
            }
        } catch (Exception e) {
            log.error("Elasticsearch向量搜索失败，索引: {}。错误: {}", indexName, e.getMessage(), e);
        }
        return results;
    }







    /**
     * 根据文档ID（mysqlDocId）删除Elasticsearch索引中对应文档的所有片段。
     * @param indexName Elasticsearch索引名称
     * @param mysqlDocId MySQL中的文档ID (对应ES中的 metadata.mysqlDocId)
     * @return 是否删除成功
     */
    @Override
    public boolean deleteDocumentByMysqlDocId(String indexName, Long mysqlDocId) {
        try {
            // 检查索引是否存在
            ExistsRequest existsRequest = ExistsRequest.of(e -> e.index(indexName));
            boolean exists = elasticsearchClient.indices().exists(existsRequest).value();
            if (!exists) {
                log.warn("Elasticsearch索引 {} 不存在，跳过删除文档片段操作，mysqlDocId: {}", indexName, mysqlDocId);
                return true; // 索引不存在时认为删除成功，因为目标已经不存在了
            }

            // 构建 delete_by_query 请求
            // 目标是删除所有在指定索引中，且 metadata.mysqlDocId 匹配给定 mysqlDocId 的文档片段
            DeleteByQueryRequest deleteRequest = DeleteByQueryRequest.of(d -> d
                    .index(indexName)
                    .query(q -> q
                            .term(t -> t // 使用 term 查询来精确匹配
                                    .field("metadata.mysqlDocId") // 匹配 metadata 对象下的 mysqlDocId 字段
                                    .value(String.valueOf(mysqlDocId)) // mysqlDocId 在 ES 中是 keyword，所以转为字符串
                            )
                    )
            );

            DeleteByQueryResponse response = elasticsearchClient.deleteByQuery(deleteRequest);

            if (response.failures().isEmpty()) {
                log.info("成功从Elasticsearch索引 {} 删除文档片段，mysqlDocId: {}，删除数量: {}",
                        indexName, mysqlDocId, response.deleted());
                return true;
            } else {
                log.error("从Elasticsearch索引 {} 删除文档片段失败，mysqlDocId: {}，失败详情: {}",
                        indexName, mysqlDocId, response.failures());
                return false;
            }
        } catch (IOException e) {
            log.error("从Elasticsearch索引 {} 删除文档片段时发生IO错误，mysqlDocId: {}", indexName, mysqlDocId, e);
            return false;
        } catch (Exception e) {
            log.error("从Elasticsearch索引 {} 删除文档片段时发生未知错误，mysqlDocId: {}", indexName, mysqlDocId, e);
            return false;
        }
    }

    /**
     * 统计Elasticsearch索引中的文档片段总数
     * @param indexName 索引名称
     * @return 片段总数，如果索引不存在或查询失败返回0
     */
    @Override
    public long countDocuments(String indexName) {
        try {
            // 检查索引是否存在
            ExistsRequest existsRequest = ExistsRequest.of(e -> e.index(indexName));
            boolean exists = elasticsearchClient.indices().exists(existsRequest).value();
            if (!exists) {
                log.warn("Elasticsearch索引 {} 不存在，返回片段数量为0", indexName);
                return 0L;
            }

            // 使用count API统计文档数量
            co.elastic.clients.elasticsearch.core.CountRequest countRequest =
                co.elastic.clients.elasticsearch.core.CountRequest.of(c -> c
                    .index(indexName)
                    .query(q -> q.matchAll(m -> m)) // 匹配所有文档
                );

            co.elastic.clients.elasticsearch.core.CountResponse countResponse =
                elasticsearchClient.count(countRequest);

            long count = countResponse.count();
            log.debug("Elasticsearch索引 {} 的文档片段总数: {}", indexName, count);
            return count;

        } catch (IOException e) {
            log.error("统计Elasticsearch索引 {} 的文档片段数量失败", indexName, e);
            return 0L;
        } catch (Exception e) {
            log.error("统计Elasticsearch索引 {} 的文档片段数量时发生未知错误", indexName, e);
            return 0L;
        }
    }

    /**
     * 混合检索实现：结合关键词匹配和向量相似度搜索
     * 使用bool查询组合关键词查询和向量查询，通过权重控制两种检索方式的重要性
     */
    @Override
    public List<AgentSearchResultVO> searchHybrid(String indexName, String queryText, float[] queryVector,
                                                 int topK, Long knowledgeBaseId, String tenantId,
                                                 HybridSearchConfig config) {
        long startTime = System.currentTimeMillis();
        List<AgentSearchResultVO> results = new ArrayList<>();

        // 参数验证
        if (!StringUtils.hasText(indexName) || !StringUtils.hasText(queryText) ||
            queryVector == null || queryVector.length == 0 || config == null) {
            log.warn("混合检索参数无效，返回空结果");
            return results;
        }

        // 配置验证和标准化
        if (!config.isValid()) {
            log.warn("混合检索配置无效，降级到纯向量检索");
            return searchVector(indexName, queryVector, topK, knowledgeBaseId, tenantId);
        }
        config.normalize();

        // 轻量化：移除缓存功能，直接执行检索

        try {
            log.info("🔍 开始混合检索 - 索引: {}, 查询: '{}', 关键词权重: {}, 向量权重: {} [分数已归一化到0-10范围]",
                    indexName, queryText, config.getKeywordWeight(), config.getVectorWeight());

            // 构建混合查询（包含分数范围归一化）
            co.elastic.clients.elasticsearch._types.query_dsl.Query hybridQuery = buildHybridQuery(
                    queryText, queryVector, config);

            // 构建最终查询
            Query finalQuery = NativeQuery.builder()
                    .withQuery(hybridQuery)
                    .withMaxResults(topK)
                    .build();

            // 执行查询
            @SuppressWarnings("rawtypes")
            SearchHits<Map> searchHits = elasticsearchTemplate.search(finalQuery, Map.class,
                    IndexCoordinates.of(indexName));

            log.info("🔍 混合检索完成，索引: {}, 返回结果数: {}, topK: {} [关键词+向量分数均在0-10范围]",
                    indexName, searchHits.getTotalHits(), topK);

            // 处理查询结果（添加分数阈值过滤）
            int filteredCount = 0; // 被过滤的结果数量

            for (@SuppressWarnings("rawtypes") SearchHit<Map> hit : searchHits) {
                double hybridScore = hit.getScore();

                // 混合检索分数阈值过滤（如果启用）
                if (hybridSearchFilterConfig.isEnabled() && hybridSearchFilterConfig.isValid()
                        && hybridScore < hybridSearchFilterConfig.getMinimumScore()) {

                    filteredCount++;

                    // 调试日志记录
                    if (hybridSearchFilterConfig.isEnableDebugLog()) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> source = hit.getContent();
                        String fragmentText = source.get("text") != null ? String.valueOf(source.get("text")) : "";
                        log.debug("混合检索过滤低分数结果 - 分数: {:.2f}, 阈值: {:.2f}, 内容: {}",
                                hybridScore, hybridSearchFilterConfig.getMinimumScore(),
                                fragmentText.length() > 50 ? fragmentText.substring(0, 50) + "..." : fragmentText);
                    }

                    continue; // 跳过低分数的结果
                }

                // 转换为结果对象
                AgentSearchResultVO vo = convertHitToSearchResult(hit, knowledgeBaseId);
                if (vo != null) {
                    results.add(vo);
                }
            }

            // 记录过滤统计信息
            if (filteredCount > 0) {
                log.info("混合检索分数过滤完成 - 原始结果数: {}, 过滤数: {}, 最终结果数: {}, 阈值: {:.2f}",
                        searchHits.getTotalHits(), filteredCount, results.size(), hybridSearchFilterConfig.getMinimumScore());
            } else {
                log.debug("混合检索无结果被过滤 - 结果数: {}, 阈值: {:.2f}",
                        results.size(), hybridSearchFilterConfig.getMinimumScore());
            }

            log.info("混合检索成功 - 查询: '{}', 有效结果数: {}, 耗时: {}ms",
                    queryText, results.size(), System.currentTimeMillis() - startTime);

            // 轻量化：移除缓存存储和性能监控，只保留基础日志
            return results;

        } catch (Exception e) {
            log.error("混合检索失败，降级到纯向量检索 - 索引: {}, 查询: '{}', 错误: {}",
                    indexName, queryText, e.getMessage(), e);
            // 降级到纯向量检索
            return searchVector(indexName, queryVector, topK, knowledgeBaseId, tenantId);
        }
    }

    /**
     * 构建混合查询：组合关键词查询和向量查询
     *
     * 分数范围归一化说明：
     * - 关键词查询分数范围：0-10+（基于TF-IDF）
     * - 向量查询分数范围：0-2（余弦相似度-1到1，加1后变为0-2）
     * - 归一化策略：将向量分数乘以5，使其范围变为0-10，与关键词分数保持一致
     */
    private co.elastic.clients.elasticsearch._types.query_dsl.Query buildHybridQuery(
            String queryText, float[] queryVector, HybridSearchConfig config) {

        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        // 1. 关键词查询部分（分数范围：0-10+）
        if (config.getKeywordWeight() > 0) {
            MatchQuery keywordQuery = MatchQuery.of(m -> m
                    .field("text")
                    .query(queryText)
                    .analyzer(config.getAnalyzer())
                    .minimumShouldMatch(config.getMinimumShouldMatch())  // 使用固定配置
                    .boost((float) config.getKeywordWeight().doubleValue())
            );

            boolQueryBuilder.should(s -> s.match(keywordQuery));

            log.debug("关键词查询 - 文本: '{}', minimumShouldMatch: {}, 权重: {}, 分数范围: 0-10+",
                    queryText, config.getMinimumShouldMatch(), config.getKeywordWeight());
        }

        // 2. 向量查询部分（分数范围归一化：0-2 → 0-10）
        if (config.getVectorWeight() > 0) {
            ScriptScoreQuery vectorQuery = ScriptScoreQuery.of(ssq -> ssq
                    .query(q -> q.matchAll(m -> m))
                    .script(s -> s.inline(is -> is
                            // 分数归一化：(cosineSimilarity + 1.0) * 5.0
                            // 原始范围：-1到1 → 加1后：0到2 → 乘5后：0到10
                            .source("(cosineSimilarity(params.queryVector, 'embedding') + 1.0) * 5.0")
                            .lang("painless")
                            .params("queryVector", JsonData.of(queryVector))
                    ))
                    .boost((float) config.getVectorWeight().doubleValue())
            );

            boolQueryBuilder.should(s -> s.scriptScore(vectorQuery));

            log.debug("向量查询 - 向量维度: {}, 权重: {}, 分数范围: 0-10（归一化后）",
                    queryVector.length, config.getVectorWeight());
        }

        // 设置至少匹配一个should子句
        boolQueryBuilder.minimumShouldMatch("1");

        return co.elastic.clients.elasticsearch._types.query_dsl.Query.of(q -> q.bool(boolQueryBuilder.build()));
    }

    /**
     * 将SearchHit转换为AgentSearchResultVO
     */
    @SuppressWarnings("rawtypes")
    private AgentSearchResultVO convertHitToSearchResult(SearchHit<Map> hit, Long knowledgeBaseId) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> source = hit.getContent();

            AgentSearchResultVO vo = new AgentSearchResultVO();
            vo.setKnowledgeBaseId(knowledgeBaseId);
            vo.setScore(hit.getScore());

            // 设置文档内容
            if (source.get("text") != null) {
                vo.setFragmentContent(String.valueOf(source.get("text")));
            }

            // 从 metadata 中获取文档信息
            if (source.get("metadata") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> metadata = (Map<String, Object>) source.get("metadata");

                // 设置文档ID
                Object docIdObj = metadata.get("mysqlDocId");
                if (docIdObj != null) {
                    vo.setDocumentId(Long.valueOf(String.valueOf(docIdObj)));
                }

                // 设置文档名称
                Object fileNameObj = metadata.get("fileName");
                if (fileNameObj != null) {
                    vo.setDocumentName(String.valueOf(fileNameObj));
                }
            }

            return vo;

        } catch (Exception e) {
            log.warn("转换搜索结果失败: {}", e.getMessage());
            return null;
        }
    }
}