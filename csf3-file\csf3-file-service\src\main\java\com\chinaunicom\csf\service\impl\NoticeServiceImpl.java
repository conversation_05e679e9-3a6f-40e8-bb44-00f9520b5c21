package com.chinaunicom.csf.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.mapper.NoticeMapper;
import com.chinaunicom.csf.service.NoticeService;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.entity.Notice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class NoticeServiceImpl extends BaseServiceImpl<NoticeMapper, Notice> implements NoticeService {

	private NoticeMapper mapper;

	@Autowired
	public NoticeServiceImpl(NoticeMapper mapper) {
		this.mapper = mapper;
	}

	@Override
	public IPage<Notice> selectNoticePage(IPage<Notice> page, Notice notice) {
		return page.setRecords(baseMapper.selectNoticePage(page, notice));
	}

	@Override
	public List<Notice> top(Integer number) {
		return mapper.topList(number);
	}

}
