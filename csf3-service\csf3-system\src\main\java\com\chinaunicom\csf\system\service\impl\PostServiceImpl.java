
package com.chinaunicom.csf.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.tool.utils.BeanUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.system.entity.Post;
import com.chinaunicom.csf.system.entity.Tenant;
import com.chinaunicom.csf.system.mapper.PostMapper;
import com.chinaunicom.csf.system.mapper.TenantMapper;
import com.chinaunicom.csf.system.service.IPostService;
import com.chinaunicom.csf.system.vo.PostVO;
import com.chinaunicom.csf.system.wrapper.PostWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 岗位表 服务实现类
 *
 */
@Service
public class PostServiceImpl extends BaseServiceImpl<PostMapper, Post> implements IPostService {

	@Resource
	private TenantMapper tenantMapper;

	@Override
	public IPage<PostVO> selectPostPage(IPage<PostVO> page, PostVO post) {
		return page.setRecords(baseMapper.selectPostPage(page, post));
	}

	@Override
	public String getPostIds(String tenantId, String postNames) {
		List<Post> postList = baseMapper.selectList(Wrappers.<Post>query().lambda().eq(Post::getTenantId, tenantId).in(Post::getPostName, Func.toStrList(postNames)));
		if (postList != null && postList.size() > 0) {
			return postList.stream().map(post -> Func.toStr(post.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<String> getPostNames(String postIds) {
		return baseMapper.getPostNames(Func.toLongArray(postIds));
	}

	@Override
	public IPage<PostVO> getList(Post post, Query query) {
		IPage<Post> pages = this.page(Condition.getPage(query), Condition.getQueryWrapper(post));

		List<Tenant> tenants = tenantMapper.selectList(new QueryWrapper<>());
		Map<String, Tenant> tenantMap = new HashMap<>();
		for (Tenant tenant: tenants) {
			tenantMap.put(tenant.getTenantId(),tenant);
		}

		IPage<PostVO> postVOIPage = PostWrapper.build().pageVO(pages);
		List<PostVO> userVOList = new ArrayList<>();
		for (PostVO postVO: postVOIPage.getRecords()) {
			Tenant tenant = tenantMap.get(postVO.getTenantId());
			if(null != tenant) {
				postVO.setTenantName(tenant.getTenantName());
			}
			PostVO curVO = new PostVO();
			BeanUtil.copy(postVO, curVO);
			userVOList.add(curVO);
		}
		postVOIPage.setRecords(userVOList);

		return postVOIPage;
	}

	@Override
	public PostVO getDetail(Post post) {
		Post detail = this.getOne(Condition.getQueryWrapper(post));
		PostVO postVO = PostWrapper.build().entityVO(detail);
		LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(Tenant::getTenantId, postVO.getTenantId());
		Tenant tenant = tenantMapper.selectOne(queryWrapper);
		postVO.setTenantName(tenant.getTenantName());

		return postVO;
	}

}
