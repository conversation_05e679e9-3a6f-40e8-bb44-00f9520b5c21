
package com.chinaunicom.csf.system.wrapper;

import com.chinaunicom.csf.common.constant.CommonConstant;
import com.chinaunicom.csf.core.mp.support.BaseEntityWrapper;
import com.chinaunicom.csf.core.tool.node.ForestNodeMerger;
import com.chinaunicom.csf.core.tool.node.INode;
import com.chinaunicom.csf.core.tool.utils.BeanUtil;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.core.tool.utils.SpringUtil;
import com.chinaunicom.csf.system.entity.Dept;
import com.chinaunicom.csf.system.service.IDeptService;
import com.chinaunicom.csf.system.vo.DeptVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 */
public class DeptWrapper extends BaseEntityWrapper<Dept, DeptVO> {

	private static IDeptService deptService;

	static {
		deptService = SpringUtil.getBean(IDeptService.class);
	}

	public static DeptWrapper build() {
		return new DeptWrapper();
	}

	@Override
	public DeptVO entityVO(Dept dept) {
		DeptVO deptVO = BeanUtil.copy(dept, DeptVO.class);
		if (Func.equals(dept.getParentId(), CommonConstant.TOP_PARENT_ID)) {
			deptVO.setParentName(CommonConstant.TOP_PARENT_NAME);
		} else {
			Dept parent = deptService.getById(dept.getParentId());
			deptVO.setParentName(parent.getDeptName());
		}
		return deptVO;
	}

	public List<INode> listNodeVO(List<Dept> list) {
		List<INode> collect = list.stream().map(dept -> BeanUtil.copy(dept, DeptVO.class)).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
