
package com.chinaunicom.csf.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 生产者启动器
 *
 */
@SpringBootApplication
@Slf4j
@RestController
@RequestMapping("producer")
public class ProducerApplication {

	public static void main(String[] args) {
		SpringApplication.run(ProducerApplication.class, args);
	}


	@Autowired
	private StreamBridge streamBridge;

	@GetMapping("/send/{msg}")
	public void send(@PathVariable("msg") String msg) {
		log.info("使用streamBridge发送消息至通道csfMsgTest-out-0: {}", msg);
		streamBridge.send("csfMsgTest-out-0", msg);

	}

}
