package com.chinaunicom.csf.agent.controller;


import com.chinaunicom.csf.agent.entity.AgentLlms;
import com.chinaunicom.csf.agent.service.IAgentLlmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 存储大模型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@RestController
@RequestMapping("/agent-llms")
public class AgentLlmsController {
    @Autowired
    private IAgentLlmsService agentLlmsService;

    @GetMapping("/list")
    public List<AgentLlms> list() {
        return agentLlmsService.list();
    }

    @GetMapping("/{id}")
    public AgentLlms getById(@PathVariable Integer id) {
        return agentLlmsService.getById(id);
    }

    @PostMapping("/add")
    public boolean add(@RequestBody AgentLlms agentLlms) {
        return agentLlmsService.save(agentLlms);
    }

    @PutMapping("/update")
    public boolean update(@RequestBody AgentLlms agentLlms) {
        return agentLlmsService.updateById(agentLlms);
    }

    @DeleteMapping("/delete/{id}")
    public boolean delete(@PathVariable Integer id) {
        return agentLlmsService.removeById(id);
    }
}
