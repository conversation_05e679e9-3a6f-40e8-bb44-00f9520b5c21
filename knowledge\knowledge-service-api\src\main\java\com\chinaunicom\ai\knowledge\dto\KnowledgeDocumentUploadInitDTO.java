package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 文档上传初始化请求DTO
 */
@Data
@Schema(description = "文档上传初始化请求DTO")
public class KnowledgeDocumentUploadInitDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long knowledgeBaseId;

    @NotBlank(message = "文件名称不能为空")
    @Schema(description = "文件名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "my_document.pdf")
    private String fileName;

    @NotBlank(message = "文件类型不能为空")
    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "pdf")
    private String fileType;

    @NotNull(message = "文件大小不能为空")
    @Schema(description = "文件大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "102400")
    private Long fileSize;
}