package com.chinaunicom.csf.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 初始化分片上传响应DTO
 */
@Data
public class InitMultipartUploadResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID (32位不含横线的UUID)
     */
    private String fileId;

    /**
     * MinIO 中的对象名
     */
    private String objectName;

    /**
     * 存储的桶名
     */
    private String bucketName;

    /**
     * Upload ID，用于后续分片上传
     */
    private String uploadId;
} 