<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chinaunicom.csf</groupId>
        <artifactId>csf3</artifactId>
        <version>1.8.0-SNAPSHOT</version>
    </parent>

    <artifactId>csf3-service</artifactId>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <description>csf 微服务集合</description>

    <modules>
        <module>csf3-desk</module>
        <module>csf3-log</module>
        <module>csf3-system</module>
        <module>csf3-user</module>
        <module>csf3-demo</module>
        <module>csf3-agent</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinaunicom.csf</groupId>
            <artifactId>csf3-scope-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.4</version>
            </plugin>
        </plugins>
    </build>

</project>
