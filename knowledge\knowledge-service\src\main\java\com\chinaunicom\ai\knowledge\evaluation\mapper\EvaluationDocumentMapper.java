package com.chinaunicom.ai.knowledge.evaluation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评测文档Mapper接口
 */
@Mapper
public interface EvaluationDocumentMapper extends BaseMapper<EvaluationDocument> {
    
    /**
     * 根据知识库ID查询所有评测文档
     */
    List<EvaluationDocument> selectByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 根据原始文档ID查询
     */
    EvaluationDocument selectByOriginalDocId(@Param("originalDocId") String originalDocId);
    
    /**
     * 根据知识库文档ID查询
     */
    EvaluationDocument selectByKnowledgeDocId(@Param("knowledgeDocId") Long knowledgeDocId);
    
    /**
     * 统计指定知识库的文档数量
     */
    Integer countByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);
    
    /**
     * 根据状态查询文档列表
     */
    List<EvaluationDocument> selectByStatus(@Param("status") String status, @Param("knowledgeBaseId") Long knowledgeBaseId);
}
