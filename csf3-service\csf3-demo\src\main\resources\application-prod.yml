#服务器端口
server:
  port: 8200

#数据源配置
#spring:
#  datasource:
#    url: ${csf.datasource.prod.url}
#    username: ${csf.datasource.prod.username}
#    password: ${csf.datasource.prod.password}

spring:
  #排除DruidDataSourceAutoConfigure
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源或者数据源组,默认值即为master
      primary: master
      datasource:
        master:
          url: ${csf.datasource.demo.master.url}
          username: ${csf.datasource.demo.master.username}
          password: ${csf.datasource.demo.master.password}
        slave:
          url: ${csf.datasource.demo.slave.url}
          username: ${csf.datasource.demo.slave.username}
          password: ${csf.datasource.demo.slave.password}
