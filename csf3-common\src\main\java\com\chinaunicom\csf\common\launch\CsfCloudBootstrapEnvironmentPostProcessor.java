package com.chinaunicom.csf.common.launch;

import com.chinaunicom.csf.common.constant.LauncherConstant;
import com.chinaunicom.csf.core.launch.constant.NacosConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.env.OriginTrackedMapPropertySource;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;

import java.util.HashMap;
import java.util.Map;

import static com.chinaunicom.csf.core.launch.CsfBootstrapContextEnvironmentPostProcessor.CSF_BOOTSTRAP_ENVIRONMENT_POST_PROCESSOR_ORDER;

public class CsfCloudBootstrapEnvironmentPostProcessor implements EnvironmentPostProcessor, Ordered {

	public final static String CSF_CLOUD_BOOTSTRAP_PROPERTY_SOURCE_NAME = "csf-cloud-bootstrap";

	@Override
	public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {

		// only process environments in a bootstrap context
		if (!environment.getPropertySources().contains("bootstrap")) {
			return;
		}

		String profile = environment.getProperty("csf.env");
		String appName = environment.getProperty("spring.application.name");

		Map<String, Object> csfCloudBootstrapProperties = new HashMap<>();
		csfCloudBootstrapProperties.put("spring.cloud.nacos.config.prefix", NacosConstant.NACOS_CONFIG_PREFIX);
		csfCloudBootstrapProperties.put("spring.cloud.nacos.config.file-extension", NacosConstant.NACOS_CONFIG_FORMAT);
		csfCloudBootstrapProperties.put("spring.cloud.alibaba.seata.tx-service-group", appName.concat(NacosConstant.NACOS_GROUP_SUFFIX));
		// 判断是否有nacos相关配置
		if (!environment.containsProperty("spring.cloud.nacos.discovery.server-addr")) {
			csfCloudBootstrapProperties.put("spring.cloud.nacos.discovery.server-addr", LauncherConstant.nacosAddr(profile));
		}

		if (!environment.containsProperty("spring.cloud.nacos.config.server-addr")) {
			csfCloudBootstrapProperties.put("spring.cloud.nacos.config.server-addr", LauncherConstant.nacosAddr(profile));
		}
		environment.getPropertySources().addLast(new OriginTrackedMapPropertySource(CSF_CLOUD_BOOTSTRAP_PROPERTY_SOURCE_NAME, csfCloudBootstrapProperties));
	}

	@Override
	public int getOrder() {
		return CSF_BOOTSTRAP_ENVIRONMENT_POST_PROCESSOR_ORDER + 1;
	}

}
