
package com.chinaunicom.csf.gateway.filter;

import com.alibaba.nacos.common.utils.StringUtils;
import com.chinaunicom.csf.core.jwt.JwtUtil;
import com.chinaunicom.csf.core.launch.constant.TokenConstant;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.chinaunicom.csf.gateway.props.AuthProperties;
import com.chinaunicom.csf.gateway.provider.AuthProvider;
import com.chinaunicom.csf.gateway.provider.ResponseProvider;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;

/**
 * 令牌认证
 */
@Slf4j
@Component
@AllArgsConstructor
public class AuthFilter implements GlobalFilter, Ordered {
	private final AuthProperties authProperties;
	private final ObjectMapper objectMapper;
	private final AntPathMatcher antPathMatcher = new AntPathMatcher();

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		long nowMillis = System.currentTimeMillis();
		Date now = new Date(nowMillis);
		String path = exchange.getRequest().getURI().getPath();
		if (isSkip(path) || isSkipOther(exchange, path) ) {
			return chain.filter(exchange);
		}
		ServerHttpResponse resp = exchange.getResponse();
		ServerHttpRequest req = exchange.getRequest();
		String headerToken = exchange.getRequest().getHeaders().getFirst(AuthProvider.AUTH_KEY);
		String paramToken = exchange.getRequest().getQueryParams().getFirst(AuthProvider.AUTH_KEY);
		if (StringUtils.isBlank(headerToken) && StringUtils.isBlank(paramToken)) {
			return unAuth(resp, "缺失令牌,鉴权失败");
		}
		String auth = StringUtils.isBlank(headerToken) ? paramToken : headerToken;
		String token = JwtUtil.getToken(auth);
		Claims claims = JwtUtil.parseJWT(token, JwtUtil.getJwtProperties().getState());
		if (token == null || claims == null) {
			return unAuth(resp, "请求未授权");
		}

		if (JwtUtil.getJwtProperties().getState()) {
			String tenantId = String.valueOf(claims.get(TokenConstant.TENANT_ID));
			String userId = String.valueOf(claims.get(TokenConstant.USER_ID));
			String source = String.valueOf(claims.get(TokenConstant.USER_TYPE));
			String accessToken = JwtUtil.getAccessToken(tenantId, userId, source, claims.getId());
			if (accessToken == null) {
				return unAuth(resp, "令牌已过期");
			}

			long expireMillis = claims.getExpiration().getTime() - claims.getNotBefore().getTime();
			// 开启自动续期时，更新过期时间
			if (JwtUtil.getJwtProperties().getAutoRenewal()) {
				JwtUtil.expireToken(tenantId, userId, source, claims.getId(), (int) expireMillis / 1000);
			}
			// 如果jwt过期，则移除旧令牌，并重新生成令牌
			if (now.after(claims.getExpiration())) {
				JwtUtil.removeAccessToken(tenantId, userId, source, claims.getId());

				String jti = UUID.randomUUID().toString();
				String newToken = createNewToken(claims, jti, now, expireMillis);

				JwtUtil.addAccessToken(tenantId, userId, source, jti, newToken, (int) expireMillis / 1000);
				resp.getHeaders().add(TokenConstant.HEADER_ACCESS_TOKEN, newToken);
				req = exchange.getRequest().mutate().header(TokenConstant.HEADER, TokenConstant.BEARER + " " + newToken).build();
			}
		}
		return chain.filter(exchange.mutate().request(req).build());
	}

	private String createNewToken(Claims claims, String jti, Date now, long expireMillis) {
		SignatureAlgorithm signatureAlgorithm = SignatureAlgorithm.HS256;
		byte[] apiKeySecretBytes = Base64.getDecoder().decode(getBase64Security());
		Key signingKey = new SecretKeySpec(apiKeySecretBytes, signatureAlgorithm.getJcaName());
		JwtBuilder builder = Jwts.builder().setHeaderParam("typ", "JWT").setClaims(claims).signWith(signingKey).setId(jti);
		Date exp = new Date(now.getTime() + expireMillis);
		builder.setExpiration(exp).setNotBefore(now);
		return builder.compact();
	}

	private String getBase64Security() {
		return Base64.getEncoder().encodeToString(JwtUtil.getJwtProperties().getSignKey().getBytes(StandardCharsets.UTF_8));
	}

	private boolean isSkip(String path) {
		return AuthProvider.getDefaultSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path))
			|| authProperties.getSkipUrl().stream().anyMatch(pattern -> antPathMatcher.match(pattern, path));
	}

	private Mono<Void> unAuth(ServerHttpResponse resp, String msg) {
		resp.setStatusCode(HttpStatus.UNAUTHORIZED);
		resp.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
		String result = "";
		try {
			result = objectMapper.writeValueAsString(ResponseProvider.unAuth(msg));
		} catch (JsonProcessingException e) {
			log.error(e.getMessage(), e);
		}
		DataBuffer buffer = resp.bufferFactory().wrap(result.getBytes(StandardCharsets.UTF_8));
		return resp.writeWith(Flux.just(buffer));
	}

	@Override
	public int getOrder() {
		return -100;
	}

	/**
	 * 特殊判断，是否允许访问
	 * 如
	 * @return
	 */
	private boolean isSkipOther(ServerWebExchange exchange, String path) {
		// 针对如： http://************:31181/basic-api/csf-plus-auth/v2/api-docs 等v2/api-docs的接口，判断请求源是否来着swagger页面，如果是则允许访问
		String swaggerReferer = exchange.getRequest().getHeaders().getFirst("Referer");
		if(StringUtils.isNotBlank(swaggerReferer)) {
			if(swaggerReferer.contains("/doc.html") && path.contains("/v3/api-docs")) {
				return true;
			}
		}
		return false;
	}

}
