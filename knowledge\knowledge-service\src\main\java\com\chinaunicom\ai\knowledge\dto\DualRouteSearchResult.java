package com.chinaunicom.ai.knowledge.dto;

import com.chinaunicom.ai.knowledge.vo.AgentSearchResultVO;
import lombok.Data;

import java.util.List;

/**
 * 双路检索结果封装类
 * 用于封装精确匹配和混合检索的结果
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-08
 */
@Data
public class DualRouteSearchResult {
    
    /**
     * 精确匹配检索结果
     */
    private List<AgentSearchResultVO> exactMatchResults;
    
    /**
     * 混合检索结果
     */
    private List<AgentSearchResultVO> hybridSearchResults;
    
    /**
     * 精确匹配是否成功执行
     */
    private boolean exactMatchSuccess;
    
    /**
     * 混合检索是否成功执行
     */
    private boolean hybridSearchSuccess;
    
    /**
     * 精确匹配执行时间（毫秒）
     */
    private long exactMatchTime;
    
    /**
     * 混合检索执行时间（毫秒）
     */
    private long hybridSearchTime;
    
    /**
     * 异常信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 是否有任何成功的检索结果
     * 
     * @return 如果至少有一路检索成功则返回true
     */
    public boolean hasAnySuccess() {
        return exactMatchSuccess || hybridSearchSuccess;
    }
    
    /**
     * 获取总的检索时间
     * 
     * @return 两路检索的最大时间（并行执行）
     */
    public long getTotalTime() {
        return Math.max(exactMatchTime, hybridSearchTime);
    }
}
