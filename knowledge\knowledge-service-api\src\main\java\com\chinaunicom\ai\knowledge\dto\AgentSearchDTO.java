package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.io.Serializable;
/**
 * 智能体向量搜索请求DTO
 */
@Data
@Schema(description = "智能体向量搜索请求DTO")
public class AgentSearchDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "查询向量", requiredMode = Schema.RequiredMode.REQUIRED, example = "什么是RAG？")
    private float[] queryVector;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long knowledgeBaseId;

    @Schema(description = "返回结果数量", example = "5")
    private Integer topK = 5;
}