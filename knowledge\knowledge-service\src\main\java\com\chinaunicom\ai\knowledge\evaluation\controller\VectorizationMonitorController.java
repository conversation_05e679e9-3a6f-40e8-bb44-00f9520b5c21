package com.chinaunicom.ai.knowledge.evaluation.controller;

import com.chinaunicom.ai.knowledge.evaluation.service.VectorizationTimeoutMonitor;
import com.chinaunicom.csf.core.tool.api.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

/**
 * 向量化监控管理接口
 * 
 * 提供向量化状态监控、超时检查等管理功能
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-07-09
 */
@Slf4j
@RestController
@RequestMapping("/vectorization-monitor")
@RequiredArgsConstructor
@Tag(name = "向量化监控管理", description = "向量化状态监控和超时处理接口")
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class VectorizationMonitorController {

    private final VectorizationTimeoutMonitor timeoutMonitor;

    /**
     * 获取向量化状态统计
     */
    @GetMapping("/status")
    @Operation(summary = "获取向量化状态统计", description = "获取当前系统中各种向量化状态的文档数量统计")
    public R<VectorizationTimeoutMonitor.VectorizationStatusStats> getVectorizationStatus() {
        try {
            VectorizationTimeoutMonitor.VectorizationStatusStats stats = timeoutMonitor.getVectorizationStatusStats();
            return R.data(stats);
        } catch (Exception e) {
            log.error("获取向量化状态统计失败", e);
            return R.fail("获取向量化状态统计失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发向量化超时检查
     */
    @PostMapping("/check-timeout")
    @Operation(summary = "手动触发向量化超时检查", description = "立即检查并处理超过30分钟仍在向量化的文档")
    public R<String> checkVectorizationTimeout() {
        try {
            log.info("手动触发向量化超时检查");
            timeoutMonitor.checkVectorizationTimeout();
            return R.success("向量化超时检查已完成");
        } catch (Exception e) {
            log.error("手动触发向量化超时检查失败", e);
            return R.fail("向量化超时检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定知识库的向量化超时文档
     */
    @PostMapping("/check-timeout/{knowledgeBaseId}")
    @Operation(summary = "检查指定知识库的向量化超时文档", description = "检查并处理指定知识库中超时的向量化文档")
    public R<String> checkKnowledgeBaseTimeout(
            @Parameter(description = "知识库ID", required = true)
            @PathVariable Long knowledgeBaseId) {
        try {
            log.info("检查知识库 {} 的向量化超时文档", knowledgeBaseId);
            int processedCount = timeoutMonitor.checkKnowledgeBaseVectorizationTimeout(knowledgeBaseId);
            return R.success("知识库 " + knowledgeBaseId + " 向量化超时检查完成，处理了 " + processedCount + " 个超时文档");
        } catch (Exception e) {
            log.error("检查知识库 {} 向量化超时文档失败", knowledgeBaseId, e);
            return R.fail("检查知识库向量化超时文档失败: " + e.getMessage());
        }
    }

    /**
     * 获取向量化服务健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取向量化服务健康状态", description = "检查向量化服务是否存在过载或异常情况")
    public R<VectorizationHealthStatus> getVectorizationHealth() {
        try {
            VectorizationTimeoutMonitor.VectorizationStatusStats stats = timeoutMonitor.getVectorizationStatusStats();
            
            VectorizationHealthStatus health = new VectorizationHealthStatus();
            health.setVectorizingCount(stats.getVectorizingCount());
            health.setTimeoutCount(stats.getTimeoutCount());
            
            // 判断健康状态
            if (stats.getVectorizingCount() > 20) {
                health.setStatus("OVERLOADED");
                health.setMessage("向量化服务过载：正在处理 " + stats.getVectorizingCount() + " 个文档（超过安全阈值20）");
                health.setHealthy(false);
            } else if (stats.getTimeoutCount() > 10) {
                health.setStatus("WARNING");
                health.setMessage("发现 " + stats.getTimeoutCount() + " 个超时文档，建议检查向量化服务状态");
                health.setHealthy(false);
            } else if (stats.getVectorizingCount() > 15) {
                health.setStatus("WARNING");
                health.setMessage("向量化队列较满：正在处理 " + stats.getVectorizingCount() + " 个文档，接近安全阈值");
                health.setHealthy(true);
            } else {
                health.setStatus("HEALTHY");
                health.setMessage("向量化服务运行正常");
                health.setHealthy(true);
            }
            
            return R.data(health);
        } catch (Exception e) {
            log.error("获取向量化服务健康状态失败", e);
            return R.fail("获取向量化服务健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 向量化健康状态
     */
    @lombok.Data
    public static class VectorizationHealthStatus {
        private boolean healthy;           // 是否健康
        private String status;            // 状态：HEALTHY, WARNING, OVERLOADED
        private String message;           // 状态描述
        private int vectorizingCount;     // 正在向量化的文档数量
        private int timeoutCount;         // 超时文档数量
    }
}
