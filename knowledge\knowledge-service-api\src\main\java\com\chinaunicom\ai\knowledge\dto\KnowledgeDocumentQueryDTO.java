package com.chinaunicom.ai.knowledge.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 知识文档查询请求DTO，支持分页和文件名模糊查询
 */
@Data
@Schema(description = "知识文档查询请求DTO")
public class KnowledgeDocumentQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long baseId;

    @Schema(description = "文件名关键词（模糊查询）", example = "报告")
    private String fileName;

    @NotNull(message = "页码不能为空")
    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer pageNum;

    @NotNull(message = "每页大小不能为空")
    @Schema(description = "每页大小", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer pageSize;
}