package com.chinaunicom.ai.knowledge.evaluation.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.io.Serializable;

/**
 * 端到端评测请求DTO
 * 
 * <AUTHOR> Knowledge Team
 * @since 2025-01-07
 */
@Data
@Schema(description = "端到端评测请求")
public class FullEvaluationRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 评测数据集路径
     * 默认使用1doc_QA.json
     */
    @Schema(description = "评测数据集路径", example = "1doc_QA.json")
    private String datasetPath = "1doc_QA.json";

    /**
     * 最大导入文档数量
     * 默认导入全部文档
     */
    @Min(value = 1, message = "最大文档数量必须大于0")
    @Schema(description = "最大导入文档数量", example = "100")
    private Integer maxDocuments;

    /**
     * 评测完成后是否清理知识库
     * 默认true，清理临时资源
     */
    @Schema(description = "评测完成后是否清理知识库", example = "true")
    private Boolean cleanupAfterTest = true;

    /**
     * 知识库名称前缀
     * 默认自动生成
     */
    @Size(max = 50, message = "知识库名称前缀不能超过50个字符")
    @Schema(description = "知识库名称前缀", example = "auto-eval")
    private String knowledgeBaseName;

    /**
     * 向量化超时时间（秒）
     * 默认600秒（10分钟）
     */
    @Min(value = 60, message = "向量化超时时间不能少于60秒")
    @Schema(description = "向量化超时时间（秒）", example = "600")
    private Integer vectorizationTimeoutSeconds = 600;

    /**
     * 向量化状态检查间隔（秒）
     * 默认5秒检查一次
     */
    @Min(value = 1, message = "状态检查间隔不能少于1秒")
    @Schema(description = "向量化状态检查间隔（秒）", example = "5")
    private Integer statusCheckIntervalSeconds = 5;

    /**
     * 向量模型ID
     * 如果不指定，使用默认的向量模型
     */
    @Schema(description = "向量模型ID", example = "1")
    private Long vectorModelId;

    /**
     * 是否启用详细日志
     * 默认false
     */
    @Schema(description = "是否启用详细日志", example = "false")
    private Boolean enableDetailedLogging = false;

    /**
     * 召回文档数量（topK）
     * 默认5个
     */
    @Min(value = 1, message = "召回文档数量必须大于0")
    @Schema(description = "召回文档数量（topK）", example = "5")
    private Integer topK = 5;

    /**
     * 是否使用新的召回率计算公式
     * true: 召回正确片段数/总片段数
     * false: 正确召回数/总问题数（原公式）
     */
    @Schema(description = "是否使用新的召回率计算公式", example = "true")
    private Boolean useNewRecallFormula = true;

    /**
     * 获取生成的知识库名称
     * 如果用户没有指定前缀，则自动生成
     */
    public String getGeneratedKnowledgeBaseName() {
        if (knowledgeBaseName != null && !knowledgeBaseName.trim().isEmpty()) {
            return knowledgeBaseName.trim() + "-" + System.currentTimeMillis();
        }
        return "auto-eval-" + System.currentTimeMillis();
    }

    /**
     * 验证请求参数
     */
    public void validate() {
        if (maxDocuments != null && maxDocuments <= 0) {
            throw new IllegalArgumentException("最大文档数量必须大于0");
        }
        
        if (vectorizationTimeoutSeconds != null && vectorizationTimeoutSeconds < 60) {
            throw new IllegalArgumentException("向量化超时时间不能少于60秒");
        }
        
        if (statusCheckIntervalSeconds != null && statusCheckIntervalSeconds < 1) {
            throw new IllegalArgumentException("状态检查间隔不能少于1秒");
        }
        
        if (topK != null && topK <= 0) {
            throw new IllegalArgumentException("召回文档数量必须大于0");
        }
        
        if (knowledgeBaseName != null && knowledgeBaseName.length() > 50) {
            throw new IllegalArgumentException("知识库名称前缀不能超过50个字符");
        }
    }

    /**
     * 获取实际的最大文档数量
     * 如果未指定，返回null表示导入全部
     */
    public Integer getEffectiveMaxDocuments() {
        return maxDocuments;
    }

    /**
     * 是否需要清理资源
     */
    public boolean shouldCleanup() {
        return cleanupAfterTest != null && cleanupAfterTest;
    }

    /**
     * 是否启用详细日志
     */
    public boolean isDetailedLoggingEnabled() {
        return enableDetailedLogging != null && enableDetailedLogging;
    }

    /**
     * 是否使用新召回率公式
     */
    public boolean shouldUseNewRecallFormula() {
        return useNewRecallFormula != null && useNewRecallFormula;
    }
}
