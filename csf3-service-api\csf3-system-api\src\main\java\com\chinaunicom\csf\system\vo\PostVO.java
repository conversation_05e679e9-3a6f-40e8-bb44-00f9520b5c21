
package com.chinaunicom.csf.system.vo;

import com.chinaunicom.csf.system.entity.Post;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位表视图实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(name = "PostVO对象", description = "岗位表")
public class PostVO extends Post {
	private static final long serialVersionUID = 1L;

	/**
	 * 岗位分类名
	 */
	private String categoryName;

	/**
	 * 租户名称
	 */
	private String tenantName;
}
