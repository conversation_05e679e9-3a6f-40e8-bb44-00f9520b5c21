package com.chinaunicom.ai.knowledge.evaluation.service;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument;
import com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper;
import com.chinaunicom.ai.knowledge.entity.KnowledgeDocument;
import com.chinaunicom.ai.knowledge.enums.DocumentStatusEnum;
import com.chinaunicom.ai.knowledge.mapper.KnowledgeDocumentMapper;
import com.chinaunicom.ai.knowledge.service.impl.PythonVectorizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 评测文件上传服务
 * 负责将文档内容上传到MinIO并触发向量化
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "evaluation.controller.enabled", havingValue = "true", matchIfMissing = false)
public class EvaluationFileUploader {
    
    private final AmazonS3 amazonS3;
    private final PythonVectorizationService pythonVectorizationService;
    private final EvaluationDocumentMapper evaluationDocumentMapper;
    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    
    @Value("${minio.bucket-name}")
    private String bucketName;
    
    /**
     * 上传文档到知识库
     */
    public boolean uploadDocument(EvaluationDocument document) {
        try {
            // 1. 上传文件到MinIO
            String fileUrl = uploadToMinio(document);
            if (fileUrl == null) {
                return false;
            }
            
            // 2. 创建知识库文档记录
            Long knowledgeDocId = createKnowledgeDocument(document, fileUrl);
            if (knowledgeDocId == null) {
                return false;
            }
            
            // 3. 更新评测文档记录
            document.setKnowledgeDocId(knowledgeDocId);
            document.setStatus("UPLOADED");
            document.setUpdateTime(LocalDateTime.now());
            evaluationDocumentMapper.updateById(document);
            
            // 4. 触发向量化处理
            triggerVectorization(document, knowledgeDocId);
            
            return true;
            
        } catch (Exception e) {
            log.error("上传文档失败: {}", document.getOriginalDocId(), e);
            
            // 更新状态为失败
            document.setStatus("FAILED");
            document.setUpdateTime(LocalDateTime.now());
            evaluationDocumentMapper.updateById(document);
            
            return false;
        }
    }
    
    /**
     * 上传文件到MinIO
     */
    private String uploadToMinio(EvaluationDocument document) {
        try {
            // 使用与现有系统兼容的路径格式
            String objectKey = document.getFileObjectId();
            
            byte[] contentBytes = document.getNews1().getBytes(StandardCharsets.UTF_8);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(contentBytes);
            
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(contentBytes.length);
            metadata.setContentType("text/plain; charset=utf-8");
            metadata.addUserMetadata("originalDocId", document.getOriginalDocId());
            metadata.addUserMetadata("knowledgeBaseId", document.getKnowledgeBaseId().toString());
            
            PutObjectRequest request = new PutObjectRequest(bucketName, objectKey, inputStream, metadata);
            amazonS3.putObject(request);
            
            // 使用与现有系统兼容的fileUrl格式
            String fileUrl = bucketName + "/" + objectKey;
            
            log.debug("文件上传成功: {} -> {}", document.getOriginalDocId(), fileUrl);
            return fileUrl;
            
        } catch (Exception e) {
            log.error("MinIO文件上传失败: {}", document.getOriginalDocId(), e);
            return null;
        }
    }
    
    /**
     * 创建知识库文档记录
     */
    private Long createKnowledgeDocument(EvaluationDocument document, String fileUrl) {
        try {
            // 创建知识库文档记录，参考FileUploadService的实现
            KnowledgeDocument knowledgeDoc = new KnowledgeDocument();
            knowledgeDoc.setBaseId(document.getKnowledgeBaseId());
            knowledgeDoc.setFileName(document.getFileName());
            knowledgeDoc.setFileType("txt"); // 固定为txt类型
            knowledgeDoc.setFileSize((long) document.getNews1().getBytes(StandardCharsets.UTF_8).length);
            knowledgeDoc.setFileObjectId(document.getFileObjectId());
            knowledgeDoc.setFileUrl(fileUrl);
            knowledgeDoc.setStatus(DocumentStatusEnum.UPLOADED.getCode());
            knowledgeDoc.setIsDeleted(0);
            knowledgeDoc.setCreateUser(1L); // 评测系统用户
            knowledgeDoc.setCreateTime(LocalDateTime.now());
            knowledgeDoc.setTenantId("000000"); // 使用默认租户ID

            // 插入数据库
            knowledgeDocumentMapper.insert(knowledgeDoc);

            log.debug("创建知识库文档记录成功: {} -> {}", document.getOriginalDocId(), knowledgeDoc.getId());
            return knowledgeDoc.getId();

        } catch (Exception e) {
            log.error("创建知识库文档记录失败: {}", document.getOriginalDocId(), e);
            return null;
        }
    }
    
    /**
     * 触发向量化处理
     */
    private void triggerVectorization(EvaluationDocument document, Long knowledgeDocId) {
        try {
            // 更新状态为向量化中
            document.setStatus("VECTORIZING");
            document.setUpdateTime(LocalDateTime.now());
            evaluationDocumentMapper.updateById(document);
            
            // 异步触发向量化
            pythonVectorizationService.triggerVectorization(
                    knowledgeDocId,
                    document.getKnowledgeBaseId(),
                    document.getFileObjectId(),
                    document.getFileName()
            );
            
            log.debug("触发向量化处理: {}", document.getOriginalDocId());
            
        } catch (Exception e) {
            log.error("触发向量化失败: {}", document.getOriginalDocId(), e);
            
            // 向量化失败，但文档已上传，状态保持为UPLOADED
            document.setStatus("VECTORIZE_FAILED");
            document.setUpdateTime(LocalDateTime.now());
            evaluationDocumentMapper.updateById(document);
        }
    }
}
