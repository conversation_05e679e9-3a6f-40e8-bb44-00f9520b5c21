<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinaunicom.ai.knowledge.evaluation.mapper.EvaluationDocumentMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.chinaunicom.ai.knowledge.evaluation.entity.EvaluationDocument">
        <id column="id" property="id" />
        <result column="original_doc_id" property="originalDocId" />
        <result column="knowledge_doc_id" property="knowledgeDocId" />
        <result column="knowledge_base_id" property="knowledgeBaseId" />
        <result column="event" property="event" />
        <result column="news1" property="news1" />
        <result column="questions" property="questions" />
        <result column="answers" property="answers" />
        <result column="file_name" property="fileName" />
        <result column="file_object_id" property="fileObjectId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据知识库ID查询所有评测文档 -->
    <select id="selectByKnowledgeBaseId" resultMap="BaseResultMap">
        SELECT * FROM evaluation_document
        WHERE knowledge_base_id = #{knowledgeBaseId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据原始文档ID查询 -->
    <select id="selectByOriginalDocId" resultMap="BaseResultMap">
        SELECT * FROM evaluation_document
        WHERE original_doc_id = #{originalDocId}
        LIMIT 1
    </select>

    <!-- 根据知识库文档ID查询 -->
    <select id="selectByKnowledgeDocId" resultMap="BaseResultMap">
        SELECT * FROM evaluation_document
        WHERE knowledge_doc_id = #{knowledgeDocId}
        LIMIT 1
    </select>

    <!-- 统计指定知识库的文档数量 -->
    <select id="countByKnowledgeBaseId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM evaluation_document
        WHERE knowledge_base_id = #{knowledgeBaseId}
    </select>

    <!-- 根据状态查询文档列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT * FROM evaluation_document
        WHERE status = #{status}
        <if test="knowledgeBaseId != null">
            AND knowledge_base_id = #{knowledgeBaseId}
        </if>
        ORDER BY create_time ASC
    </select>

</mapper>