
package com.example.demo.feign;

import com.chinaunicom.csf.core.tool.api.R;
import com.example.demo.entity.Notice;
import com.example.demo.mapper.NoticeMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Notice Feign
 *
 */
@Tag(name = "ExcludedTag")
@RestController
@AllArgsConstructor
public class NoticeClient implements INoticeClient {

	private NoticeMapper mapper;

	@Override
	@GetMapping(TOP)
	public R<List<Notice>> top(Integer number) {
		return R.data(mapper.topList(number));
	}

}
