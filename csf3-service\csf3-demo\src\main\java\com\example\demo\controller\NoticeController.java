
package com.example.demo.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinaunicom.csf.common.cache.CacheNames;
import com.chinaunicom.csf.core.boot.ctrl.CsfController;
import com.chinaunicom.csf.core.mp.support.Condition;
import com.chinaunicom.csf.core.mp.support.Query;
import com.chinaunicom.csf.core.secure.annotation.PreAuth;
import com.chinaunicom.csf.core.tool.api.R;
import com.chinaunicom.csf.core.tool.utils.Func;
import com.chinaunicom.csf.plugins.datasecurity.annotations.Audit;
import com.example.demo.entity.Notice;
import com.example.demo.service.INoticeService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 控制器
 */
@RestController
@RequestMapping("notice")
@AllArgsConstructor
@Tag(description = "用户博客", name = "博客接口")
public class NoticeController extends CsfController implements CacheNames {

	private INoticeService noticeService;

	/**
	 * 详情
	 */
	@PreAuth("hasRoles:usr")
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情",description = "传入notice")
	public R<Notice> detail(Notice notice) {
		Notice detail = noticeService.getOne(Condition.getQueryWrapper(notice));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@Parameters({
		@Parameter(name = "category", description = "公告类型",  in = ParameterIn.QUERY, schema = @Schema(type = "integer")),
		@Parameter(name = "title", description = "公告标题",  in = ParameterIn.QUERY, schema = @Schema(type = "string"))
	})
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页",description = "传入notice")
	public R<IPage<Notice>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> notice, Query query) {
		IPage<Notice> pages = noticeService.page(Condition.getPage(query), Condition.getQueryWrapper(notice, Notice.class));
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "新增",description = "传入notice")
	public R save(@RequestBody Notice notice) {
		return R.status(noticeService.save(notice));
	}

	/**
	 * 修改内容
	 */
	@PutMapping("/content/update")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "修改内容",description = "传入notice")
	@Audit
	public R updateContent(@RequestBody Notice notice) {
		return R.status(noticeService.updateContent(notice.getId(), notice.getContent()));
	}

	/**
	 * 修改
	 */
	@PutMapping("/update")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "修改",description = "传入notice")
	@Audit
	public R update(@RequestBody Notice notice) {
		return R.status(noticeService.updateById(notice));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "新增或修改",description = "传入notice")
	public R submit(@RequestBody Notice notice) {
		return R.status(noticeService.saveOrUpdate(notice));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "逻辑删除",description = "传入notice")
	public R remove(@Parameter(description = "主键集合") @RequestParam String ids) {
		boolean temp = noticeService.deleteLogic(Func.toLongList(ids));
		return R.status(temp);
	}

}
