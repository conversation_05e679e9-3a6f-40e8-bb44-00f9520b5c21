-- 评测文档表
CREATE TABLE `evaluation_document` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `original_doc_id` varchar(64) NOT NULL COMMENT '原始文档ID（来自JSON中的id字段）',
    `knowledge_doc_id` bigint DEFAULT NULL COMMENT '知识库文档ID（上传到知识库后的内部ID）',
    `knowledge_base_id` bigint NOT NULL COMMENT '知识库ID',
    `event` text COMMENT '事件描述',
    `news1` longtext COMMENT '新闻内容（用作文档内容）',
    `questions` text COMMENT '评测问题',
    `answers` text COMMENT '标准答案',
    `file_name` varchar(255) DEFAULT NULL COMMENT '上传的文件名',
    `file_object_id` varchar(64) DEFAULT NULL COMMENT '文件对象ID（MinIO中的对象ID）',
    `status` varchar(32) DEFAULT 'PENDING' COMMENT '文档状态：PENDING-待处理, UPLOADED-已上传, VECTORIZING-向量化中, VECTORIZED-已向量化, FAILED-失败',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_original_doc_id` (`original_doc_id`),
    KEY `idx_knowledge_base_id` (`knowledge_base_id`),
    KEY `idx_knowledge_doc_id` (`knowledge_doc_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测文档表';

-- 评测测试历史表
CREATE TABLE `evaluation_test_history` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `knowledge_base_id` bigint NOT NULL COMMENT '知识库ID',
    `question` text NOT NULL COMMENT '测试问题（来自questions字段）',
    `expected_doc_id` varchar(64) NOT NULL COMMENT '期望的文档ID（问题对应的原始文档ID）',
    `recall_count` int DEFAULT 0 COMMENT '召回的文档数量',
    `is_correct_recall` tinyint(1) DEFAULT 0 COMMENT '是否召回正确（是否包含期望的文档）',
    `recalled_doc_ids` json DEFAULT NULL COMMENT '召回的文档ID列表（JSON格式）',
    `similarity_scores` json DEFAULT NULL COMMENT '相似度分数列表（JSON格式）',
    `execution_time` bigint DEFAULT 0 COMMENT '执行时间（毫秒）',
    `status` varchar(32) DEFAULT 'SUCCESS' COMMENT '测试状态：SUCCESS-成功, FAILED-失败',
    `error_message` text COMMENT '错误信息（如果失败）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_knowledge_base_id` (`knowledge_base_id`),
    KEY `idx_expected_doc_id` (`expected_doc_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评测测试历史表';

-- 插入示例数据（可选）
-- INSERT INTO `evaluation_document` (`original_doc_id`, `knowledge_base_id`, `event`, `news1`, `questions`, `answers`, `file_name`, `status`) 
-- VALUES ('test_doc_001', 1, '测试事件', '测试新闻内容', '测试问题？', '测试答案', 'test_doc_001.txt', 'PENDING');
