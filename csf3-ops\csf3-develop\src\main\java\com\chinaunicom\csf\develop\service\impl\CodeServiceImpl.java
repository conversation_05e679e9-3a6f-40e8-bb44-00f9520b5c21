
package com.chinaunicom.csf.develop.service.impl;

import com.chinaunicom.csf.core.mp.base.BaseServiceImpl;
import com.chinaunicom.csf.develop.entity.Code;
import com.chinaunicom.csf.develop.mapper.CodeMapper;
import com.chinaunicom.csf.develop.service.ICodeService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 */
@Service
public class CodeServiceImpl extends BaseServiceImpl<CodeMapper, Code> implements ICodeService {

	@Override
	public boolean submit(Code code) {
		return this.saveOrUpdate(code);
	}

}
