# 端到端知识库评测系统使用指南

## 🎯 系统概述

端到端知识库评测系统提供了一个完整的评测流程，从动态创建知识库到最终的资源清理，实现了全自动化的检索质量评估。

### 核心特性

- ✅ **动态知识库创建**：每次评测创建独立的临时知识库
- ✅ **自动文档导入**：从评测数据集导入文档并上传到MinIO
- ✅ **向量化监控**：实时监控文档向量化进度，支持超时控制
- ✅ **新召回率公式**：支持片段级别的召回率计算
- ✅ **资源自动清理**：评测完成后可选择性清理临时资源
- ✅ **详细执行日志**：记录每个步骤的执行状态和耗时

## 🚀 快速开始

### 1. 基础评测

最简单的端到端评测，使用默认参数：

```bash
curl -X POST "http://localhost:10001/evaluation/run-full-evaluation" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 2. 自定义评测

指定评测参数：

```bash
curl -X POST "http://localhost:10001/evaluation/run-full-evaluation" \
  -H "Content-Type: application/json" \
  -d '{
    "maxDocuments": 50,
    "cleanupAfterTest": true,
    "knowledgeBaseName": "my-test",
    "vectorizationTimeoutSeconds": 300,
    "useNewRecallFormula": true
  }'
```

### 3. 保留知识库

评测完成后保留知识库用于进一步分析：

```bash
curl -X POST "http://localhost:10001/evaluation/run-full-evaluation" \
  -H "Content-Type: application/json" \
  -d '{
    "maxDocuments": 100,
    "cleanupAfterTest": false,
    "knowledgeBaseName": "analysis-kb",
    "enableDetailedLogging": true
  }'
```

## 📋 请求参数详解

### FullEvaluationRequest 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `datasetPath` | String | "1doc_QA.json" | 评测数据集路径（当前固定） |
| `maxDocuments` | Integer | null | 最大导入文档数量，null表示全部导入 |
| `cleanupAfterTest` | Boolean | true | 评测完成后是否清理知识库 |
| `knowledgeBaseName` | String | null | 知识库名称前缀，null表示自动生成 |
| `vectorizationTimeoutSeconds` | Integer | 600 | 向量化超时时间（秒） |
| `statusCheckIntervalSeconds` | Integer | 5 | 向量化状态检查间隔（秒） |
| `vectorModelId` | Long | null | 向量模型ID，null表示使用默认模型 |
| `enableDetailedLogging` | Boolean | false | 是否启用详细日志 |
| `topK` | Integer | 5 | 召回文档数量 |
| `useNewRecallFormula` | Boolean | true | 是否使用新的召回率计算公式 |

### 召回率计算公式

#### 原公式（传统）
```
召回率 = 正确召回的问题数 / 总问题数
```

#### 新公式（片段级别）
```
召回率 = 所有问题召回的正确片段数量之和 / 知识库总片段数
```

新公式更准确地反映了检索系统在片段级别的召回能力。

## 📊 响应结果详解

### FullEvaluationResult 结构

```json
{
  "knowledgeBaseInfo": {
    "id": 123,
    "name": "auto-eval-1704614400000",
    "indexName": "kb-tenant1-123",
    "createTime": "2025-01-07T10:30:00",
    "vectorModelId": 1,
    "vectorModelName": "m3e-base"
  },
  "documentImportStats": {
    "totalDocuments": 100,
    "successCount": 98,
    "failureCount": 2,
    "importDurationMs": 5000,
    "failureReasons": ["失败的文档ID: doc1, doc2"]
  },
  "vectorizationStats": {
    "totalSegments": 450,
    "vectorizedCount": 98,
    "vectorizationFailureCount": 2,
    "vectorizationDurationMs": 120000,
    "avgVectorizationTimePerDoc": 1224.5,
    "statusCheckCount": 24
  },
  "evaluationResult": {
    "knowledgeBaseId": 123,
    "totalQuestions": 100,
    "correctRecalls": 85,
    "recallRate": 0.189,
    "averageAccuracy": 0.67,
    "avgExecutionTime": 150.0,
    "testResults": [...]
  },
  "cleanupStatus": {
    "enabled": true,
    "knowledgeBaseDeleted": true,
    "esIndexDeleted": true,
    "documentsDeleted": true,
    "cleanupDurationMs": 2000
  },
  "totalDurationMs": 127000,
  "startTime": "2025-01-07T10:30:00",
  "endTime": "2025-01-07T10:32:07",
  "success": true,
  "executionSteps": [
    {
      "stepName": "CREATE_KNOWLEDGE_BASE",
      "status": "SUCCESS",
      "startTime": "2025-01-07T10:30:00",
      "endTime": "2025-01-07T10:30:02",
      "durationMs": 2000,
      "description": "创建临时知识库"
    }
  ]
}
```

## 🔧 辅助接口

### 1. 检查向量化状态

```bash
curl -X GET "http://localhost:10001/evaluation/vectorization-status?knowledgeBaseId=123"
```

响应：
```json
{
  "totalSegments": 450,
  "vectorizedCount": 98,
  "vectorizationFailureCount": 2,
  "vectorizationDurationMs": 120000
}
```

### 2. 检查知识库是否准备就绪

```bash
curl -X GET "http://localhost:10001/evaluation/knowledge-base-ready?knowledgeBaseId=123"
```

响应：
```json
{
  "code": 200,
  "success": true,
  "data": true,
  "msg": "操作成功"
}
```

## 📈 性能优化建议

### 1. 文档数量控制

对于大规模评测，建议分批进行：

```bash
# 小规模测试（快速验证）
"maxDocuments": 10

# 中等规模测试（平衡性能和准确性）
"maxDocuments": 100

# 大规模测试（完整评估）
"maxDocuments": 1000
```

### 2. 超时时间设置

根据文档数量调整超时时间：

```bash
# 小规模：5分钟
"vectorizationTimeoutSeconds": 300

# 中等规模：10分钟
"vectorizationTimeoutSeconds": 600

# 大规模：30分钟
"vectorizationTimeoutSeconds": 1800
```

### 3. 状态检查间隔

```bash
# 快速检查（适合小规模）
"statusCheckIntervalSeconds": 3

# 标准检查（推荐）
"statusCheckIntervalSeconds": 5

# 慢速检查（适合大规模）
"statusCheckIntervalSeconds": 10
```

## 🚨 错误处理

### 常见错误及解决方案

#### 1. 向量化超时

**错误信息**：`向量化监控超时`

**解决方案**：
- 增加 `vectorizationTimeoutSeconds` 参数
- 减少 `maxDocuments` 数量
- 检查Python向量化服务状态

#### 2. 知识库创建失败

**错误信息**：`创建临时知识库失败`

**解决方案**：
- 检查向量模型是否可用
- 确认Elasticsearch服务正常
- 验证租户权限

#### 3. 文档导入失败

**错误信息**：`导入评测文档失败`

**解决方案**：
- 检查MinIO服务状态
- 确认数据集文件存在
- 验证文件权限

## 🔍 监控和调试

### 1. 日志监控

关键日志关键词：
- `端到端评测` - 主流程日志
- `向量化监控` - 向量化进度
- `临时知识库` - 知识库操作
- `资源清理` - 清理操作

### 2. 性能指标

重要性能指标：
- **总耗时**：完整流程执行时间
- **向量化耗时**：文档向量化处理时间
- **召回率**：检索质量核心指标
- **平均准确率**：检索精确性指标

### 3. 资源使用

监控资源使用情况：
- **数据库连接**：评测期间的连接数
- **MinIO存储**：临时文件存储空间
- **Elasticsearch索引**：临时索引大小
- **内存使用**：向量化过程内存消耗

## 🎯 最佳实践

### 1. 评测策略

```bash
# 阶段1：快速验证（10个文档）
# 验证系统基本功能和配置

# 阶段2：中等规模测试（100个文档）
# 评估性能和稳定性

# 阶段3：完整评测（全部文档）
# 获得准确的质量评估结果
```

### 2. 资源管理

```bash
# 开发环境：保留知识库用于调试
"cleanupAfterTest": false

# 测试环境：自动清理节省资源
"cleanupAfterTest": true

# 生产环境：根据需要决定
"cleanupAfterTest": true
```

### 3. 结果分析

重点关注的指标：
- **召回率趋势**：不同规模下的召回率变化
- **准确率分布**：各个问题的准确率分布
- **性能瓶颈**：识别最耗时的步骤
- **失败模式**：分析失败原因和模式

## 📝 注意事项

1. **并发限制**：避免同时运行多个端到端评测
2. **资源清理**：确保失败时的资源清理机制
3. **数据一致性**：评测期间避免修改数据集
4. **权限验证**：确保有足够的权限创建和删除资源
5. **网络稳定性**：确保与各个服务的网络连接稳定
