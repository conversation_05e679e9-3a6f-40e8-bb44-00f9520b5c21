{"openapi": "3.0.1", "info": {"title": "端到端知识库评测API", "description": "提供完整的知识库评测流程：创建知识库 → 导入文档 → 向量化 → 评测 → 清理", "version": "1.0.0"}, "servers": [{"url": "http://localhost:10001", "description": "本地开发环境"}], "paths": {"/evaluation/run-full-evaluation": {"post": {"tags": ["端到端评测"], "summary": "端到端评测", "description": "创建临时知识库，导入数据，向量化，评测，清理资源的完整流程", "operationId": "runFullEvaluation", "parameters": [{"name": "Authorization", "in": "header", "description": "认证令牌", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "Csf-auth", "in": "header", "description": "CSF认证头", "required": false, "schema": {"type": "string", "default": ""}}], "requestBody": {"description": "端到端评测请求参数", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FullEvaluationRequest"}, "examples": {"基础评测": {"summary": "使用默认参数的基础评测", "value": {}}, "自定义评测": {"summary": "自定义参数的评测", "value": {"maxDocuments": 100, "cleanupAfterTest": true, "knowledgeBaseName": "my-test", "vectorizationTimeoutSeconds": 600, "useNewRecallFormula": true, "enableDetailedLogging": false}}, "保留知识库": {"summary": "评测完成后保留知识库", "value": {"maxDocuments": 50, "cleanupAfterTest": false, "knowledgeBaseName": "analysis-kb", "enableDetailedLogging": true}}}}}, "required": false}, "responses": {"200": {"description": "评测成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/FullEvaluationResult"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/evaluation/vectorization-status": {"get": {"tags": ["端到端评测"], "summary": "检查知识库向量化状态", "description": "检查指定知识库的向量化进度", "operationId": "getVectorizationStatus", "parameters": [{"name": "Authorization", "in": "header", "description": "认证令牌", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "Csf-auth", "in": "header", "description": "CSF认证头", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "knowledgeBaseId", "in": "query", "description": "知识库ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"$ref": "#/components/schemas/VectorizationStats"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/evaluation/knowledge-base-ready": {"get": {"tags": ["端到端评测"], "summary": "检查知识库是否准备就绪", "description": "检查知识库是否完成向量化并可以进行评测", "operationId": "isKnowledgeBaseReady", "parameters": [{"name": "Authorization", "in": "header", "description": "认证令牌", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "Csf-auth", "in": "header", "description": "CSF认证头", "required": false, "schema": {"type": "string", "default": ""}}, {"name": "knowledgeBaseId", "in": "query", "description": "知识库ID", "required": true, "schema": {"type": "integer", "format": "int64", "example": 123}}], "responses": {"200": {"description": "查询成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/ApiResponse"}, {"type": "object", "properties": {"data": {"type": "boolean", "description": "知识库是否准备就绪", "example": true}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码", "example": 200}, "success": {"type": "boolean", "description": "是否成功", "example": true}, "msg": {"type": "string", "description": "响应消息", "example": "操作成功"}, "data": {"type": "object", "description": "响应数据"}}}, "FullEvaluationRequest": {"type": "object", "description": "端到端评测请求", "properties": {"datasetPath": {"type": "string", "description": "评测数据集路径", "example": "1doc_QA.json", "default": "1doc_QA.json"}, "maxDocuments": {"type": "integer", "description": "最大导入文档数量", "example": 100, "minimum": 1}, "cleanupAfterTest": {"type": "boolean", "description": "评测完成后是否清理知识库", "example": true, "default": true}, "knowledgeBaseName": {"type": "string", "description": "知识库名称前缀", "example": "auto-eval", "maxLength": 50}, "vectorizationTimeoutSeconds": {"type": "integer", "description": "向量化超时时间（秒）", "example": 600, "default": 600, "minimum": 60}, "statusCheckIntervalSeconds": {"type": "integer", "description": "向量化状态检查间隔（秒）", "example": 5, "default": 5, "minimum": 1}, "vectorModelId": {"type": "integer", "format": "int64", "description": "向量模型ID", "example": 1}, "enableDetailedLogging": {"type": "boolean", "description": "是否启用详细日志", "example": false, "default": false}, "topK": {"type": "integer", "description": "召回文档数量（topK）", "example": 5, "default": 5, "minimum": 1}, "useNewRecallFormula": {"type": "boolean", "description": "是否使用新的召回率计算公式", "example": true, "default": true}}}, "FullEvaluationResult": {"type": "object", "description": "端到端评测结果", "properties": {"knowledgeBaseInfo": {"$ref": "#/components/schemas/KnowledgeBaseInfo"}, "documentImportStats": {"$ref": "#/components/schemas/DocumentImportStats"}, "vectorizationStats": {"$ref": "#/components/schemas/VectorizationStats"}, "evaluationResult": {"$ref": "#/components/schemas/EvaluationResult"}, "cleanupStatus": {"$ref": "#/components/schemas/CleanupStatus"}, "totalDurationMs": {"type": "integer", "format": "int64", "description": "整个流程的总耗时（毫秒）", "example": 127000}, "startTime": {"type": "string", "description": "流程开始时间", "example": "2025-01-07T10:30:00"}, "endTime": {"type": "string", "description": "流程结束时间", "example": "2025-01-07T10:32:07"}, "success": {"type": "boolean", "description": "是否成功完成", "example": true}, "errorMessage": {"type": "string", "description": "错误信息（如果失败）", "example": "向量化超时"}, "executionSteps": {"type": "array", "description": "详细的执行步骤日志", "items": {"$ref": "#/components/schemas/ExecutionStep"}}}}, "KnowledgeBaseInfo": {"type": "object", "description": "临时知识库信息", "properties": {"id": {"type": "integer", "format": "int64", "description": "知识库ID", "example": 123}, "name": {"type": "string", "description": "知识库名称", "example": "auto-eval-1704614400000"}, "indexName": {"type": "string", "description": "ES索引名称", "example": "kb-tenant1-123"}, "createTime": {"type": "string", "description": "创建时间", "example": "2025-01-07T10:30:00"}, "vectorModelId": {"type": "integer", "format": "int64", "description": "向量模型ID", "example": 1}, "vectorModelName": {"type": "string", "description": "向量模型名称", "example": "m3e-base"}}}, "DocumentImportStats": {"type": "object", "description": "文档导入统计", "properties": {"totalDocuments": {"type": "integer", "description": "总文档数量", "example": 100}, "successCount": {"type": "integer", "description": "成功导入数量", "example": 98}, "failureCount": {"type": "integer", "description": "失败数量", "example": 2}, "importDurationMs": {"type": "integer", "format": "int64", "description": "导入耗时（毫秒）", "example": 5000}, "failureReasons": {"type": "array", "description": "失败原因列表", "items": {"type": "string"}, "example": ["失败的文档ID: doc1, doc2"]}}}, "VectorizationStats": {"type": "object", "description": "向量化处理统计", "properties": {"totalSegments": {"type": "integer", "format": "int64", "description": "总片段数量", "example": 450}, "vectorizedCount": {"type": "integer", "description": "向量化成功数量", "example": 98}, "vectorizationFailureCount": {"type": "integer", "description": "向量化失败数量", "example": 2}, "vectorizationDurationMs": {"type": "integer", "format": "int64", "description": "向量化处理时间（毫秒）", "example": 120000}, "avgVectorizationTimePerDoc": {"type": "number", "format": "double", "description": "平均每个文档的向量化时间（毫秒）", "example": 1224.5}, "statusCheckCount": {"type": "integer", "description": "向量化状态检查次数", "example": 24}}}, "EvaluationResult": {"type": "object", "description": "评测结果", "properties": {"knowledgeBaseId": {"type": "integer", "format": "int64", "description": "知识库ID", "example": 123}, "totalQuestions": {"type": "integer", "description": "总问题数", "example": 100}, "correctRecalls": {"type": "integer", "description": "正确召回数", "example": 85}, "recallRate": {"type": "number", "format": "double", "description": "召回率", "example": 0.189}, "averageAccuracy": {"type": "number", "format": "double", "description": "平均准确率", "example": 0.67}, "avgExecutionTime": {"type": "number", "format": "double", "description": "平均执行时间（毫秒）", "example": 150.0}, "testResults": {"type": "array", "description": "测试结果列表", "items": {"$ref": "#/components/schemas/TestResult"}}, "startTime": {"type": "string", "description": "开始时间", "example": "2025-01-07T10:30:00"}, "endTime": {"type": "string", "description": "结束时间", "example": "2025-01-07T10:32:07"}, "totalDuration": {"type": "integer", "format": "int64", "description": "总耗时（毫秒）", "example": 127000}}}, "TestResult": {"type": "object", "description": "单个测试结果", "properties": {"question": {"type": "string", "description": "问题文本", "example": "什么是人工智能？"}, "expectedDocId": {"type": "string", "description": "期望的文档ID", "example": "doc1"}, "isCorrect": {"type": "boolean", "description": "是否召回正确", "example": true}, "accuracy": {"type": "number", "format": "double", "description": "准确率", "example": 0.5}, "recallCount": {"type": "integer", "description": "召回的文档数量", "example": 2}, "recalledDocIds": {"type": "array", "description": "召回的文档ID列表", "items": {"type": "string"}, "example": ["doc1", "doc2"]}, "similarityScores": {"type": "array", "description": "相似度分数列表", "items": {"type": "number", "format": "double"}, "example": [0.95, 0.78]}, "executionTime": {"type": "integer", "format": "int64", "description": "执行时间（毫秒）", "example": 120}}}, "CleanupStatus": {"type": "object", "description": "资源清理状态", "properties": {"enabled": {"type": "boolean", "description": "是否启用清理", "example": true}, "knowledgeBaseDeleted": {"type": "boolean", "description": "知识库删除状态", "example": true}, "esIndexDeleted": {"type": "boolean", "description": "ES索引删除状态", "example": true}, "documentsDeleted": {"type": "boolean", "description": "文档删除状态", "example": true}, "cleanupDurationMs": {"type": "integer", "format": "int64", "description": "清理耗时（毫秒）", "example": 2000}, "cleanupError": {"type": "string", "description": "清理错误信息", "example": "删除ES索引失败"}}}, "ExecutionStep": {"type": "object", "description": "执行步骤", "properties": {"stepName": {"type": "string", "description": "步骤名称", "example": "CREATE_KNOWLEDGE_BASE"}, "status": {"type": "string", "description": "步骤状态", "enum": ["SUCCESS", "FAILURE", "IN_PROGRESS"], "example": "SUCCESS"}, "startTime": {"type": "string", "description": "开始时间", "example": "2025-01-07T10:30:00"}, "endTime": {"type": "string", "description": "结束时间", "example": "2025-01-07T10:30:02"}, "durationMs": {"type": "integer", "format": "int64", "description": "耗时（毫秒）", "example": 2000}, "description": {"type": "string", "description": "步骤描述", "example": "创建临时知识库"}, "errorMessage": {"type": "string", "description": "错误信息", "example": "向量模型不存在"}, "additionalData": {"type": "object", "description": "额外数据", "additionalProperties": true}}}}}}